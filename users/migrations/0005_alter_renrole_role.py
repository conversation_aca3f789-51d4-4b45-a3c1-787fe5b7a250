# Generated by Django 3.2.25 on 2024-12-31 13:49

from django.db import migrations, models

from users.models import UserRoleChoices


def add_beneficiary_role(apps, schema_editor):
    RenRole = apps.get_model('users', 'RenRole')
    RenRole.objects.create(role=UserRoleChoices.BENEFICIARY)


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0004_remove_user_role'),
    ]

    operations = [
        migrations.AlterField(
            model_name='renrole',
            name='role',
            field=models.PositiveSmallIntegerField(choices=[(1, 'Admin'), (2, 'Project Managers'), (3, 'Customer Services'), (4, 'Negotiator'), (5, 'Beneficiary')], db_index=True, unique=True, verbose_name='Role'),
        ),
        migrations.RunPython(add_beneficiary_role),
    ]
