# Generated by Django 3.2 on 2024-08-27 15:36

from django.db import migrations, models
import django_extensions.db.fields


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="RenRole",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "role",
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, "Admin"),
                            (2, "Project Managers"),
                            (3, "Customer Services"),
                            (4, "Negotiator"),
                        ],
                        db_index=True,
                        unique=True,
                        verbose_name="Role",
                    ),
                ),
            ],
            options={
                "verbose_name": "REN Role",
                "verbose_name_plural": "REN Roles",
            },
        ),
        migrations.AddField(
            model_name="user",
            name="roles",
            field=models.ManyToManyField(
                blank=True, to="users.RenRole", verbose_name="ren roles"
            ),
        ),
    ]
