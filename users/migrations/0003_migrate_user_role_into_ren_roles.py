# Generated by Django 3.2 on 2024-08-27 15:38

from django.db import migrations


def migrate_user_role_into_ren_roles(apps, schema_editor):
    from users.models import UserRoleChoices

    User = apps.get_model("users", "User")
    RenRole = apps.get_model("users", "RenRole")
    users = User.objects.all()
    project_manager_role = RenRole.objects.get_or_create(
        role=UserRoleChoices.PROJECT_MANAGERS
    )[0]
    customer_service_role = RenRole.objects.get_or_create(
        role=UserRoleChoices.CUSTOMER_SERVICES
    )[0]
    negotiator_role = RenRole.objects.get_or_create(role=UserRoleChoices.NEGOTIATORS)[0]
    update_ren_roles_mapper = {
        UserRoleChoices.ADMIN.value: lambda _user: _user.roles.clear(),
        UserRoleChoices.PROJECT_MANAGERS.value: lambda _user: _user.roles.add(
            project_manager_role
        ),
        UserRoleChoices.CUSTOMER_SERVICES.value: lambda _user: _user.roles.add(
            customer_service_role
        ),
        UserRoleChoices.NEGOTIATORS.value: lambda _user: _user.roles.add(
            negotiator_role
        ),
    }
    for user in users:
        update_ren_roles_mapper[user.role](user)


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0002_auto_20240827_1536"),
    ]

    operations = [
        migrations.RunPython(migrate_user_role_into_ren_roles),
    ]
