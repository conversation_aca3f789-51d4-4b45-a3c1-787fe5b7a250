from django.db import models
from django.db.models import Q
from django.utils.functional import cached_property
from django.utils.translation import gettext_lazy as _
from django_extensions.db.models import TimeStampedModel
from gabbro.users.models import User as <PERSON><PERSON>broUser, UserManager


class UserRoleChoices(models.IntegerChoices):
    ADMIN = 1, _("Admin")
    PROJECT_MANAGERS = 2, _("Project Managers")
    CUSTOMER_SERVICES = 3, _("Customer Services")
    NEGOTIATORS = 4, _("Negotiator")
    BENEFICIARY = 5, _("Beneficiary")


class RenUserManager(UserManager):
    def create_user(self, external_key, email, phone, first_name, last_name, role=None):
        external_key = external_key
        email = email
        phone = phone

        user = self.model(
            external_key=external_key,
            email=self.normalize_email(email) if email else None,
            phone=phone,
            first_name=first_name,
            last_name=last_name,
        )
        user.save(using=self._db)
        user.roles.add(role)
        return user

    def create_superuser(self, email, password):
        user = self.model(
            email=self.normalize_email(email), is_superuser=True, is_staff=True
        )
        user.set_password(password)
        user.save(using=self._db)
        return user


class RenRole(TimeStampedModel):
    role = models.PositiveSmallIntegerField(
        choices=UserRoleChoices.choices,
        db_index=True,
        unique=True,
        verbose_name=_("Role"),
    )

    class Meta:
        verbose_name = _("REN Role")
        verbose_name_plural = _("REN Roles")

    def __str__(self):
        return self.get_role_display()


class User(GabbroUser):
    roles = models.ManyToManyField(RenRole, blank=True, verbose_name=_("ren roles"))
    objects = RenUserManager()

    def __str__(self):
        return f"{self.pk} - {self.first_name} {self.last_name}"

    @cached_property
    def is_project_manager(self):
        return self.roles.filter(role=UserRoleChoices.PROJECT_MANAGERS).exists()

    @cached_property
    def is_customer_service(self):
        return self.roles.filter(role=UserRoleChoices.CUSTOMER_SERVICES).exists()

    @cached_property
    def is_negotiator(self):
        return self.roles.filter(role=UserRoleChoices.NEGOTIATORS).exists()

    @cached_property
    def is_beneficiary(self):
        return self.roles.filter(role=UserRoleChoices.BENEFICIARY).exists()

    @cached_property
    def customer_service_total_tasks_count(self) -> int:
        """
        - Retrieve all customer service tasks
        """
        return self.tasks.count()

    @cached_property
    def customer_service_in_progress_tasks_count(self) -> int:
        """
        - Retrieve all customer service tasks that not rejected or finished
        """
        from orders.models.task import TaskStatus

        return self.tasks.exclude(
            Q(status=TaskStatus.REJECTED) | Q(status=TaskStatus.FINISHED)
        ).count()

    @cached_property
    def customer_service_pending_review_tasks_count(self) -> int:
        """
        - Retrieve all customer services pending_review tasks
        """
        from orders.models.task import TaskStatus

        return self.tasks.filter(status=TaskStatus.PENDING_REVIEW).count()

    @cached_property
    def customer_service_finished_tasks_count(self) -> int:
        """
        - Retrieve all customer service finished tasks
        """
        from orders.models.task import TaskStatus

        return self.tasks.filter(status=TaskStatus.FINISHED).count()

    @cached_property
    def customer_service_rejected_tasks_count(self) -> int:
        """
        - Retrieve all customer service Rejected tasks
        """
        from orders.models.task import TaskStatus

        return self.tasks.filter(status=TaskStatus.REJECTED).count()

    @cached_property
    def negotiator_total_tasks_count(self) -> int:
        """
        - Retrieve all negotiator tasks
        """
        return self.negotiator_tasks.count()

    @cached_property
    def negotiator_in_progress_tasks_count(self) -> int:
        """
        - Retrieve all negotiator tasks that not rejected or finished
        """
        from orders.models.task import TaskStatus

        return self.negotiator_tasks.exclude(
            Q(status=TaskStatus.REJECTED) | Q(status=TaskStatus.FINISHED)
        ).count()

    @cached_property
    def negotiator_finished_tasks_count(self) -> int:
        """
        - Retrieve all negotiator finished tasks
        """
        from orders.models.task import TaskStatus

        return self.negotiator_tasks.filter(status=TaskStatus.FINISHED).count()

    @cached_property
    def negotiator_rejected_tasks_count(self) -> int:
        """
        - Retrieve all negotiator Rejected tasks
        """
        from orders.models.task import TaskStatus

        return self.negotiator_tasks.filter(status=TaskStatus.REJECTED).count()
