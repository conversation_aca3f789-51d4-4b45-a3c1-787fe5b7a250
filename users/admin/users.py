from django.contrib import admin
from gabbro.users.admin import UserAdmin
from django.utils.translation import ugettext_lazy as _
from users.admin.forms.users import UserForm
from users.models import User, RenRole


class ExtendedUserAdmin(UserAdmin):
    list_display = (
        "pk",
        "external_key",
        "email",
        "phone",
        "first_name",
        "last_name",
        "project_manager",
        "customer_service",
        "negotiator",
    )
    list_filter = ("is_superuser", "is_staff", "roles__role")
    search_fields = [
        "email",
        "phone",
        "first_name",
        "last_name",
    ]

    def has_delete_permission(self, request, obj=None):
        return False

    def get_fields(self, request, obj=None):
        return super().get_fields(request, obj) + ("roles",)

    def get_form(self, request, obj=None, change=False, **kwargs):
        return UserForm

    @admin.display(description=_("Project Manager"))
    def project_manager(self, obj):
        return obj.is_project_manager

    @admin.display(description=_("Customer Service"))
    def customer_service(self, obj):
        return obj.is_customer_service

    @admin.display(description=_("Negotiator"))
    def negotiator(self, obj):
        return obj.is_negotiator


admin.site.register(User, ExtendedUserAdmin)
admin.site.register(RenRole)
