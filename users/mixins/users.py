from django.utils.translation import gettext_lazy as _
from gabbro.graphene import NotFound

from users.models import User, UserRoleChoices


class UserMixin:
    @staticmethod
    def get_user_if_exists(user_id: int, role: UserRoleChoices):
        user = User.objects.filter(id=user_id, roles__role=role).first()
        if not user:
            raise NotFound(
                reason={
                    "user_id": _("User with id %(user_id)s not found")
                    % {"user_id": user_id}
                }
            )
        return user
