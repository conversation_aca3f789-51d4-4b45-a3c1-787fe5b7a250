import graphene
from django.contrib.auth import get_user_model

from users.schema.object_types import UserType
from utils.graphene import authentication_required

User = get_user_model()


# Queries
class Query(graphene.ObjectType):
    user_details = graphene.Field(UserType)

    @staticmethod
    @authentication_required
    def resolve_user_details(root, info, **kwargs):
        return info.context.user
