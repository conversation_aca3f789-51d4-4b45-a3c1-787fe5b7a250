import graphene
from django.contrib.auth import get_user_model
from graphene_django import DjangoObjectType
from graphene_gis.scalars import JSONScalar

from users.models import RenRole

User = get_user_model()


class RenRoleType(DjangoObjectType):
    role = graphene.Field(JSONScalar)

    class Meta:
        model = RenRole
        fields = ["role"]

    def resolve_role(self: RenRole, info):
        return {"key": self.role, "display": self.get_role_display()}


class UserType(DjangoObjectType):
    roles = graphene.List(RenRoleType)

    class Meta:
        model = User
        fields = (
            "id",
            "first_name",
            "last_name",
            "email",
            "phone",
            "is_superuser",
            "is_staff",
            "avatar",
            "roles",
        )

    def resolve_roles(self: User, info):
        return self.roles.all() or []


class CustomerServiceType(DjangoObjectType):
    total_tasks_count = graphene.Int()
    in_progress_tasks_count = graphene.Int()
    finished_tasks_count = graphene.Int()
    pending_review_tasks_count = graphene.Int()
    rejected_tasks_count = graphene.Int()

    class Meta:
        model = User
        fields = (
            "id",
            "first_name",
            "last_name",
            "email",
        )

    def resolve_total_tasks_count(self: User, info):
        return self.customer_service_total_tasks_count

    def resolve_in_progress_tasks_count(self: User, info):
        return self.customer_service_in_progress_tasks_count

    def resolve_finished_tasks_count(self: User, info):
        return self.customer_service_finished_tasks_count

    def resolve_pending_review_tasks_count(self: User, info):
        return self.customer_service_pending_review_tasks_count

    def resolve_rejected_tasks_count(self: User, info):
        return self.customer_service_rejected_tasks_count


class NegotiatorType(DjangoObjectType):
    total_tasks_count = graphene.Int()
    in_progress_tasks_count = graphene.Int()
    finished_tasks_count = graphene.Int()
    pending_review_tasks_count = graphene.Int()
    rejected_tasks_count = graphene.Int()

    class Meta:
        model = User
        fields = (
            "id",
            "first_name",
            "last_name",
            "email",
        )

    def resolve_total_tasks_count(self: User, info):
        return self.negotiator_total_tasks_count

    def resolve_in_progress_tasks_count(self: User, info):
        return self.negotiator_in_progress_tasks_count

    def resolve_finished_tasks_count(self: User, info):
        return self.negotiator_finished_tasks_count

    def resolve_rejected_tasks_count(self: User, info):
        return self.negotiator_rejected_tasks_count


class UserListType(graphene.ObjectType):
    data = graphene.List(UserType)
    count = graphene.Int()


class CustomerServiceListType(graphene.ObjectType):
    data = graphene.List(CustomerServiceType)
    count = graphene.Int()


class NegotiatorListType(graphene.ObjectType):
    data = graphene.List(NegotiatorType)
    count = graphene.Int()


__all__ = ["UserType", "UserListType", "CustomerServiceListType", "NegotiatorListType"]
