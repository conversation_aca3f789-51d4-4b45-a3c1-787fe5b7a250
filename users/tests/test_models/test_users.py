from django.contrib.auth import get_user_model
from django.test import TestCase

from users.models import UserRoleChoices, RenRole
from utils.tests.factories import UserFactory, RenRoleFactory

User = get_user_model()


class UserBaseTestCase(TestCase):
    def setUp(self):
        pass


class UserModelTestCase(UserBaseTestCase):
    def setUp(self):
        super(UserModelTestCase, self).setUp()

    def test_create_user(self):
        role = RenRole.objects.get_or_create(role=UserRoleChoices.CUSTOMER_SERVICES)[0]
        print(RenRole.objects.last())
        user = User.objects.create_user(
            email="<EMAIL>",
            role=role,
            external_key=1,
            phone="1234567890",
            first_name="abdallah",
            last_name="nasir",
        )
        user.set_password("easy-password-2024")
        user.save()
        self.assertEqual(user.email, "<EMAIL>")
        self.assertEqual(user.is_active, True)
        self.assertListEqual(
            list(user.roles.values_list("role", flat=True)),
            [UserRoleChoices.CUSTOMER_SERVICES.value],
        )
        self.assertEqual(user.is_staff, False)
        self.assertEqual(user.is_superuser, False)
        self.assertEqual(user.phone, "1234567890")
        self.assertEqual(user.first_name, "abdallah")
        self.assertEqual(user.last_name, "nasir")

    def test_create_super_user(self):
        user = User.objects.create_superuser(
            email="<EMAIL>",
            password="easy-password-2024",
        )
        self.assertEqual(user.email, "<EMAIL>")
        self.assertEqual(user.is_active, True)
        self.assertFalse(user.roles.exists())
        self.assertEqual(user.is_staff, True)
        self.assertEqual(user.is_superuser, True)

    def test_user_str(self):
        user = UserFactory(
            email="<EMAIL>",
            roles=[RenRoleFactory(role=UserRoleChoices.CUSTOMER_SERVICES)],
            external_key=1,
            phone="1234567890",
            first_name="abdallah",
            last_name="nasir",
        )
        self.assertEqual(str(user), f"{user.pk} - {user.first_name} {user.last_name}")
