from django.contrib.admin import AdminSite
from django.contrib.auth.models import Group, Permission
from django.http import HttpRequest
from django.test import TestCase

from users.admin.groups import GroupAdmin
from utils.tests.factories import UserFactory


class MockRequest(HttpRequest):
    def __init__(self, user=None):
        super().__init__()
        self.user = user


class GroupAdminTest(TestCase):
    def setUp(self):
        self.site = AdminSite()
        self.admin_group = GroupAdmin(Group, self.site)
        self.user = UserFactory(is_superuser=True)
        self.request = MockRequest(user=self.user)
        self.group = Group.objects.create(name="Test Group")
        self.group.permissions.set(Permission.objects.all())

    def test_layer_admin_registration(self):
        self.assertIsNotNone(self.admin_group)

    def test_list_display(self):
        # Check if the list_display is correctly defined in the admin class
        expected_list_display = ("__str__",)
        self.assertEqual(self.admin_group.list_display, expected_list_display)

    def test_search_fields(self):
        # Check if the search fields are correctly defined
        expected_search_fields = ("name",)
        self.assertEqual(self.admin_group.search_fields, expected_search_fields)

    def test_queryset(self):
        # Test that the correct queryset is returned
        queryset = self.admin_group.get_queryset(self.request)
        self.assertIn(self.group, queryset)

    def test_has_view_permission(self):
        # Test the view permission logic
        self.assertTrue(self.admin_group.has_view_permission(self.request))

    def test_has_add_permission(self):
        # Test the add permission logic
        self.assertTrue(self.admin_group.has_add_permission(self.request))

    def test_has_change_permission(self):
        # Test the change permission logic
        self.assertTrue(self.admin_group.has_change_permission(self.request))

    def test_has_delete_permission(self):
        # Test the delete permission logic
        self.assertFalse(self.admin_group.has_delete_permission(self.request))

    def test_get_fields(self):
        self.assertListEqual(
            self.admin_group.get_fields(self.request),
            ["name", "permissions"],
        )
        self.assertListEqual(
            self.admin_group.get_fields(self.request, self.group),
            ["name", "permissions"],
        )
