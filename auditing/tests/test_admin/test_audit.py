from django.contrib.admin import AdminSite
from django.http import HttpRequest
from django.test import TestCase

from auditing.admin import AuditAdmin
from auditing.models import Audit, Actions
from utils.tests.factories import UserFactory


class MockRequest(HttpRequest):
    def __init__(self, user=None):
        super().__init__()
        self.user = user


class AuditAdminTest(TestCase):
    def setUp(self):
        self.site = AdminSite()
        self.audit_admin = AuditAdmin(Audit, self.site)
        self.user = UserFactory(is_superuser=True)
        self.request = MockRequest(user=self.user)
        self.audit = Audit.objects.create(
            content_object=self.user,
            created_by=self.user,
            action=Actions.EDIT,
            data={"foo": "bar"},
        )

    def test_audit_admin_registration(self):
        self.assertIsNotNone(self.audit_admin)

    def test_list_display(self):
        # Check if the list_display is correctly defined in the admin class
        expected_list_display = ["id", "__str__", "action", "created", "created_by"]
        self.assertEqual(self.audit_admin.list_display, expected_list_display)

    def test_search_fields(self):
        # Check if the search fields are correctly defined
        expected_search_fields = [
            "object_id",
            "created_by__first_name",
            "created_by__last_name",
            "created_by__phone",
        ]
        self.assertEqual(self.audit_admin.search_fields, expected_search_fields)

    def test_queryset(self):
        # Test that the correct queryset is returned
        queryset = self.audit_admin.get_queryset(self.request)
        self.assertIn(self.audit, queryset)

    def test_has_view_permission(self):
        # Test the view permission logic
        self.assertTrue(self.audit_admin.has_view_permission(self.request))

    def test_has_add_permission(self):
        # Test the add permission logic
        self.assertFalse(self.audit_admin.has_add_permission(self.request))

    def test_has_change_permission(self):
        # Test the change permission logic
        self.assertFalse(self.audit_admin.has_change_permission(self.request))

    def test_has_delete_permission(self):
        # Test the delete permission logic
        self.assertFalse(self.audit_admin.has_delete_permission(self.request))

    def test_get_fields(self):
        self.assertListEqual(
            self.audit_admin.get_fields(self.request),
            ["created_by", "action", "data"],
        )
        self.assertListEqual(
            self.audit_admin.get_fields(self.request, self.audit),
            ["created_by", "action", "data"],
        )
