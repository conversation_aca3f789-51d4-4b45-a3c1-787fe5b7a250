from django.test import TestCase
from django.utils.translation import activate

from auditing.models import Audit, Actions
from utils.tests.factories import UserFactory


class AudiTestCase(TestCase):
    def setUp(self):
        activate("en-us")
        self.user = UserFactory()
        self.audit = Audit.objects.create(
            content_object=self.user,
            created_by=self.user,
            action=Actions.EDIT,
            data={"foo": "bar"},
        )

    def test_audit_is_created(self):
        self.assertIsInstance(self.audit, Audit)

    def test_model_fields(self):
        fields = [
            "id",
            "created",
            "modified",
            "content_type",
            "object_id",
            "created_by",
            "action",
            "data",
        ]
        layer_field_names = [f.name for f in self.audit._meta.fields]
        self.assertListEqual(fields, layer_field_names)

    def test_string_representation(self):
        self.assertEqual(
            str(self.audit), f"{self.audit.content_type} | {self.audit.object_id}"
        )

    def test_verbose_names(self):
        verbose_name = self.audit._meta.verbose_name
        verbose_name_plural = self.audit._meta.verbose_name_plural
        self.assertEqual(verbose_name, "Audit")
        self.assertEqual(verbose_name_plural, "Audits")

    def test_delete_layer(self):
        self.audit.delete()

    def test_update_layer(self):
        self.audit.data["test"] = "test"
        self.audit.save(update_fields=["data"])
        self.audit.refresh_from_db()
        self.assertEqual(self.audit.data, {"foo": "bar", "test": "test"})
