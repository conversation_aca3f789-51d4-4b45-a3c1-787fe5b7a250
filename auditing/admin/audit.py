from django.contrib import admin

from ..models import Audit


class AuditAdmin(admin.ModelAdmin):
    exclude = ["content_type", "object_id"]
    list_display = ["id", "__str__", "action", "created", "created_by"]
    search_fields = [
        "object_id",
        "created_by__first_name",
        "created_by__last_name",
        "created_by__phone",
    ]
    list_filter = ["content_type"]

    def has_delete_permission(self, request, obj=None):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request, obj=None):
        return False


admin.site.register(Audit, AuditAdmin)
