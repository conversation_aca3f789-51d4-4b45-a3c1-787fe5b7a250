from django.contrib.contenttypes.fields import GenericFore<PERSON><PERSON><PERSON>
from django.contrib.contenttypes.models import ContentType
from django.db import models
from django.utils.translation import ugettext_lazy as _
from django_extensions.db.models import TimeStampedModel
from jsoneditor.fields.django3_jsonfield import <PERSON><PERSON><PERSON><PERSON>


class Actions(models.IntegerChoices):
    ADD = (0, _("Add"))
    EDIT = (1, _("Edit"))
    DELETE = (2, _("Delete"))


class Audit(TimeStampedModel):
    content_type = models.ForeignKey(
        ContentType, on_delete=models.SET_NULL, blank=True, null=True
    )
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey("content_type", "object_id")
    created_by = models.ForeignKey(
        "users.User",
        on_delete=models.SET_NULL,
        null=True,
        verbose_name=_("Created by"),
    )
    action = models.PositiveSmallIntegerField(
        choices=Actions.choices, verbose_name=_("action"), db_index=True
    )
    data = J<PERSON><PERSON>ield(verbose_name=_("data"), null=True, blank=True)

    def __str__(self):
        return f"{self.content_type} | {self.object_id}"

    class Meta:
        verbose_name = _("Audit")
        verbose_name_plural = _("Audits")
