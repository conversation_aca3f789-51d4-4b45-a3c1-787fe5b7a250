from auditing.models import Actions, Audit


class AuditMixin:
    @staticmethod
    def audit_for_object_creation(content_object, user, data=None):
        audit_data = {
            "content_object": content_object,
            "action": Actions.ADD.value,
            "created_by": user,
            "data": data,
        }
        Audit.objects.create(**audit_data)

    @staticmethod
    def audit_for_object_updating(content_object, user, data=None):
        audit_data = {
            "content_object": content_object,
            "action": Actions.EDIT.value,
            "created_by": user,
            "data": data,
        }
        Audit.objects.create(**audit_data)

    @staticmethod
    def audit_for_object_deleting(object_id, user, data=None, content_object=None):
        audit_data = {
            "content_object": content_object,
            "object_id": object_id,
            "action": Actions.DELETE.value,
            "created_by": user,
            "data": data,
        }
        audit_data.pop("content_object") if content_object is None else None
        Audit.objects.create(**audit_data)
