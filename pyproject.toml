[tool.black]
line-length = 88
target-version = ['py36', 'py37']
include = '\.pyi?$'
exclude = '''
(
  /(
      \.eggs         # exclude a few common directories in the
    | \.git          # root of the project
    | \.hg
    | \.mypy_cache
    | \.tox
    | \.venv
    | _build
    | buck-out
    | build
    | dist
    | migrations
    | __pycache__
    | manage.py,
    | zoho,
    | venv,
    | \.venv,
    | \.env,
    | env,
  )/
  | foo.py           # also separately exclude a file named foo.py in
                     # the root of the project
)
'''
