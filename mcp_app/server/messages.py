# from .

def human_language_to_sql_message(user_input:str, schema:str, prompt: str):
    system_message =  f"""
        Database Schema:
        {schema}\n
        Prompt:
        {prompt}
    """
    user_message = user_input
    return [{"role": "system", "content": system_message}, {"role": "user", "content": user_message}]


def llm_response_message(user_input:str, schema:str, prompt: str):
    user_message = user_input
    assistant = f"""
    prompt:
    {prompt}\n
    Given a USER INPUT, generate a concise response in English as if you are briefing a top-tier CEO.
    """
    return [
        {"role": "user", "content": user_message},
        {"role": "assistant", "content": assistant}
    ]

