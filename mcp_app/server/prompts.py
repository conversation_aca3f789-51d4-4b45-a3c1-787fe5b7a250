

def ren_sql_prompts():
    return (
        "You have access to the database schema only.\n"
        "Do not access real data. Do not assume values.\n",
        "Generate a safe SQL SELECT query (do not run it)\n",
        "you have access to view only not to take action in database\n",
        "if user asks for any update or delete action return empty SQL \n",
        "dont explain the query just return empty SQL if the operation is failed of restricted \n",
        "Never show or imagine database contents.\n",
        "You are SQL generator so, if you cant generate sql return empty string.\n",
        "Your Answer will be executed Directly to the database so make sure the result will be pure SQL response.\n",
        'tasks form_data schema: {"type":"object","allOf":[{"if":{"properties":{"contact":{"properties":{"callStatus":{"const":"تم الرد"},"willingnessToContinueProgram":{"const":"يرغب في استكمال البرنامج"}}}}},"then":{"allOf":[{"properties":{"benificiaryServiceType":{"$ref":"#/definitions/benificiaryServiceType"}}},{"if":{"properties":{"benificiaryServiceType":{"properties":{"serviceType":{"const":"تمويل عقاري"}}}}},"else":{"properties":{"request":{"$ref":"#/definitions/request"},"location":{"$ref":"#/definitions/location"},"familyData":{"$ref":"#/definitions/familyData"},"financialData":{"$ref":"#/definitions/financialData"},"enthusiasmLevel":{"$ref":"#/definitions/enthusiasmLevel"},"supportPackages":{"$ref":"#/definitions/supportPackages"},"professionalData":{"$ref":"#/definitions/professionalData"},"locationPreferences":{"$ref":"#/definitions/locationPreferences"},"basicTransactionData":{"$ref":"#/definitions/basicTransactionData"},"financialPreferences":{"$ref":"#/definitions/financialPreferences"},"negotiatorContactTime":{"$ref":"#/definitions/negotiatorContactTime"},"realEstatePreferences":{"$ref":"#/definitions/realEstatePreferences"},"benificiaryServiceType":{"$ref":"#/definitions/benificiaryServiceType"},"personalRealEstateData":{"$ref":"#/definitions/personalRealEstateData"}}},"then":{"properties":{"request":{"$ref":"#/definitions/request"},"location":{"$ref":"#/definitions/location"},"familyData":{"$ref":"#/definitions/familyData"},"financialData":{"$ref":"#/definitions/financialData"},"enthusiasmLevel":{"$ref":"#/definitions/enthusiasmLevel"},"professionalData":{"$ref":"#/definitions/professionalData"},"basicTransactionData":{"$ref":"#/definitions/basicTransactionData"},"negotiatorContactTime":{"$ref":"#/definitions/negotiatorContactTime"},"benificiaryServiceType":{"$ref":"#/definitions/benificiaryServiceType"}}}}]}},{"if":{"properties":{"contact":{"properties":{"callStatus":{"const":"تم الرد"},"willingnessToContinueProgram":{"const":"لا يرغب في استكمال البرنامج"}}}}},"then":{"properties":{"contact":{"if":{"required":["reason"],"properties":{"reason":{"const":"أخرى"}}},"then":{"required":["comment"],"properties":{"comment":{"type":"string","title":"إضافة تعليق"}}},"required":["reason"],"properties":{"reason":{"enum":["عدم وجود قدرة مالية","عدم الرغبة في الوقت الحالي","تم شراء عقار مسبقًا","عدم الاستقرار الوظيفي في الوقت الحالي","ارتفاع سعر الفائدة","تفضيلات شخصية للإيجار","أخرى"],"type":"string","title":"السبب","default":"عدم الرغبة في الوقت الحالي"}}}}}}],"$schema":"http://json-schema.org/draft-07/schema#","properties":{"contact":{"$ref":"#/definitions/contact"},"personalData":{"$ref":"#/definitions/personalData"}},"definitions":{"contact":{"type":"object","allOf":[{"if":{"properties":{"callStatus":{"const":"تم الرد"}}},"else":{"properties":{"expectedCall":{"$ref":"#/definitions/expectedCall"}}},"then":{"properties":{"willingnessToContinueProgram":{"enum":["يرغب في استكمال البرنامج","لا يرغب في استكمال البرنامج","إعادة جدولة الإتصال"],"type":"string","title":"هل يرغب المستفيد في استكمال البرنامج؟","default":"يرغب في استكمال البرنامج"}}}},{"if":{"properties":{"willingnessToContinueProgram":{"const":"إعادة جدولة الإتصال"}}},"then":{"properties":{"expectedCall":{"$ref":"#/definitions/expectedCall"}}}}],"title":"التواصل","required":["callStatus"],"properties":{"callStatus":{"enum":["تم الرد","لم يتم الرد"],"type":"string","title":"حالة الاتصال","default":"تم الرد"}}},"request":{"type":"object","title":"الطلب","required":["registrationDate"],"properties":{"registrationDate":{"type":"string","title":"تاريخ التسجيل","format":"date"}}},"location":{"type":"object","title":"موقع السكن الحالي","required":["region","city"],"properties":{"city":{"type":"string","title":"المدينة"},"region":{"type":"string","title":"المنطقة"}}},"familyData":{"type":"object","title":"بيانات الأسرة","required":["familyMembersCount"],"properties":{"familyMembersCount":{"type":"integer","title":"عدد افراد الاسرة","minimum":0}}},"expectedCall":{"type":"object","title":"إعادة جدولة الإتصال","required":["expectedCallDate","expectedCallTime"],"properties":{"expectedCallDate":{"type":"string","title":"تاريخ الاتصال المتوقع","format":"date"},"expectedCallTime":{"enum":["08:00 صباحًا - 10:00 صباحًا","10:00 صباحًا - 12:00 مساءًا","12:00 مساءًا - 02:00 مساءًا","02:00 مساءًا - 04:00 مساءًا","04:00 مساءًا - 06:00 مساءًا","06:00 مساءًا - 08:00 مساءًا"],"type":"string","title":"وقت الاتصال المتوقع"}}},"personalData":{"type":"object","title":"البيانات الشخصية","required":["name","ageHijri"],"properties":{"name":{"type":"string","title":"الاسم"},"ageHijri":{"type":"integer","title":"العمر (هجري)","minimum":18},"mobileNumber":{"type":"string","title":"رقم الجوال","readOnly":true}}},"financialData":{"type":"object","title":"البيانات  الشخصية المالية","required":["salary","salaryBank","installmentChoice","monthlyDeduction","remainingDurationMonths"],"properties":{"salary":{"type":"number","title":"الراتب","minimum":0},"salaryBank":{"enum":["البنك الأهلي السعودي","مصرف الراجحي","بنك الرياض","بنك ساب","البنك العربي الوطني","مصرف الانماء","البنك السعودي للاستثمار","البنك السعودي الفرنسي","بنك الجزيرة","بنك البلاد","بنك الخليج الدولي","بنك الامارات","بنك أبو ظبي الأول"],"type":"string","title":"بنك الراتب"},"monthlyDeduction":{"type":"number","title":"الاستقطاع الشهري","default":0,"minimum":0},"installmentChoice":{"type":"object","title":"installmentChoice","default":{},"required":["annualPercentage","profitMargin","totalAmountWithProfits","fundingAmount","fundingPeriod"],"properties":{"profitMargin":{"type":"number"},"fundingAmount":{"type":"number"},"fundingPeriod":{"type":"number"},"annualPercentage":{"type":"number"},"totalAmountWithProfits":{"type":"number"},"installmentAfterPersonalLoan":{"type":"number"},"installmentDuringPersonalLoan":{"type":"number"}}},"remainingDurationMonths":{"type":"number","title":"المدة المتبقية (شهر)","default":0,"minimum":0}}},"enthusiasmLevel":{"type":"object","title":"حماس المستفيد","required":["enthusiasm"],"properties":{"enthusiasm":{"enum":["100%","80%","60%","40%","20%","0%"],"type":"string","title":"مدى حماس المستفيد لإتمام عملية الشراء","default":"100%"}}},"supportPackages":{"type":"object","title":"باقات الدعم","required":["product"],"properties":{"product":{"enum":["البناء الذاتي","الوحدات الجاهزة","البيع على الخارطة"],"type":"string","title":"المنتج"}}},"professionalData":{"type":"object","title":"البيانات الشخصية المهنية","required":["job"],"properties":{"job":{"enum":["حكومي مدني","خاص","عسكري","عمل حر","متقاعد","مدني","موظف بنك"],"type":"string","title":"الوظيفة"}}},"locationPreferences":{"type":"object","title":"تحديد التفضيلات المكانية المستوى الأول","required":["preferredRegion","preferredCity"],"properties":{"district":{"type":"array","items":{"type":"object"},"title":"الحي المفضل","default":[],"maxItems":5,"minItems":1,"uniqueItems":true},"mainDivision":{"type":"array","items":{"anyOf":[{"type":"string"},{"type":"object"}]},"title":"التقسيم الرئيسي","default":[],"uniqueItems":true},"preferredCity":{"type":"string","title":"المدينة المفضلة"},"preferredRegion":{"type":"string","title":"المنطقة المفضلة"}}},"basicTransactionData":{"type":"object","title":"البيانات الأساسية للمعاملة","required":["assignmentNumber","customerServiceEmployeeName","assignmentDate","assignmentTime"],"properties":{"assignmentDate":{"type":"string","title":"تاريخ الاسناد","format":"date","readOnly":true},"assignmentTime":{"type":"string","title":"وقت الاسناد","format":"time","readOnly":true},"assignmentNumber":{"type":"string","title":"رقم الاسناد","readOnly":true},"customerServiceEmployeeName":{"type":"string","title":"اسم موظف خدمة العملاء","readOnly":true}}},"financialPreferences":{"if":{"properties":{"purchaseMechanism":{"enum":["كاش","كلاهما"]}}},"then":{"required":["availableAmount"],"properties":{"availableAmount":{"type":"number","title":"مبلغ الكاش المتوفر","minimum":0}}},"type":"object","title":"تحديد التفضيلات المالية المستوى الأول","required":["purchaseMechanism","purchaseDesire","preferredPurchaseAmount"],"properties":{"purchaseDesire":{"enum":["الحد الأعلى للتمويل","الحد الأدنى للتمويل"],"type":"string","title":"رغبة الشراء"},"purchaseMechanism":{"enum":["تمويل","كاش","كلاهما"],"type":"string","title":"آلية الشراء","default":""},"preferredPurchaseAmount":{"type":"number","title":"مبلغ الشراء المفضل","minimum":0}}},"negotiatorContactTime":{"type":"object","title":"تحديد وقت تواصل المفاوض مع المستفيد","required":["contactDate","contactTime"],"properties":{"contactDate":{"type":"string","title":"تاريخ التواصل","format":"date"},"contactTime":{"enum":["08:00 صباحًا - 10:00 صباحًا","10:00 صباحًا - 12:00 مساءًا","12:00 مساءًا - 02:00 مساءًا","02:00 مساءًا - 04:00 مساءًا","04:00 مساءًا - 06:00 مساءًا","06:00 مساءًا - 08:00 مساءًا"],"type":"string","title":"وقت التواصل"}}},"realEstatePreferences":{"type":"object","title":"تحديد التفضيلات العقارية المستوى الأول","required":["preferredPropertyType"],"properties":{"preferredPropertyType":{"enum":["أرض","فيلا","فيلا دوبلكس","عمارة","برج","مزرعة","استراحة","مكتب","محطة","أخرى","شقة","دور","ملحق","غرفة","محل","معرض","مستودع","ورشة"],"type":"string","title":"نوع العقار المفضل"}}},"benificiaryServiceType":{"type":"object","title":"نوع الخدمة المقدمة","required":["serviceType"],"properties":{"serviceType":{"enum":["بحث عن عقار","تمويل عقاري"],"type":"string","title":"ما نوع الخدمة التي ترغب فيها ؟","default":"بحث عن عقار"}}},"personalRealEstateData":{"type":"object","title":"البيانات الشخصية العقارية","required":["hasLand","hasBuildingLicense","startedBuilding"],"properties":{"hasLand":{"enum":["نعم","لا"],"type":"string","title":"لديه ارض"},"startedBuilding":{"enum":["نعم","لا"],"type":"string","title":"بدأ بالبناء"},"hasBuildingLicense":{"enum":["نعم","لا"],"type":"string","title":"لديه رخصة بناء"}}}}}.\n'
        'i will give you a sample of task form_data: {"negotiator":{"placeOrder":{"realEstatePreferences":{"preferredPurchaseAmount":"أرض"},"locationPreferencesLevelTwo":{"cityId":1,"district":[{"id":*********,"label":"الرمال"}],"regionId":********,"mainDivision":[{"id":1,"label":"جنوب"},{"id":2,"label":"شرق"},{"id":3,"label":"شمال"},{"id":4,"label":"غرب"}],"preferredCity":"الرياض","preferredRegion":"منطقة الرياض"},"financialPreferencesLevelTwo":{"maximumPurchaseAmount":1000000,"minimumPurchaseAmount":1000,"preferredPurchaseAmount":100000}},"negotiatorData":{"ID_CODE":null,"contact":{"reason":"عدم الرغبة في الوقت الحالي","callStatus":"تم الرد","expectedCall":{},"willingnessToContinueProgram":"يرغب في استكمال البرنامج"},"request":{"registrationDate":"2025-03-23"},"location":{"city":"الرياض","region":"منطقة الرياض"},"familyData":{"familyMembersCount":2},"destination":"مستفيد خارجي","personalData":{"name":"Adel Mabrouk","ageHijri":232,"mobileNumber":"*********"},"financialData":{"salary":15000,"salaryBank":"بنك الرياض","monthlyDeduction":1000,"installmentChoice":{"profitMargin":256667,"fundingAmount":733333,"fundingPeriod":10,"annualPercentage":3.5,"totalAmountWithProfits":990000,"installmentAfterPersonalLoan":8250,"installmentDuringPersonalLoan":0},"remainingDurationMonths":0},"enthusiasmLevel":{"enthusiasm":"100%"},"supportPackages":{"product":"الوحدات الجاهزة"},"professionalData":{"job":"عسكري"},"locationPreferences":{"cityId":1,"district":[{"id":*********,"label":"الرمال"}],"regionId":********,"mainDivision":[{"id":1,"label":"جنوب"},{"id":2,"label":"شرق"},{"id":3,"label":"شمال"},{"id":4,"label":"غرب"}],"preferredCity":"الرياض","preferredRegion":"منطقة الرياض"},"basicTransactionData":{"assignmentDate":"2025-03-24","assignmentTime":"11:28:34","assignmentNumber":"774","customerServiceEmployeeName":"Dev Team"},"financialPreferences":{"purchaseDesire":"الحد الأعلى للتمويل","availableAmount":100,"purchaseMechanism":"كاش","preferredPurchaseAmount":100000},"negotiatorContactTime":{"contactDate":"2025-03-24","contactTime":"08:00 صباحًا - 10:00 صباحًا"},"realEstatePreferences":{"preferredPropertyType":"أرض"},"benificiaryServiceType":{"serviceType":"بحث عن عقار"},"personalRealEstateData":{"hasLand":"نعم","startedBuilding":"لا","hasBuildingLicense":"نعم"},"رقم التقنيات":null}},"customer_service":{"beneficiaryData":{"ID_CODE":null,"contact":{"reason":"عدم الرغبة في الوقت الحالي","callStatus":"تم الرد","expectedCall":{},"willingnessToContinueProgram":"يرغب في استكمال البرنامج"},"request":{"registrationDate":"2025-03-23"},"location":{"city":"الرياض","region":"منطقة الرياض"},"familyData":{"familyMembersCount":2},"destination":"مستفيد خارجي","personalData":{"name":"Adel Mabrouk","ageHijri":232,"mobileNumber":"*********"},"financialData":{"salary":15000,"salaryBank":"بنك الرياض","monthlyDeduction":1000,"installmentChoice":{"profitMargin":256667,"fundingAmount":733333,"fundingPeriod":10,"annualPercentage":3.5,"totalAmountWithProfits":990000,"installmentAfterPersonalLoan":8250,"installmentDuringPersonalLoan":0},"remainingDurationMonths":0},"enthusiasmLevel":{"enthusiasm":"100%"},"supportPackages":{"product":"الوحدات الجاهزة"},"professionalData":{"job":"عسكري"},"locationPreferences":{"cityId":1,"district":[{"id":*********,"label":"الرمال"}],"regionId":********,"mainDivision":[{"id":1,"label":"جنوب"},{"id":2,"label":"شرق"},{"id":3,"label":"شمال"},{"id":4,"label":"غرب"}],"preferredCity":"الرياض","preferredRegion":"منطقة الرياض"},"basicTransactionData":{"assignmentDate":"2025-03-24","assignmentTime":"11:28:34","assignmentNumber":"774","customerServiceEmployeeName":"Dev Team"},"financialPreferences":{"purchaseDesire":"الحد الأعلى للتمويل","availableAmount":100,"purchaseMechanism":"كاش","preferredPurchaseAmount":100000},"negotiatorContactTime":{"contactDate":"2025-03-24","contactTime":"08:00 صباحًا - 10:00 صباحًا"},"realEstatePreferences":{"preferredPropertyType":"أرض"},"benificiaryServiceType":{"serviceType":"بحث عن عقار"},"personalRealEstateData":{"hasLand":"نعم","startedBuilding":"لا","hasBuildingLicense":"نعم"},"رقم التقنيات":null}}}\n',
        'i store tasks customer service data inside tasks.form_data.customer_service\n',
        'i store tasks negotiator data inside tasks.form_data.negotiator\n',
        "regarding tasks form_data: always look for negotiator key first you will find updated data there\n",
        "if you didnt find negotiator, look for customer_service key\n"
        "dont return all table fields unless user asked\n",
        "return only id field as default value\n",
    )


def ren_summarize_sql_result(sql_query: str, query_result: list[dict]):
    result_text = str(query_result)
    return (
    "Given the following SQL query and its result, summarize the answer in natural human language.\n",
    f"SQL Query: {sql_query}. \n",
    f"Result: {result_text} \n",
    "Please answer naturally, not like a database table."
    "return results as string and avoid using words like [SQL, Table, Result] \n",
    "return result as user friendly string \n",
    "if you should return list of results, then return it as steps \n",
    "use numbers or dots like [., 1] to list results if needed \n"
    )
