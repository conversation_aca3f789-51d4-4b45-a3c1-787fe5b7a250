
import logging

import mcp
from databases import Database
from mcp import JSONRPCResponse

from connectors import ChatGPTConnector, DeepseekConnector

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


def llm_execution(messages: str) -> str:
    try:
        # ask ChatGpt First
        answer = ChatGPTConnector().complete(messages=messages)
        print("gpt")
    except Exception as e:
        # Ask Deepseek
        print("Deepseek")
        answer = DeepseekConnector().complete(messages=messages)
    except Exception as e:
        answer = f"Sorry, I couldn't answer your question. Please try again later."
    return answer


async def execute_sql(sql_query: str) -> list[dict]:
    """Execute a SQL query and return the result as rows."""
    rows = []
    try:
        database = Database("postgresql://abdallah:199712@localhost:5432/ren_dev")
        if not database.is_connected:
            await database.connect()

        rows = await database.fetch_all(query=sql_query)
    except Exception as e:
        logger.debug(
            f"execute_sql: {e}"
        )
    return [dict(row) for row in rows]


