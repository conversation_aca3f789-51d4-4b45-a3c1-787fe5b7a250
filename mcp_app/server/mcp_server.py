# server.py
import logging
import os
from dotenv import load_dotenv
from mcp.server.fastmcp import FastMCP
from messages import human_language_to_sql_message, llm_response_message
from prompts import ren_sql_prompts, ren_summarize_sql_result
from resources import ren_schema
from tools import llm_execution, execute_sql

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
load_dotenv()


mcp = FastMCP(name="Geotech", instructions=None,
    host="localhost",
    port=6274)


# Tools

@mcp.tool("talk_to_llm")
async def talk_to_llm(user_question: str) -> str:
    """Tool to ask Any LLM about the database using schema and prompt."""
    natural_language_to_sql = llm_execution(
        messages=human_language_to_sql_message(user_input=user_question, schema=get_db_schema(), prompt=sql_prompt())
    )
    logger.debug(
        f"natural_language_to_sql: {natural_language_to_sql}"
    )
    query_result = await execute_sql_query(sql_query=natural_language_to_sql)
    logger.debug(
        f"query_result: {query_result}"
    )
    response = llm_execution(
        messages=llm_response_message(
            user_input=user_question, schema=get_db_schema(), prompt=summarize_sql_result_prompt(sql_query=natural_language_to_sql, query_result=query_result),
        ),
     )
    logger.debug(
        f"response: {response}"
    )
    return response


@mcp.tool("execute_sql")
async def execute_sql_query(sql_query: str) -> list[dict]:
    """Tool to execute a SQL query and return the result as rows."""
    return await  execute_sql(sql_query)


# Resources

@mcp.resource("schema://main")
def get_db_schema() -> dict:
    return ren_schema()

# Prompts

@mcp.prompt("Answer database questions based on schema only.")
def sql_prompt() -> str:
    """Guide ChatGPT to only generate SQL or explanations."""
    return ren_sql_prompts()


@mcp.prompt("Summarize the SQL result into a natural language answer.")
def summarize_sql_result_prompt(sql_query: str, query_result: list[dict]) -> str:
    """Guide ChatGPT To Summarize the SQL result into a natural language answer."""
    return ren_summarize_sql_result(sql_query=sql_query, query_result=query_result)




if __name__ == "__main__":
    mcp.run(transport="stdio")
