import psycopg2


def ren_schema() -> dict:
    return {
  "tables": {
    "acl_role": {
      "columns": {
        "id": "bigint",
        "codename": "character varying",
        "title": "character varying"
      }
    },
    "acl_role_permissions": {
      "columns": {
        "id": "bigint",
        "role_id": "bigint",
        "permission_id": "integer"
      }
    },
    "acl_rule": {
      "columns": {
        "id": "bigint",
        "object_id": "integer",
        "content_type_id": "integer",
        "role_id": "bigint",
        "user_id": "bigint"
      }
    },
    "acl_rule_groups": {
      "columns": {
        "id": "bigint",
        "rule_id": "bigint",
        "group_id": "integer"
      }
    },
    "auditing_audit": {
      "columns": {
        "id": "bigint",
        "created": "timestamp with time zone",
        "modified": "timestamp with time zone",
        "object_id": "integer",
        "action": "smallint",
        "data": "jsonb",
        "content_type_id": "integer",
        "created_by_id": "bigint"
      }
    },
    "auth_group": {
      "columns": {
        "id": "integer",
        "name": "character varying"
      }
    },
    "auth_group_permissions": {
      "columns": {
        "id": "bigint",
        "group_id": "integer",
        "permission_id": "integer"
      }
    },
    "auth_permission": {
      "columns": {
        "id": "integer",
        "name": "character varying",
        "content_type_id": "integer",
        "codename": "character varying"
      }
    },
    "deals_agreement": {
      "columns": {
        "id": "bigint",
        "created": "timestamp with time zone",
        "modified": "timestamp with time zone",
        "status": "smallint",
        "form_data": "jsonb",
        "task_id": "bigint"
      }
    },
    "deals_beneficiary": {
      "columns": {
        "id": "bigint",
        "code": "character varying",
        "external_id": "integer",
        "name": "character varying",
        "data": "jsonb",
        "user_id": "bigint",
        "data_source": "character varying"
      }
    },
    "deals_deal": {
      "columns": {
        "id": "bigint",
        "created": "timestamp with time zone",
        "modified": "timestamp with time zone",
        "status": "smallint",
        "form_data": "jsonb",
        "favorite_offer_id": "bigint"
      }
    },
    "deals_externaloffer": {
      "columns": {
        "id": "bigint",
        "created": "timestamp with time zone",
        "modified": "timestamp with time zone",
        "external_offer_id": "bigint",
        "data": "jsonb",
        "status": "smallint"
      }
    },
    "deals_favoriteoffer": {
      "columns": {
        "id": "bigint",
        "created": "timestamp with time zone",
        "modified": "timestamp with time zone",
        "task_id": "bigint",
        "offer_id": "bigint",
        "reserved": "boolean"
      }
    },
    "deals_layer": {
      "columns": {
        "id": "bigint",
        "created": "timestamp with time zone",
        "modified": "timestamp with time zone",
        "key": "character varying",
        "title": "character varying",
        "description": "text",
        "metadata": "jsonb",
        "boundaries": "USER-DEFINED"
      }
    },
    "deals_offer": {
      "columns": {
        "id": "bigint",
        "created": "timestamp with time zone",
        "modified": "timestamp with time zone",
        "license_number": "character varying",
        "offer_type": "smallint",
        "status": "smallint",
        "data": "jsonb",
        "record_id": "bigint",
        "external_offer_id": "integer"
      }
    },
    "deals_offerrequest": {
      "columns": {
        "id": "bigint",
        "created": "timestamp with time zone",
        "modified": "timestamp with time zone",
        "status": "character varying",
        "data": "jsonb",
        "external_offer_id": "bigint",
        "internal_offer_id": "bigint",
        "request_id": "bigint"
      }
    },
    "deals_record": {
      "columns": {
        "id": "bigint",
        "created": "timestamp with time zone",
        "modified": "timestamp with time zone",
        "geometry": "USER-DEFINED",
        "source_properties": "jsonb",
        "layer_id": "bigint"
      }
    },
    "deals_request": {
      "columns": {
        "id": "bigint",
        "created": "timestamp with time zone",
        "modified": "timestamp with time zone",
        "status": "smallint",
        "data": "jsonb",
        "task_id": "bigint",
        "expired": "timestamp with time zone"
      }
    },
    "django_admin_log": {
      "columns": {
        "id": "integer",
        "action_time": "timestamp with time zone",
        "object_id": "text",
        "object_repr": "character varying",
        "action_flag": "smallint",
        "change_message": "text",
        "content_type_id": "integer",
        "user_id": "bigint"
      }
    },
    "django_content_type": {
      "columns": {
        "id": "integer",
        "app_label": "character varying",
        "model": "character varying"
      }
    },
    "django_migrations": {
      "columns": {
        "id": "bigint",
        "app": "character varying",
        "name": "character varying",
        "applied": "timestamp with time zone"
      }
    },
    "django_session": {
      "columns": {
        "session_key": "character varying",
        "session_data": "text",
        "expire_date": "timestamp with time zone"
      }
    },
    "formschemas_formschema": {
      "columns": {
        "id": "bigint",
        "created": "timestamp with time zone",
        "modified": "timestamp with time zone",
        "key": "character varying",
        "json_schema": "jsonb"
      }
    },
    "geography_columns": {
      "columns": {
        "f_table_catalog": "name",
        "f_table_schema": "name",
        "f_table_name": "name",
        "f_geography_column": "name",
        "coord_dimension": "integer",
        "srid": "integer",
        "type": "text"
      }
    },
    "geometry_columns": {
      "columns": {
        "f_table_catalog": "character varying",
        "f_table_schema": "name",
        "f_table_name": "name",
        "f_geometry_column": "name",
        "coord_dimension": "integer",
        "srid": "integer",
        "type": "character varying"
      }
    },
    "guardian_groupobjectpermission": {
      "columns": {
        "id": "integer",
        "object_pk": "character varying",
        "content_type_id": "integer",
        "group_id": "integer",
        "permission_id": "integer"
      }
    },
    "guardian_userobjectpermission": {
      "columns": {
        "id": "integer",
        "object_pk": "character varying",
        "content_type_id": "integer",
        "permission_id": "integer",
        "user_id": "bigint"
      }
    },
    "notifications_notification": {
      "columns": {
        "id": "integer",
        "level": "character varying",
        "unread": "boolean",
        "actor_object_id": "character varying",
        "verb": "character varying",
        "description": "text",
        "target_object_id": "character varying",
        "action_object_object_id": "character varying",
        "timestamp": "timestamp with time zone",
        "public": "boolean",
        "action_object_content_type_id": "integer",
        "actor_content_type_id": "integer",
        "recipient_id": "bigint",
        "target_content_type_id": "integer",
        "deleted": "boolean",
        "emailed": "boolean",
        "data": "text"
      }
    },
    "orders_note": {
      "columns": {
        "id": "bigint",
        "created": "timestamp with time zone",
        "modified": "timestamp with time zone",
        "note": "text",
        "created_by_id": "bigint",
        "task_id": "bigint"
      }
    },
    "orders_order": {
      "columns": {
        "id": "bigint",
        "created": "timestamp with time zone",
        "modified": "timestamp with time zone",
        "start_date": "timestamp with time zone",
        "end_date": "timestamp with time zone"
      }
    },
    "orders_order_beneficiaries": {
      "columns": {
        "id": "bigint",
        "order_id": "bigint",
        "beneficiary_id": "bigint"
      }
    },
    "orders_task": {
      "columns": {
        "id": "bigint",
        "created": "timestamp with time zone",
        "modified": "timestamp with time zone",
        "status": "smallint",
        "beneficiary_id": "bigint",
        "customer_service_id": "bigint",
        "negotiator_id": "bigint",
        "order_id": "bigint",
        "form_data": "jsonb",
        "assigned_status": "smallint",
        "meta_data": "jsonb"
      }
    },
    "push_notifications_apnsdevice": {
      "columns": {
        "id": "bigint",
        "name": "character varying",
        "active": "boolean",
        "date_created": "timestamp with time zone",
        "device_id": "uuid",
        "registration_id": "character varying",
        "user_id": "bigint",
        "application_id": "character varying"
      }
    },
    "push_notifications_gcmdevice": {
      "columns": {
        "id": "bigint",
        "name": "character varying",
        "active": "boolean",
        "date_created": "timestamp with time zone",
        "device_id": "bigint",
        "registration_id": "text",
        "user_id": "bigint",
        "cloud_message_type": "character varying",
        "application_id": "character varying"
      }
    },
    "push_notifications_webpushdevice": {
      "columns": {
        "id": "bigint",
        "name": "character varying",
        "active": "boolean",
        "date_created": "timestamp with time zone",
        "application_id": "character varying",
        "registration_id": "text",
        "p256dh": "character varying",
        "auth": "character varying",
        "browser": "character varying",
        "user_id": "bigint"
      }
    },
    "push_notifications_wnsdevice": {
      "columns": {
        "id": "bigint",
        "name": "character varying",
        "active": "boolean",
        "date_created": "timestamp with time zone",
        "device_id": "uuid",
        "registration_id": "text",
        "user_id": "bigint",
        "application_id": "character varying"
      }
    },
    "resource_management_resourcemanagement": {
      "columns": {
        "id": "bigint",
        "action": "character varying",
        "name": "character varying",
        "status": "smallint",
        "data": "jsonb"
      }
    },
    "resource_managment_resourcemanagement": {
      "columns": {
        "id": "bigint",
        "action": "character varying",
        "name": "character varying",
        "status": "smallint",
        "data": "jsonb"
      }
    },
    "reversion_revision": {
      "columns": {
        "id": "integer",
        "date_created": "timestamp with time zone",
        "comment": "text",
        "user_id": "bigint"
      }
    },
    "reversion_version": {
      "columns": {
        "id": "integer",
        "object_id": "character varying",
        "format": "character varying",
        "serialized_data": "text",
        "object_repr": "text",
        "content_type_id": "integer",
        "revision_id": "integer",
        "db": "character varying"
      }
    },
    "spatial_ref_sys": {
      "columns": {
        "srid": "integer",
        "auth_name": "character varying",
        "auth_srid": "integer",
        "srtext": "character varying",
        "proj4text": "character varying"
      }
    },
    "uploads_upload": {
      "columns": {
        "id": "bigint",
        "created": "timestamp with time zone",
        "modified": "timestamp with time zone",
        "dataset": "character varying",
        "object_id": "integer",
        "content_type_id": "integer"
      }
    },
    "users_renrole": {
      "columns": {
        "id": "bigint",
        "created": "timestamp with time zone",
        "modified": "timestamp with time zone",
        "role": "smallint"
      }
    },
    "users_user": {
      "columns": {
        "id": "bigint",
        "password": "character varying",
        "last_login": "timestamp with time zone",
        "is_superuser": "boolean",
        "external_key": "integer",
        "email": "character varying",
        "phone": "character varying",
        "first_name": "character varying",
        "last_name": "character varying",
        "created_at": "timestamp with time zone",
        "is_staff": "boolean",
        "avatar": "character varying"
      }
    },
    "users_user_groups": {
      "columns": {
        "id": "bigint",
        "user_id": "bigint",
        "group_id": "integer"
      }
    },
    "users_user_roles": {
      "columns": {
        "id": "bigint",
        "user_id": "bigint",
        "renrole_id": "bigint"
      }
    },
    "users_user_user_permissions": {
      "columns": {
        "id": "bigint",
        "user_id": "bigint",
        "permission_id": "integer"
      }
    }
  }
}

# def