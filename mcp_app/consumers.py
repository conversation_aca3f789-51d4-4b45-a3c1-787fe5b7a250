import json
import uuid
import asyncio
import aiohttp
from channels.generic.websocket import AsyncWebsocketConsumer


class MCPConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        await self.accept()

    async def receive(self, text_data):
        text_data_json = json.loads(text_data)
        user_question = text_data_json.get('user_question')
        if not user_question:
            await self.send(text_data=json.dumps({"error": "No user question provided."}))
            await self.close()
            return

        try:
            asyncio.create_task(self.handle_user_question(user_question))
        except Exception as e:
            await self.send(text_data=json.dumps({"error": str(e)}))
            await self.close()

    async def handle_user_question(self, user_question):
        """Separate task for each user question."""
        try:
            result = await self.call_tool(user_question)
            await self.send(text_data=json.dumps(result))
        except Exception as e:
            await self.send(text_data=json.dumps({"error": str(e)}))

    async def call_tool(self, user_question: str):
        """Call an MCP tool and send back results over WebSocket."""

        session = aiohttp.ClientSession()
        sse_resp = await session.get("http://localhost:6274/sse")

        session_id = None
        current_event = None

        async for line in sse_resp.content:
            decoded_line = line.decode().strip()

            if not decoded_line:
                continue  # Skip empty lines

            if decoded_line.startswith(":"):
                continue  # Skip pings

            if decoded_line.startswith("event:"):
                current_event = decoded_line[len("event:"):].strip()

            if decoded_line.startswith("data:"):
                data_value = decoded_line[len("data:"):].strip()
                if not data_value:
                    continue

                # 1 Detect and extract session_id
                if not session_id and "session_id=" in data_value:
                    session_id = data_value.split("session_id=")[-1]

                    message_url = f"http://localhost:6274/messages/?session_id={session_id}"

                    # 2️ Initialize session
                    initialize_request = {
                        "id": str(uuid.uuid4()),
                        "jsonrpc": "2.0",
                        "method": "initialize",
                        "params": {
                            "protocolVersion": "1.0",
                            "capabilities": {},
                            "clientInfo": {
                                "name": "MyDjangoClient",
                                "version": "0.1"
                            }
                        }
                    }
                    await session.post(message_url, json=initialize_request)
                    await asyncio.sleep(0.1)  # Small delay

                    await session.post(message_url, json={
                        "jsonrpc": "2.0",
                        "method": "notifications/initialized"
                    })

                    # 3️ Send the tool call
                    tool_request = {
                        "id": str(uuid.uuid4()),
                        "jsonrpc": "2.0",
                        "method": "tools/call",
                        "params": {
                            "name": "talk_to_llm",
                            "arguments": {
                                "user_question": user_question,
                            },
                            "_meta": {
                                "progressToken": 1
                            }
                        }
                    }
                    await session.post(message_url, json=tool_request)
                elif session_id:
                    try:
                        data = json.loads(data_value)
                        if current_event == "message":
                            result = data.get("result", {})
                            if not result.get("isError", False):
                                content = result.get("content", [])
                                if content:
                                    text_value = content[0].get("text")
                                    await self.send(text_data=json.dumps({"answer": text_value}, ensure_ascii=False))
                    except json.JSONDecodeError:
                        continue
