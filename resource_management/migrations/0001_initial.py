# Generated by Django 3.2.25 on 2024-06-20 15:01

from django.db import migrations, models
import jsoneditor.fields.django3_jsonfield


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="ResourceManagement",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "action",
                    models.CharField(
                        choices=[("create_beneficiary", "Create Beneficiary")],
                        max_length=255,
                        verbose_name="Action",
                    ),
                ),
                ("name", models.<PERSON>r<PERSON><PERSON>(max_length=255, verbose_name="Name")),
                (
                    "status",
                    models.PositiveSmallIntegerField(
                        choices=[(1, "Pending"), (2, "Executed"), (3, "Failed")],
                        db_index=True,
                        default=1,
                        verbose_name="Status",
                    ),
                ),
                (
                    "data",
                    jsoneditor.fields.django3_jsonfield.JSONField(
                        blank=True, default=dict, verbose_name="Data"
                    ),
                ),
            ],
            options={
                "verbose_name": "Resource Management",
                "verbose_name_plural": "Resource Managements",
            },
        ),
    ]
