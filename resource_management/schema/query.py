import graphene

from resource_management.models import ResourceManagement
from resource_management.schema.object_types import ResourceManagementListObjectType
from utils.graphene.decorators import authentication_required
from utils.graphene.query import (
    PageInfo,
    DjangoFilterInput,
    filter_qs_paginate_with_count,
    build_q,
)


class Query(graphene.ObjectType):
    resource_managements = graphene.Field(
        ResourceManagementListObjectType,
        pk=graphene.Int(),
        page_info=PageInfo(),
        filters=graphene.List(DjangoFilterInput),
    )

    @authentication_required
    def resolve_resource_managements(self, info, pk=None, page_info=None, filters=None):
        user = info.context.user
        queryset = ResourceManagement.objects.filter(created_by=user).select_related(
            "created_by"
        )
        return ResourceManagementListObjectType(
            *filter_qs_paginate_with_count(queryset, build_q(pk, filters), page_info)
        )
