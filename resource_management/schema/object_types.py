import graphene
from graphene_django import DjangoObjectType

from resource_management.models import ResourceManagement


class ResourceManagementObjectType(DjangoObjectType):
    class Meta:
        model = ResourceManagement


class ResourceManagementListObjectType(graphene.ObjectType):
    data = graphene.List(ResourceManagementObjectType)
    count = graphene.Int()


__all__ = ["ResourceManagementObjectType", "ResourceManagementListObjectType"]
