from django.contrib import admin, messages
from django.utils.translation import gettext_lazy as _

from resource_management.admin.forms import (
    ResourceManagerAddForm,
    ResourceManagerEditForm,
)
from resource_management.models.resource_mangement import (
    ResourceManagement,
    ResourceManagementStatus,
)


class ResourceManagerAdmin(admin.ModelAdmin):
    actions = ["execute"]
    list_display = [
        "__str__",
        "id",
        "status",
    ]

    def get_form(self, request, obj: ResourceManagement = None, change=False, **kwargs):
        if obj:
            return ResourceManagerEditForm
        return ResourceManagerAddForm

    def execute(self, request, queryset):
        if len(queryset.all()) > 1:
            self.message_user(
                request=request, message=_("choose only 1 script"), level=messages.ERROR
            )
            return
        obj = queryset.last()
        if obj and obj.status != ResourceManagementStatus.PENDING.value:
            self.message_user(
                request=request,
                message=_("Script is Already Executed"),
                level=messages.ERROR,
            )
            return
        result, file_messages, user_message, errors_file = obj.execute()
        if result is True:
            obj.data = file_messages
            obj.status = ResourceManagementStatus.EXECUTED.value
            obj.save()
            for message in user_message:
                self.message_user(
                    request=request, message=message["message"], level=message["level"]
                )
        else:
            # add Script Errors into obj Data
            obj.data = file_messages
            obj.status = ResourceManagementStatus.FAILED.value
            obj.save()
            for message in user_message:
                self.message_user(
                    request=request, message=message["message"], level=message["level"]
                )

    def has_delete_permission(self, request, obj=None):
        return False

    def has_change_permission(self, request, obj=None):
        return False


admin.site.register(ResourceManagement, ResourceManagerAdmin)
