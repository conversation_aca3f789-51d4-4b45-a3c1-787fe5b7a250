import os

from django import forms
from django.conf import settings
from django.core.files.storage import default_storage
from django.utils.safestring import mark_safe
from django.utils.translation import gettext_lazy as _
from gabbro.uploads.models import Upload
from gabbro.utils import generate_random_string

from resource_management.models import BENEFICIARY_FILE_PATH
from resource_management.models.resource_mangement import (
    ResourceManagement,
    ResourceActionsMapping,
)

MEDIA_ROOT = getattr(settings, "MEDIA_ROOT", None)


class ResourceManagerAddForm(forms.ModelForm):
    file = forms.FileField(required=False)

    class Meta:
        model = ResourceManagement
        fields = [
            "action",
            "name",
        ]

    def clean(self):
        """validate file extension before save"""
        cleaned_data = super().clean()
        action = self.cleaned_data.get("action")
        if action not in ResourceActionsMapping:
            raise forms.ValidationError(_("invalid Action"))
        validate, msg = ResourceActionsMapping[action]["class"](
            cleaned_data
        ).validate_file()
        if not validate:
            raise forms.ValidationError(msg)
        return cleaned_data

    def save(self, commit=True):
        """save file if required and create Upload Instance"""
        obj = super(ResourceManagerAddForm, self).save(commit=False)
        obj.save()
        file = self.cleaned_data.get("file")
        if file:
            file_storage = default_storage
            # rename saving the original dataset file
            file_extension = file.name.split(".")[-1]
            random_text = generate_random_string(24)
            file_name = f"{random_text}.{file_extension}"
            save_to = os.path.join(MEDIA_ROOT, BENEFICIARY_FILE_PATH)
            file_path = os.path.join(save_to, file_name)
            file_storage.save(name=file_path, content=file)
            # creating Upload instance
            Upload.objects.create(
                dataset=os.path.join(save_to, file_name),
                content_object=obj,
            )
        return obj


class ReadOnlyFileWidget(forms.Widget):
    def render(self, name, value, attrs=None, renderer=None):
        if value:
            return mark_safe(f'<a href="{value}" target="_blank">{value}</a>')
        return "-"


class ResourceManagerEditForm(forms.ModelForm):
    class Meta:
        model = ResourceManagement
        fields = ["action", "name", "status", "data"]
