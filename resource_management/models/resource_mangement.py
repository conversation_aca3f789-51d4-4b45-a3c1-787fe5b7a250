from django.contrib.contenttypes.fields import GenericRelation
from django.db import models
from django.utils.translation import gettext_lazy as _
from django_extensions.db.models import TimeStampedModel
from gabbro.uploads.models import Upload
from jsoneditor.fields.django3_jsonfield import <PERSON><PERSON><PERSON><PERSON>

from deals.scripts.upload_bulk_benefeciary import UploadBulkBeneficiary

BENEFICIARY_FILE_PATH = "attachments/beneficiaries/"

ResourceActionsMapping = {
    "create_beneficiary": {
        "class": UploadBulkBeneficiary,
        "file_path": BENEFICIARY_FILE_PATH,
        "display": _("Create Beneficiary"),
    },
}


class ResourceManagementActionChoices(models.TextChoices):
    CREATE_BENEFICIARY = "create_beneficiary", _("Create beneficiary")


class ResourceManagementStatus(models.IntegerChoices):
    PENDING = 1, _("Pending")
    EXECUTED = 2, _("Executed")
    FAILED = 3, _("Failed")


class ResourceManagement(TimeStampedModel):
    created_by = models.ForeignKey(
        "users.User",
        on_delete=models.SET_NULL,
        null=True,
        verbose_name=_("Created by"),
    )
    action = models.CharField(
        choices=ResourceManagementActionChoices.choices,
        max_length=255,
        verbose_name=_("Action"),
    )
    name = models.CharField(max_length=255, verbose_name=_("Name"))
    status = models.PositiveSmallIntegerField(
        choices=ResourceManagementStatus.choices,
        default=ResourceManagementStatus.PENDING,
        db_index=True,
        verbose_name=_("Status"),
    )
    attachment = GenericRelation(
        Upload,
        on_delete=models.SET_NULL,
        null=True,
        related_name="resource_management_attachment",
        verbose_name=_("Attachment"),
    )
    data = JSONField(default=dict, blank=True, verbose_name=_("Data"))
    errors_file = models.FileField(
        null=True, blank=True, verbose_name=_("Errors File"), max_length=255
    )
    audits = GenericRelation("auditing.Audit", verbose_name=_("Audits"))

    class Meta:
        verbose_name = _("Resource Management")
        verbose_name_plural = _("Resource Managements")

    def __str__(self):
        return self.name

    def execute(self):
        action = self.action
        file = self.attachment.last()
        if file:
            file = file.dataset
        cls = ResourceActionsMapping[action]["class"]({"file": file})
        result, file_messages, user_message, errors_file = cls.execute()
        return result, file_messages, user_message, errors_file
