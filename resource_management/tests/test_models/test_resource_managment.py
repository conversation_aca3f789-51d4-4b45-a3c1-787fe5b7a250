import os
from unittest.mock import patch
from django.conf import settings
from django.contrib.messages import DEFAULT_LEVELS
from django.test import TestCase, override_settings
from django.utils.translation.trans_real import activate
from gabbro.uploads.models import Upload
from gabbro.utils import generate_random_string

from resource_management.models import ResourceManagement, ResourceManagementStatus
from utils.tests.factories import FormSchemaFactory


class ResourceManagementBaseTestCase(TestCase):
    def setUp(self):
        activate("en")
        self.file_path = os.path.join(
            settings.BASE_DIR, "resource_management/tests/fixtures/beneficiaries.xlsx"
        )


class ResourceManagementTestCase(ResourceManagementBaseTestCase):
    def setUp(self):
        super(ResourceManagementTestCase, self).setUp()
        self.form_schem = FormSchemaFactory(
            json_schema={
                "form": {
                    "type": "object",
                    "required": ["request"],
                    "properties": {
                        "ID_CODE": {"type": ["string", "null"]},
                        "request": {
                            "type": "object",
                            "properties": {
                                "registrationDate": {
                                    "type": ["string", "null"],
                                    "format": "date",
                                }
                            },
                        },
                        "location": {
                            "type": "object",
                            "properties": {
                                "city": {"type": ["string", "null"]},
                                "region": {"type": ["string", "null"]},
                            },
                        },
                        "familyData": {
                            "type": "object",
                            "properties": {
                                "familyMembersCount": {"type": ["integer", "null"]}
                            },
                        },
                        "destination": {"type": ["string", "null"]},
                        "personalData": {
                            "type": "object",
                            "required": ["name", "mobileNumber"],
                            "properties": {
                                "name": {"type": "string"},
                                "ageHijri": {"type": ["integer", "null"]},
                                "mobileNumber": {
                                    "type": "string",
                                    "pattern": "^5\\d{8}$",
                                },
                            },
                        },
                        "financialData": {
                            "type": "object",
                            "properties": {
                                "AIP1": {"type": ["null", "number"]},
                                "AIP2": {"type": ["null", "number"]},
                                "AIP3": {"type": ["null", "number"]},
                                "AIP4": {"type": ["null", "number"]},
                                "AIP5": {"type": ["null", "number"]},
                                "salary": {"type": ["null", "number"]},
                                "funder1": {"type": ["string", "null"]},
                                "funder2": {"type": ["string", "null"]},
                                "funder3": {"type": ["string", "null"]},
                                "funder4": {"type": ["string", "null"]},
                                "funder5": {"type": ["string", "null"]},
                                "salaryBank": {"type": ["string", "null"]},
                                "entitlement": {"type": ["null", "string"]},
                                "fundingAmount": {"type": ["number", "null"]},
                                "additionalIncome": {"type": ["number", "null"]},
                                "housingAllowance": {"type": ["number", "null"]},
                                "monthlyDeduction": {"type": ["number", "null"]},
                                "remainingDurationMonths": {
                                    "type": ["integer", "null"]
                                },
                            },
                        },
                        "supportPackages": {
                            "type": "object",
                            "properties": {"product": {"type": ["null", "string"]}},
                        },
                        "professionalData": {
                            "type": "object",
                            "properties": {"job": {"type": ["null", "string"]}},
                        },
                        "personalRealEstateData": {
                            "type": "object",
                            "properties": {
                                "hasLand": {"type": ["null", "string"]},
                                "startedBuilding": {"type": ["null", "string"]},
                                "hasBuildingLicense": {"type": ["null", "string"]},
                            },
                        },
                        "رقم التقنيات": {"type": ["integer", "null"]},
                    },
                }
            },
            key="upload-bulk-beneficiaries",
        )

    def test_resource_management_creation(self):
        obj = ResourceManagement.objects.create(
            action="create_beneficiary",
            name=generate_random_string(length=20),
        )
        self.assertEqual(ResourceManagement.objects.count(), 1)
        self.assertEqual(obj.status, ResourceManagementStatus.PENDING.value)
        self.assertEqual(obj.action, "create_beneficiary")
        self.assertEqual(obj.attachment.count(), 0)

    def test_resource_management_str(self):
        obj = ResourceManagement.objects.create(
            action="create_beneficiary",
            name=generate_random_string(length=20),
        )
        self.assertEqual(str(obj), obj.name)

    @override_settings(
        MEDIA_ROOT=os.path.join(settings.BASE_DIR, "resource_management/tests/fixtures")
    )
    @patch("django.core.files.storage.default_storage.save")
    def test_resource_management_execution(self, mock_save):
        mock_save.return_value = "mocked/path/filename.xlsx"
        obj = ResourceManagement.objects.create(
            action="create_beneficiary",
            name=generate_random_string(length=20),
        )
        Upload.objects.create(dataset=self.file_path, content_object=obj)
        result, file_messages, user_message, errors_file = obj.execute()
        self.assertTrue(result)
        self.assertDictEqual(
            file_messages,
            {
                "metadata": {"created_records": 10},
                "errors": {"file_errors": {}, "script_errors": {}},
            },
        )
        self.assertListEqual(
            user_message,
            [
                {
                    "level": DEFAULT_LEVELS["SUCCESS"],
                    "message": "10 Beneficiary Created",
                },
                {"level": DEFAULT_LEVELS["ERROR"], "message": "0 Error"},
            ],
        )
