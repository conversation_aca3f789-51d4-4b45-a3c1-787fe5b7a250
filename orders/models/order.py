from django.contrib.contenttypes.fields import GenericRelation
from django.db import models
from django.utils.functional import cached_property
from django.utils.translation import gettext_lazy as _
from django_extensions.db.models import TimeStampedModel

from orders.models.task import TaskStatus, TaskAssignedStatus


class Order(TimeStampedModel):
    beneficiaries = models.ManyToManyField(
        "deals.Beneficiary", blank=False, verbose_name=_("Beneficiaries")
    )
    start_date = models.DateTimeField(verbose_name=_("Start Date"))
    end_date = models.DateTimeField(verbose_name=_("End Date"))
    audits = GenericRelation("auditing.Audit", verbose_name=_("Audits"))

    class Meta:
        verbose_name = _("Order")
        verbose_name_plural = _("Orders")

    def __str__(self):
        return f"{self.id}"

    @cached_property
    def tasks_count(self) -> int:
        return getattr(self, "total_tasks", None) or self.tasks.count()

    @cached_property
    def rejected_tasks_count(self) -> int:
        return (
            getattr(self, "rejected_tasks", None)
            or self.tasks.filter(status=TaskStatus.REJECTED.value).count()
        )

    @cached_property
    def pending_review_tasks_count(self) -> int:
        return (
            getattr(self, "pending_review_tasks", None)
            or self.tasks.filter(status=TaskStatus.PENDING_REVIEW.value).count()
        )

    @cached_property
    def assigned_to_customer_services_task_count(self) -> int:
        return (
            getattr(self, "assigned_to_customer_services_task", None)
            or self.tasks.filter(
                status=TaskStatus.IN_PROGRESS,
                assigned_status=TaskAssignedStatus.ASSIGNED_TO_CUSTOMER_SERVICE.value,
            ).count()
        )

    @cached_property
    def assigned_to_negotiator_task_count(self) -> int:
        return (
            getattr(self, "assigned_to_negotiator_task", None)
            or self.tasks.filter(
                assigned_status=TaskAssignedStatus.ASSIGNED_TO_NEGOTIATOR.value,
            ).count()
        )
