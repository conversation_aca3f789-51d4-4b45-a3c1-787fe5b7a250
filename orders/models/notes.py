from django.db import models
from django.utils.translation import gettext_lazy as _
from django_extensions.db.models import TimeStampedModel


class Note(TimeStampedModel):
    note = models.TextField(verbose_name=_("Note"), max_length=5000)
    task = models.ForeignKey(
        "orders.Task",
        on_delete=models.CASCADE,
        verbose_name=_("Task"),
        related_name="notes",
    )
    created_by = models.ForeignKey(
        "users.User",
        verbose_name=_("Created by"),
        on_delete=models.CASCADE,
        related_name="created_notes",
    )

    def __str__(self):
        return f"{self.created_by}-{self.task}"
