from django.contrib.contenttypes.fields import GenericRelation
from django.db import models
from django.utils.translation import gettext_lazy as _
from django_extensions.db.models import TimeStampedModel
from jsoneditor.fields.django3_jsonfield import <PERSON><PERSON><PERSON><PERSON>

from users.models import UserRoleChoices
from utils.general_utils import get_nested_value

TASK_DATA_MAPPER = {
    "customer_service": {
        "service_type": "customer_service.beneficiaryData.benificiaryServiceType.serviceType"
    }
}


class TaskServiceType(models.TextChoices):
    LOOKING_FOR_PROPERTY = "بحث عن عقار", _("Looking For Property")
    REAL_STATE_FINANCING = "تمويل عقاري", _("Real State Financing")


class TaskAssignedStatus(models.IntegerChoices):
    NOT_ASSIGNED = 1, _("Not Assigned")
    ASSIGNED_TO_CUSTOMER_SERVICE = 2, _("Assigned To Customer Service")
    ASSIGNED_TO_NEGOTIATOR = 3, _("Assigned To Negotiator")
    ASSIGNED_TO_PROJECT_MANAGER = 5, _("Assigned To Project Manager")


class TaskStatus(models.IntegerChoices):
    IN_PROGRESS = 1, _("In Progress")
    REJECTED = 3, _("Rejected")
    PENDING_REVIEW = 4, _("Pending Review")
    FINISHED = 5, _("Finished")
    CHECK_BENEFICIARY_DATA = 6, _("Checking Beneficiary Data")
    REVIEW_RECOMMENDATIONS = 7, _("Review Recommendations")
    REVIEW_OFFERS = 8, _("Review Offers")
    CREATE_DEAL = 9, _("Create Deal")
    LOOKING_FOR_PROPERTY = 10, _("Looking For Property")
    SEND_PROPERTY_TO_BENEFICIARY = 11, _("Send Property To Beneficiary")
    PROPERTY_INSPECTION = 12, _("Property Inspection")
    NEGOTIATE_WITH_OWNER = 13, _("Negotiate With Owner")
    SHARING_DATA_WITH_CLIENT = 14, _("Sharing Data With Client")
    OFFER_SENT_TO_BENEFICIARY = 15, _("Offer Sent To Beneficiary")
    BENEFICIARY_ACCEPT_THE_OFFER = 16, _("Beneficiary Accept The Offer")
    OFFER_RECEIVED = 17, _("Offer Received")
    OFFER_AGREEMENT = 18, _("Offer Agreement")
    SECOND_LEVEL_PREFERENCES = 19, _("Second Level Preferences")
    IDENTIFY_SUITABLE_PROPERTY = 20, _("Identify Suitable Property")


class Task(TimeStampedModel):
    order = models.ForeignKey(
        "orders.Order",
        on_delete=models.PROTECT,
        related_name="tasks",
        verbose_name=_("Order"),
    )
    customer_service = models.ForeignKey(
        "users.User",
        on_delete=models.PROTECT,
        related_name="tasks",
        limit_choices_to={"roles__role": UserRoleChoices.CUSTOMER_SERVICES},
        verbose_name=_("Customer Service"),
    )
    negotiator = models.ForeignKey(
        "users.User",
        on_delete=models.SET_NULL,
        related_name="negotiator_tasks",
        null=True,
        limit_choices_to={"roles__role": UserRoleChoices.NEGOTIATORS},
        verbose_name=_("Negotiator"),
    )
    beneficiary = models.ForeignKey(
        "deals.Beneficiary",
        on_delete=models.PROTECT,
        related_name="tasks",
        verbose_name=_("Beneficiary"),
    )
    status = models.PositiveSmallIntegerField(
        choices=TaskStatus.choices,
        default=TaskStatus.IN_PROGRESS.value,
        db_index=True,
        verbose_name=_("Status"),
    )
    assigned_status = models.PositiveSmallIntegerField(
        choices=TaskAssignedStatus.choices,
        default=TaskAssignedStatus.ASSIGNED_TO_CUSTOMER_SERVICE,
        db_index=True,
        verbose_name=_("Assigned Status"),
    )
    form_data = JSONField(
        blank=True,
        null=True,
        verbose_name=_("Form Data"),
    )
    meta_data = JSONField(blank=True, null=True, verbose_name=_("Meta Data"))
    audits = GenericRelation("auditing.Audit", verbose_name=_("Audits"))

    class Meta:
        verbose_name = _("Task")
        verbose_name_plural = _("Tasks")
        indexes = [
            models.Index(
                models.F(
                    "form_data__customer_service__beneficiaryData__personalData__mobileNumber"
                ),
                name="task_beneficiary_phone_number",
            ),
            models.Index(
                models.F(
                    "form_data__customer_service__beneficiaryData__location__region"
                ),
                name="task_beneficiary_region",
            ),
            models.Index(
                models.F(
                    "form_data__customer_service__beneficiaryData__location__city"
                ),
                name="task_beneficiary_cty",
            ),
            models.Index(
                models.F(
                    "form_data__customer_service__beneficiaryData__location__product"
                ),
                name="task_beneficiary_product",
            ),
            models.Index(
                models.F(
                    "form_data__customer_service__beneficiaryData__professionalData__job"
                ),
                name="task_beneficiary_job",
            ),
            models.Index(
                models.F(
                    "form_data__customer_service__beneficiaryData__financialData__salaryBank"
                ),
                name="task_beneficiary_salary_bank",
            ),
            models.Index(
                models.F(
                    "form_data__customer_service__beneficiaryData__personalData__age"
                ),
                name="task_beneficiary_age",
            ),
            models.Index(
                models.F(
                    "form_data__customer_service__beneficiaryData__familyData__familyMembersCount"
                ),
                name="task_beneficiary_members_count",
            ),
            models.Index(
                models.F(
                    "form_data__customer_service__beneficiaryData__financialData__fundingAmount"
                ),
                name="task_beneficiary_funding",
            ),
        ]

    def __str__(self):
        return f"{self.beneficiary.name} - {self.id}"

    def extract_data(self, field: str, path: str):
        field: str = TASK_DATA_MAPPER[path].get(field)
        data = self.form_data or {}
        if not field:
            return None
        field = get_nested_value(data=data, path=field)
        return field
