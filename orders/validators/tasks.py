from typing import Dict

from django.contrib.auth import get_user_model
from django.db.models import QuerySet
from django.utils.translation import ugettext_lazy as _
from gabbro.graphene import BadRequest, NotFound

from common.validators import InputValidation
from deals.management.commands.load_ren_locations import LAYERS_KEYS
from deals.models import Layer
from formschemas.mixins import FormSchemaMixin
from orders.mixins import TaskMixin
from orders.models import Task, TaskAssignedStatus, TaskStatus
from orders.schema.input_object_types import Level
from orders.strategies.recommendations import LOCATION_PREFERENCES_MAPPER
from users.mixins import UserMixin
from users.models import UserRoleChoices
from utils.general_utils import get_nested_value
from utils.ren_calculator import RealEstateFinanceCalculator

User = get_user_model()


class UpdateTaskValidation(InputValidation, TaskMixin):
    def get_object_if_exists(self, user: User, task_input: dict) -> Dict[str, Task]:
        task = self.get_task_if_exists(filters={"id": task_input.get("task_id")})
        # validate if deal exists
        self.validate_task_has_not_an_ongoing_deal(task=task)
        # validate status, form_data
        self.validate_status(task=task)
        self.validate_form_data(task_input=task_input, task=task)
        return {"task": task}

    @staticmethod
    def validate_status(task):
        if task.status in [
            TaskStatus.REJECTED,
            TaskStatus.PENDING_REVIEW,
            TaskStatus.FINISHED,
        ]:
            raise BadRequest(
                reason={
                    "status": _("Task is %(status)s, can't be updated")
                    % {"status": task.get_status_display()}
                }
            )

    def validate_form_data(self, task_input: dict, task: Task):
        form_data = task_input["form_data"]
        # validate keys
        level_1_keys = ["beneficiaryData"]
        level_2_keys = ["negotiatorData", "placeOrder"]
        if task.assigned_status == TaskAssignedStatus.ASSIGNED_TO_CUSTOMER_SERVICE:
            if not all(key in level_1_keys for key in form_data.keys()):
                raise BadRequest(reason={"form_data": _("invalid key") % {}})
        if task.assigned_status == TaskAssignedStatus.ASSIGNED_TO_NEGOTIATOR:
            if not all(key in level_2_keys for key in form_data.keys()):
                raise BadRequest(reason={"form_data": _("invalid key") % {}})
        if not task_input.get("is_draft"):
            # validate locations
            self.task_location_validation(form_data=form_data, task=task)
            # validate fund calculator
            self.task_fund_validation(form_data=form_data)

    @staticmethod
    def task_location_validation(form_data: dict, task: Task):
        """
        - validate city and Region in form_data
        """
        locations = dict
        if task.assigned_status == TaskAssignedStatus.ASSIGNED_TO_CUSTOMER_SERVICE:
            locations = form_data.get("beneficiaryData", {}).get(
                "locationPreferences", {}
            )
        if task.assigned_status == TaskAssignedStatus.ASSIGNED_TO_NEGOTIATOR:
            locations = form_data.get("placeOrder", {}).get(
                "locationPreferencesLevelTwo",
                {}
                or form_data.get("negotiatorData", {}).get("locationPreferences", {}),
            )

        region_id = locations.get("regionId")
        city_id = locations.get("cityId")
        zone_id_list = locations.get("mainDivision")
        districts_id_list = locations.get("district")
        # validate locations
        validation = (
            isinstance(zone_id_list, list)
            and all(isinstance(zone, dict) for zone in zone_id_list),
            region_id is not None,
            city_id is not None,
            isinstance(districts_id_list, list)
            and all(
                isinstance(district, dict) and district.get("id") is not None
                for district in districts_id_list
            ),
        )
        if not all(validation):
            return

        # validate if required layers exists first
        city_layer = Layer.objects.filter(
            key=LAYERS_KEYS.get("city"),
        ).first()
        if not city_layer:
            raise NotFound(reason={"form_data": _("city Layer Not Found") % {}})
        region_layer = Layer.objects.filter(key=LAYERS_KEYS.get("region")).first()
        if not region_layer:
            raise NotFound(reason={"form_data": _("region Layer Not Found") % {}})
        district_layer = Layer.objects.filter(
            key=LAYERS_KEYS.get("districts"),
        ).first()
        if not district_layer:
            raise NotFound(reason={"form_data": _("districts Layer Not Found") % {}})

        # validate the relation between city and region
        region_record = region_layer.record_set.filter(
            source_properties__id=region_id
        ).first()
        if not region_record:
            raise NotFound(
                reason={
                    "form_data": _("Region %(region_id)s Not Found")
                    % {"region_id": region_id}
                }
            )

        city_record = city_layer.record_set.filter(
            source_properties__id=city_id,
            source_properties__region_id=region_id,
        )
        if not city_record:
            raise NotFound(
                reason={
                    "form_data": _("City %(city_id)s Not Found") % {"city_id": city_id}
                }
            )

        # check if district exists and validate through (city and zones)
        districts_id_list = [district["id"] for district in districts_id_list]
        filters = {
            "source_properties__city_id": city_id,
            "source_properties__id__in": districts_id_list,
        }
        zone_id_list = [
            zone["id"]
            for zone in zone_id_list
            if isinstance(zone, dict) and "id" in zone
            if isinstance(zone, dict) and "id" in zone and zone["id"] is not None
        ]
        zone_id_list = zone_id_list if zone_id_list else None
        if zone_id_list:
            filters["source_properties__zone_id__in"] = zone_id_list
        else:
            filters["source_properties__zone_id__isnull"] = True
        districts_record = district_layer.record_set.filter(**filters)
        district_ids_db = set(
            districts_record.values_list("source_properties__id", flat=True)
        )
        district_ids_input = set(districts_id_list)
        wrong_districts_ids = district_ids_input.difference(district_ids_db)
        if wrong_districts_ids:
            raise BadRequest(
                reason={
                    "form_data": _(
                        "District %(wrong_districts_ids)s Doesnt Relate to this Zone/City"
                    )
                    % {"wrong_districts_ids": wrong_districts_ids}
                }
            )
        return None

    @staticmethod
    def task_fund_validation(form_data: dict):
        form_data = form_data.get("negotiatorData", {}) or form_data.get(
            "beneficiaryData", {}
        )
        financial_data = form_data.get("financialData", {})
        installment_choice = financial_data.get("installmentChoice", {})
        if not installment_choice:
            return
        salary = financial_data.get("salary")
        monthly_deduction = financial_data.get("monthlyDeduction")
        remaining_duration_months = financial_data.get("remainingDurationMonths")
        fund_duration = installment_choice.get("fundingPeriod")
        cls = RealEstateFinanceCalculator(
            salary,
            monthly_deduction,
            remaining_duration_months,
            fund_duration=fund_duration,
        )
        # validate calculation
        is_valid = cls.validations()
        if not is_valid:
            raise BadRequest(reason={"form_data": _("invalid input field") % {}})

        # calculate based on task form_data
        task_expected_plan = cls.calculation()

        amount_guaranteed = installment_choice.get("fundingAmount")
        monthly_inst_during_pf = installment_choice.get("installmentDuringPersonalLoan")
        monthly_inst_post_pf = installment_choice.get("installmentAfterPersonalLoan")
        profit_margin_finance_period = installment_choice.get("profitMargin")
        total_profit_amount = installment_choice.get("totalAmountWithProfits")
        # calculate the chosen plan
        task_financial_plan = {
            "amount_guaranteed": cls.round_result(amount_guaranteed),
            "monthly_inst_during_pf": cls.round_result(monthly_inst_during_pf),
            "monthly_inst_post_pf": cls.round_result(monthly_inst_post_pf),
            "profit_margin_finance_period": cls.round_result(
                profit_margin_finance_period
            ),
            "total_profit_amount": cls.round_result(total_profit_amount),
            "fund_duration": fund_duration,
        }
        if task_financial_plan != task_expected_plan:
            # calculate error margin
            error_margin = all(
                [
                    cls.is_close_enough(
                        task_financial_plan["amount_guaranteed"],
                        task_expected_plan["amount_guaranteed"],
                    ),
                    cls.is_close_enough(
                        task_financial_plan["monthly_inst_during_pf"],
                        task_expected_plan["monthly_inst_during_pf"],
                    ),
                    cls.is_close_enough(
                        task_financial_plan["monthly_inst_post_pf"],
                        task_expected_plan["monthly_inst_post_pf"],
                    ),
                    cls.is_close_enough(
                        task_financial_plan["profit_margin_finance_period"],
                        task_expected_plan["profit_margin_finance_period"],
                    ),
                    cls.is_close_enough(
                        task_financial_plan["total_profit_amount"],
                        task_expected_plan["total_profit_amount"],
                    ),
                ]
            )
            if not error_margin:
                raise BadRequest(reason={"form_data": _("invalid expected plan ") % {}})
        return None


class AssignTaskToProjectManagerValidation(InputValidation, TaskMixin):
    def get_object_if_exists(self, user: User, task_input: dict) -> Dict[str, Task]:
        task = self.get_task_if_exists(filters={"id": task_input.get("task_id")})
        # validate if deal exists
        self.validate_task_has_not_an_ongoing_deal(task=task)
        # validate status, form_data
        self.validate_keys_exists(
            form_data=task.form_data, task_id=task.id, level="customer_service"
        )
        self.validate_status(task=task)
        return {"task": task}

    @staticmethod
    def validate_status(task: Task):
        if task.status != TaskStatus.IN_PROGRESS:
            raise BadRequest(
                reason={
                    "status": _("Task is %(status)s, can't be updated")
                    % {"status": task.get_status_display()}
                }
            )


class AssignTaskToNegotiatorValidation(InputValidation, TaskMixin, UserMixin):
    def get_object_if_exists(
        self, user: User, task_input: dict
    ) -> Dict[str, QuerySet[Task and User]]:
        tasks = self.get_tasks_if_exist(filters={"id__in": task_input.get("task_ids")})
        negotiator = self.get_user_if_exists(
            user_id=task_input["negotiator_id"], role=UserRoleChoices.NEGOTIATORS
        )
        # validate status, form_data
        for task in tasks:
            self.validate_status(task=task)
            self.validate_keys_exists(
                form_data=task.form_data, task_id=task.id, level="customer_service"
            )
        return {"tasks": tasks, "negotiator": negotiator}

    @staticmethod
    def validate_status(task: Task):
        if task.status != TaskStatus.PENDING_REVIEW:
            raise BadRequest(
                reason={
                    "status": _("Task is %(status)s, can't be updated")
                    % {"status": task.get_status_display()}
                }
            )


class RejectTaskValidation(InputValidation, TaskMixin, FormSchemaMixin):
    def get_object_if_exists(
        self, user: User, task_input: dict
    ) -> Dict[str, Task and User]:
        task = self.get_task_if_exists(filters={"id": task_input.get("task_id")})
        self.validate_task_status_is_in_progress(task=task)
        self.validate_task_has_not_an_ongoing_deal(task=task)
        form_schema = (
            self.get_form_schema_if_exists(key=task_input.get("form_schema_key"))
            if task_input.get("form_schema_key")
            else None
        )
        return {"task": task, "form_schema": form_schema}


class DeleteTaskValidation(InputValidation, TaskMixin):
    def get_object_if_exists(self, user: User, task_input: dict):
        task = self.get_task_if_exists(filters={"id": task_input.get("task_id")})
        self.validate_task_status_is_in_progress(task=task)
        self.validate_task_has_not_an_ongoing_deal(task=task)
        return {"task": task}


class ReassignToCustomerServiceTaskValidation(InputValidation, TaskMixin, UserMixin):
    def get_object_if_exists(self, user: User, task_input: dict):
        tasks = self.get_tasks_if_exist(
            filters={
                "id__in": task_input.get("tasks_ids"),
                "assigned_status": TaskAssignedStatus.ASSIGNED_TO_CUSTOMER_SERVICE,
            }
        )
        customer_service = self.get_user_if_exists(
            user_id=task_input["user_id"], role=UserRoleChoices.CUSTOMER_SERVICES
        )
        self.validate_tasks_statuses_is_in_progress(tasks=tasks)
        return {"tasks": tasks, "customer_service": customer_service}


class ReassignToNegotiatorTaskValidation(InputValidation, TaskMixin, UserMixin):
    def get_object_if_exists(self, user: User, task_input: dict):
        tasks = self.get_tasks_if_exist(
            filters={
                "id__in": task_input.get("tasks_ids"),
                "assigned_status": TaskAssignedStatus.ASSIGNED_TO_NEGOTIATOR,
            }
        )
        negotiator = self.get_user_if_exists(
            user_id=task_input["user_id"], role=UserRoleChoices.NEGOTIATORS
        )
        self.validate_tasks_statuses_is_in_progress(tasks=tasks)
        self.validate_tasks_has_no_negotiators(tasks=tasks)
        return {"tasks": tasks, "negotiator": negotiator}

    @staticmethod
    def validate_tasks_has_no_negotiators(tasks: QuerySet[Task]):
        invalid_tasks_ids = tasks.filter(negotiator__isnull=True).values_list(
            "id", flat=True
        )
        if invalid_tasks_ids:
            raise BadRequest(
                reason={
                    "tasks_ids": _(
                        "Tasks with ids %(ids)s have no negotiator, please assign to negotiator first"
                    )
                    % {"ids": str(list(invalid_tasks_ids))}
                }
            )


class RecommendationValidation(InputValidation, TaskMixin):
    def get_object_if_exists(
        self, user: User, input_data: dict
    ) -> Dict[str, QuerySet[Task]]:
        task = self.get_task_if_exists(
            filters={
                "id": input_data.get("task_id"),
                "assigned_status": TaskAssignedStatus.ASSIGNED_TO_NEGOTIATOR,
            }
        )
        # validate task form_data first before getting recommendation
        if not task.form_data:
            raise BadRequest(
                reason={"form_data": _("Please Submit Form Data First") % {}}
            )
        # level 1 check
        level_1_required_keys = ["beneficiaryData"]
        for key in level_1_required_keys:
            if not task.form_data.get("customer_service", {}).get(key):
                raise BadRequest(
                    reason={"form_data": _("please Submit first level") % {}}
                )
        # level 2 check
        level = input_data.get("level")
        if level == Level.DISTRICT:
            level_2_required_keys = ["negotiatorData", "placeOrder"]
            for key in level_2_required_keys:
                if not task.form_data.get("negotiator", {}).get(key):
                    raise BadRequest(
                        reason={"form_data": _("please Submit second level") % {}}
                    )
            districts = get_nested_value(
                data=task.form_data.get("negotiator", {}),
                path=LOCATION_PREFERENCES_MAPPER["level2"]["district"],
            )
            if districts is None:
                raise BadRequest(reason={"district": _("District is Required") % {}})
        return {"task": task, "level": input_data.get("level")}
