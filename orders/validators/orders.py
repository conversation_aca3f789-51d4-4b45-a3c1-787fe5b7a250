from typing import Dict

from django.contrib.auth import get_user_model
from django.utils.translation import ugettext_lazy as _
from gabbro.graphene import NotFound

from common.validators import InputValidation
from users.models import UserRoleChoices

User = get_user_model()


class CreateOrderValidation(InputValidation):
    def get_object_if_exists(self, user: User, order_input: dict) -> Dict[str, User]:
        customer_service = User.objects.filter(
            id=order_input.get("customer_service", None),
            roles__role=UserRoleChoices.CUSTOMER_SERVICES,
        ).first()
        if not customer_service:
            raise NotFound(
                reason={"customer_service": _("invalid customer service id") % {}}
            )
        context = {"customer_service": customer_service}
        return context
