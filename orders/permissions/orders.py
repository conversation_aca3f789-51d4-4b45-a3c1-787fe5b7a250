from django.utils.translation import ugettext_lazy as _
from gabbro.graphene import Unauthorized

from common.permissions import PermissionsInterface


class CreateOrderPerms(PermissionsInterface):
    def check_permissions(self, user, context):
        permissions = (user.is_project_manager, user.is_superuser)
        if not any(permissions):
            raise Unauthorized(reason={"user": _("Permission Denied") % {}})
