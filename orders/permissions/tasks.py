from typing import Dict

from django.utils.translation import ugettext_lazy as _
from gabbro.graphene import Unauthorized

from common.permissions import PermissionsInterface
from orders.models import TaskAssignedStatus, Task
from users.models import User


class UpdateCustomerServiceTaskPerms(PermissionsInterface):
    def check_permissions(self, user: User, context: Dict[str, Task]):
        task = context["task"]
        authorization = (
            user.is_superuser,
            user.is_project_manager,
            (
                user.is_customer_service
                and task.customer_service == user
                and task.assigned_status
                == TaskAssignedStatus.ASSIGNED_TO_CUSTOMER_SERVICE
            ),
        )
        if not any(authorization):
            raise Unauthorized(reason={"user": _("Permission Denied") % {}})


class UpdateNegotiatorTaskPerms(PermissionsInterface):
    def check_permissions(self, user: User, context: Dict[str, Task]):
        task = context["task"]
        authorization = (
            user.is_superuser,
            user.is_project_manager,
            (
                user.is_negotiator
                and task.negotiator == user
                and task.assigned_status == TaskAssignedStatus.ASSIGNED_TO_NEGOTIATOR
            ),
        )
        if not any(authorization):
            raise Unauthorized(reason={"user": _("Permission Denied") % {}})


class AssignTaskToProjectManagerTaskPerms(PermissionsInterface):
    def check_permissions(self, user: User, context: Dict[str, Task]):
        task = context["task"]
        authorization = (task.customer_service == user, user.is_superuser)
        if not any(authorization):
            raise Unauthorized(reason={"user": _("Permission Denied") % {}})


class AssignTaskToNegotiatorTaskPerms(PermissionsInterface):
    def check_permissions(self, user: User, context: Dict[str, Task]):
        authorization = (user.is_project_manager, user.is_superuser)
        if not any(authorization):
            raise Unauthorized(reason={"user": _("Permission Denied") % {}})


class RejectTaskPerms(PermissionsInterface):
    def check_permissions(self, user: User, context: Dict[str, Task]):
        task = context["task"]
        authorization = (
            task.customer_service == user,
            task.negotiator == user,
            user.is_superuser,
        )
        if not any(authorization):
            raise Unauthorized(reason={"user": _("Permission Denied") % {}})


class DeleteTaskPerms(PermissionsInterface):
    def check_permissions(self, user, context):
        authorization = (user.is_project_manager, user.is_superuser)
        if not any(authorization):
            raise Unauthorized(reason={"user": _("Permission Denied") % {}})


class ReassignToCustomerServiceTaskPerms(PermissionsInterface):
    def check_permissions(self, user, context):
        authorization = (user.is_project_manager, user.is_superuser)
        if not any(authorization):
            raise Unauthorized(reason={"user": _("Permission Denied") % {}})


class ReassignToNegotiatorTaskPerms(PermissionsInterface):
    def check_permissions(self, user, context):
        authorization = (user.is_project_manager, user.is_superuser)
        if not any(authorization):
            raise Unauthorized(reason={"user": _("Permission Denied") % {}})


class RecommendationsPerms(PermissionsInterface):
    def check_permissions(self, user, context):
        task: Task = context["task"]
        authorization = (
            user.is_project_manager,
            user.is_negotiator and task.negotiator == user,
            user.is_superuser,
        )
        if not any(authorization):
            raise Unauthorized(reason={"user": _("Permission Denied") % {}})
