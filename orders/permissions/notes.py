from django.utils.translation import ugettext_lazy as _
from gabbro.graphene import Unauthorized

from common.permissions import PermissionsInterface


class CreateNotePerms(PermissionsInterface):
    def check_permissions(self, user, context):
        task = context["task"]
        permissions = (
            user.is_project_manager,
            user.is_superuser,
            (user.is_customer_service and task.customer_service == user),
            (user.is_negotiator and task.negotiator == user),
        )
        if not any(permissions):
            raise Unauthorized(reason={"user": _("Permission Denied") % {}})
