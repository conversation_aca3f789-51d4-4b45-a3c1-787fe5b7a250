# Generated by Django 3.2.25 on 2024-09-10 11:54

import logging
from typing import Optional

from django.db import migrations
from django.db.models import QuerySet

from deals.management.commands.load_ren_locations import LAYERS_KEYS
from deals.models import Layer, Record
from orders.models import Task

logger = logging.getLogger("orders")


def update_locations_preferences(apps, schema_editor):
    tasks = Task.objects.filter(
        form_data__has_key="clientPreferences",
        form_data__clientPreferences__locationPreferences__regionId__isnull=True,
    ).select_related("beneficiary")
    logger.debug(f"[update_locations_preferences] tasks count: {tasks.count()}")
    region_layer = Layer.objects.filter(key=LAYERS_KEYS.get("region")).first()
    city_layer = Layer.objects.filter(
        key=LAYERS_KEYS.get("city"),
    ).first()
    zone_layer = Layer.objects.filter(key=LAYERS_KEYS.get("zone")).first()
    if not all([region_layer, city_layer, zone_layer]):
        logger.debug(f"[update_locations_preferences] invalid layer id")
        return
    for task in tasks:
        logger.debug(f"[update_locations_preferences] task_id: {task.id}")
        form_data = task.form_data
        task_level_1_form_data = form_data.get("clientPreferences", {}).get(
            "locationPreferences"
        )
        region = task_level_1_form_data.get("preferredRegion")
        zone = task_level_1_form_data.get("mainDivision")
        city = task_level_1_form_data.get("preferredCity")
        if not all([region, city]):
            logger.debug(
                f"[update_locations_preferences] no region or city in the form_data"
            )
            return
        # check if both region and city exist
        updated_form_data = check_locations_exists_and_update_drafts(
            task=task,
            region_layer=region_layer,
            city_layer=city_layer,
            region=region,
            city=city,
        )
        if updated_form_data:
            task.form_data = updated_form_data
            logger.debug(
                f"[update_locations_preferences] updated_task_form_data: {updated_form_data}"
            )
        else:
            update_task(
                task=task,
                region_layer=region_layer,
                city_layer=city_layer,
                zone_layer=zone_layer,
                region=region,
                city=city,
                zone=zone,
            )
    Task.objects.bulk_update(tasks, ["form_data"])


def check_city_duplicated_records(value, records: QuerySet[Record]):
    records_count = records.filter(source_properties__city_name__iexact=value).count()
    return bool(records_count > 1)


def check_locations_exists_and_update_drafts(
    task: Task, region_layer: Layer, city_layer: Layer, region: str, city: str
) -> Optional[dict]:
    region_records = region_layer.record_set.filter(
        source_properties__region_name__iexact=region
    )
    city_records = city_layer.record_set.filter(
        source_properties__city_name__iexact=city
    )
    form_data = task.form_data
    if all([region_records, city_records]):
        logger.debug(
            "[check_locations_exists_and_update_drafts] No region or city records"
        )
        return

    client_preferences_form = form_data.pop("clientPreferences", None)
    location_preferences_form = client_preferences_form.get("locationPreferences", {})
    location_preferences_form.pop("preferredCity", None)
    location_preferences_form.pop("preferredRegion", None)
    location_preferences_form["mainDivision"] = location_preferences_form.get(
        "mainDivision", {"id": None, "label": "غير محدد"}
    )
    form_data["drafts"] = {"clientPreferences": client_preferences_form}
    return form_data


def update_form_data(data: dict, region_id: int, city_id: int, zone: dict = None):
    updated_keys = {
        "regionId": region_id,
        "cityId": city_id,
    }
    if zone:
        updated_keys.update(mainDivision={"id": zone["id"], "label": zone["label"]})
    # update each level form data
    level_1_data = data.get("clientPreferences").get("locationPreferences")
    level_2_data = data.get("placeOrder", {}).get("locationPreferencesLevelTwo")

    if level_2_data:
        # update level 2
        level_2_data.update(updated_keys)
    if level_1_data:
        # update level 1
        level_1_data.update(updated_keys)

    # update drafts if exists
    if data.get("drafts"):
        level_1_data_draft = (
            data.get("drafts")
            .get("clientPreferences", {})
            .get("locationPreferences", {})
        )
        level_2_data_draft = (
            data.get("drafts")
            .get("placeOrder", {})
            .get("locationPreferencesLevelTwo", {})
        )

        if level_1_data_draft:
            # update level 1 form data drafts
            level_1_data_draft.update(updated_keys)

        if level_2_data_draft:
            # update level 2 form data drafts
            level_2_data_draft.update(updated_keys)

    return data


def update_task(
    task: Task,
    region_layer: Layer,
    city_layer: Layer,
    zone_layer: Layer,
    region: str,
    city: str,
    zone: str,
):
    form_data = task.form_data
    has_duplicated_records = check_city_duplicated_records(
        value=city, records=city_layer.record_set
    )
    if has_duplicated_records:
        region_record = (
            region_layer.record_set.filter(
                source_properties__region_name__iexact=region
            )
            .distinct("source_properties__region_name")
            .first()
        )
        if not region_record:
            return
        region_id = region_record.source_properties.get("id")
        city_matched_with_region = (
            city_layer.record_set.filter(
                source_properties__city_name=city,
                source_properties__region_id=region_id,
            )
            .distinct("source_properties__city_name")
            .first()
        )
        if not city_matched_with_region:
            form_data.update(mainDivision={"id": None, "label": "غير محدد"})
        else:
            city_id = city_matched_with_region.source_properties.get("id")
            if isinstance(zone, str):
                zone_matched_with_city = (
                    zone_layer.record_set.filter(
                        source_properties__zone_name__iexact=zone,
                        source_properties__city_id=city_id,
                    )
                    .distinct("source_properties__zone_name")
                    .first()
                )
                if zone_matched_with_city:
                    zone_data = {
                        "id": zone_matched_with_city.id,
                        "label": zone_matched_with_city.source_properties.get(
                            "zone_name"
                        ),
                    }
                else:
                    zone_data = {"id": None, "label": zone}
            else:
                zone_data = None
            form_data = update_form_data(
                form_data, region_id=region_id, city_id=city_id, zone=zone_data
            )
        task.form_data = form_data
    else:
        city_record = city_layer.record_set.filter(
            source_properties__city_name__iexact=city,
        ).first()
        if not city_record:
            return
        city_id = city_record.source_properties.get("id")
        region_id = city_record.source_properties.get("region_id")
        if isinstance(zone, str):
            zone_matched_with_city = zone_layer.record_set.filter(
                source_properties__city_id=city_id, source_properties__zone_name=zone
            ).distinct("source_properties__zone_name")
            if zone_matched_with_city:
                zone_data = {
                    "id": zone_matched_with_city.id,
                    "label": zone_matched_with_city.source_properties.get("zone_name"),
                }
            else:
                zone_data = {"id": None, "label": zone}
        else:
            zone_data = None
        form_data = update_form_data(
            form_data, region_id=region_id, city_id=city_id, zone=zone_data
        )
        task.form_data = form_data

    logger.debug(f"[update_locations_preferences] updated_task_form_data: {form_data}")
    return task


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0012_prefill_tasks_form_data"),
    ]

    operations = [migrations.RunPython(update_locations_preferences)]
