# Generated by Django 3.2.25 on 2024-09-05 09:22

from django.db import migrations
from django.db.models import Q

def prefill_tasks_form_data(apps, schema_editor):
    Task = apps.get_model('orders', 'Task')
    tasks = Task.objects.all().select_related("customer_service")
    for task in tasks:
        # fulfill BeneficiaryData and NegotiatorData
        form_data = task.form_data
        prefilled_transactional_data = {
            "assignmentDate": task.created.strftime("%Y-%m-%d"),
            "assignmentTime": task.created.strftime("%H:%M:%S"),
            "assignmentNumber": str(task.id),
            "customerServiceEmployeeName": f"{task.customer_service.first_name} {task.customer_service.last_name}",
        }
        form_data["beneficiaryData"]["basicTransactionData"] = prefilled_transactional_data
        if form_data.get("negotiatorData"):
            form_data["negotiatorData"]["basicTransactionData"] = prefilled_transactional_data
        drafts = form_data.get("drafts")
        if drafts:
            # fulfill (BeneficiaryData and NegotiatorData) Drafts
            if drafts.get("beneficiaryData"):
                drafts["beneficiaryData"]["basicTransactionData"] = prefilled_transactional_data
            if drafts.get("negotiatorData"):
                drafts["negotiatorData"]["basicTransactionData"] = prefilled_transactional_data
    Task.objects.bulk_update(tasks, ["form_data"])


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0011_update_main_division_key_in_task_data'),
    ]

    operations = [
        migrations.RunPython(prefill_tasks_form_data)
    ]
