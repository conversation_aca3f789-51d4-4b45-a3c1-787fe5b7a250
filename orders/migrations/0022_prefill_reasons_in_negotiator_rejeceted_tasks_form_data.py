# Generated by Django 3.2.25 on 2024-11-07 08:49

from django.db import migrations
from orders.models.task import TaskStatus
import logging

logger = logging.getLogger("orders")


def reformat_rejected_tasks_form_data_negotiator(apps, schema_editor):
    Task = apps.get_model("orders", "Task")
    tasks = Task.objects.filter(
        status=TaskStatus.REJECTED, negotiator__isnull=False,
        form_data__negotiator__negotiatorData__isnull=False
    )
    logger.debug(
        f"reformat_rejected_tasks_form_data_negotiator: tasks_count:{tasks.count()}, tasks: {list(tasks.values_list('id', flat=True))}"
    )
    for task in tasks:
        logger.debug(
            f"reformat_rejected_tasks_form_data_negotiator: task_id: {task.id}, task_status: {task.get_status_display()}"
        )
        form_data = task.form_data
        form_data.get("negotiator", {}).get("negotiatorData", {}).get("contact", {}).update(
            reason="أخرى",
            callStatus="تم الرد",
            comment="قبل إضافة القائمة المنسدلة",
            willingnessToContinueProgram="لا يرغب في استكمال البرنامج"
        )

    Task.objects.bulk_update(tasks, ["form_data"])

class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0021_task_meta_data'),
    ]

    operations = [
        migrations.RunPython(reformat_rejected_tasks_form_data_negotiator),
    ]
