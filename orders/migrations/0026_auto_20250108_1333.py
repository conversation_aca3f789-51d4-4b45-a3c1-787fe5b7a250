# Generated by Django 3.2.25 on 2025-01-20 09:55

from django.db import migrations, models
import django.db.models.expressions


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0025_alter_task_status'),
    ]

    operations = [
        migrations.AddIndex(
            model_name='task',
            index=models.Index(django.db.models.expressions.F('form_data__customer_service__beneficiaryData__personalData__mobileNumber'), name='task_beneficiary_phone_number'),
        ),
        migrations.AddIndex(
            model_name='task',
            index=models.Index(django.db.models.expressions.F('form_data__customer_service__beneficiaryData__location__region'), name='task_beneficiary_region'),
        ),
        migrations.AddIndex(
            model_name='task',
            index=models.Index(django.db.models.expressions.F('form_data__customer_service__beneficiaryData__location__city'), name='task_beneficiary_cty'),
        ),
        migrations.AddIndex(
            model_name='task',
            index=models.Index(django.db.models.expressions.F('form_data__customer_service__beneficiaryData__location__product'), name='task_beneficiary_product'),
        ),
        migrations.AddIndex(
            model_name='task',
            index=models.Index(django.db.models.expressions.F('form_data__customer_service__beneficiaryData__professionalData__job'), name='task_beneficiary_job'),
        ),
        migrations.AddIndex(
            model_name='task',
            index=models.Index(django.db.models.expressions.F('form_data__customer_service__beneficiaryData__financialData__salaryBank'), name='task_beneficiary_salary_bank'),
        ),
        migrations.AddIndex(
            model_name='task',
            index=models.Index(django.db.models.expressions.F('form_data__customer_service__beneficiaryData__personalData__age'), name='task_beneficiary_age'),
        ),
        migrations.AddIndex(
            model_name='task',
            index=models.Index(django.db.models.expressions.F('form_data__customer_service__beneficiaryData__familyData__familyMembersCount'), name='task_beneficiary_members_count'),
        ),
        migrations.AddIndex(
            model_name='task',
            index=models.Index(django.db.models.expressions.F('form_data__customer_service__beneficiaryData__financialData__fundingAmount'), name='task_beneficiary_funding'),
        ),
    ]
