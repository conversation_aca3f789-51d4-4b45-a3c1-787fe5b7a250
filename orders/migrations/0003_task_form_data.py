# Generated by Django 3.2.25 on 2024-06-21 12:14

from django.db import migrations, models
import jsoneditor.fields.django3_jsonfield


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0002_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="task",
            name="form_data",
            field=jsoneditor.fields.django3_jsonfield.JSONField(
                blank=True, null=True, verbose_name="Form Data"
            ),
        ),
        migrations.AlterField(
            model_name="task",
            name="status",
            field=models.PositiveSmallIntegerField(
                choices=[
                    (1, "Assigned to Customer Service"),
                    (2, "In Progress"),
                    (3, "Rejected"),
                    (4, "Pending Review"),
                    (5, "Assigned to Negotiator"),
                    (6, "In Completion"),
                    (7, "Finished"),
                ],
                db_index=True,
                default=1,
                verbose_name="Status",
            ),
        ),
    ]
