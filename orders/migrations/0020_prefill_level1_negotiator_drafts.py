# Generated by Django 3.2.25 on 2024-10-30 08:11

import logging

from django.db import migrations

logger = logging.getLogger("orders")

def prefill_negotiator_level_1_drafts(apps, schema_editor):
    tasks = apps.get_model("orders", "Task").objects.filter(
        form_data__negotiator__drafts__negotiatorData__isnull=False,
        form_data__negotiator__drafts__negotiatorData__locationPreferences__isnull=True
    )
    logger.debug(
        f"prefill_negotiator_level_1_drafts: tasks count {tasks.count()}, tasks_ids: {list(tasks.values_list('id', flat=True))}")
    for task in tasks:
        form_data = task.form_data
        level_1_form_data = form_data.get("customer_service", {}).get("beneficiaryData", {})
        drafted_negotiator_form_data = form_data.get("negotiator", {}).get("drafts", {}).get("negotiatorData", {})
        negotiator_level_1_form_data = {
                "locationPreferences": level_1_form_data.get("locationPreferences") if not drafted_negotiator_form_data.get("locationPreferences") else drafted_negotiator_form_data.get("locationPreferences"),
                "financialPreferences": level_1_form_data.get("financialPreferences") if not drafted_negotiator_form_data.get("financialPreferences") else drafted_negotiator_form_data.get("financialPreferences"),
                "negotiatorContactTime": level_1_form_data.get("negotiatorContactTime") if not drafted_negotiator_form_data.get("negotiatorContactTime") else drafted_negotiator_form_data.get("negotiatorContactTime"),
                "realEstatePreferences": level_1_form_data.get("realEstatePreferences") if not drafted_negotiator_form_data.get("realEstatePreferences") else drafted_negotiator_form_data.get("realEstatePreferences"),
                "personalRealEstateData":  level_1_form_data.get("personalRealEstateData") if not drafted_negotiator_form_data.get("personalRealEstateData") else drafted_negotiator_form_data.get("personalRealEstateData"),
                "enthusiasmLevel": level_1_form_data.get("enthusiasmLevel") if not drafted_negotiator_form_data.get(
                "enthusiasmLevel") else drafted_negotiator_form_data.get("enthusiasmLevel")
        }
        drafted_negotiator_form_data.update(negotiator_level_1_form_data)
        task.save(update_fields=["form_data"])
        logger.debug(
            f"prefill_negotiator_level_1_drafts: task: {task.id}, status: {task.get_status_display()},"
        )

class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0019_prefill_level1_negotiator_tasks_form_data'),
    ]

    operations = [
        migrations.RunPython(prefill_negotiator_level_1_drafts),
    ]
