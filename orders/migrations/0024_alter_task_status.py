# Generated by Django 3.2.25 on 2024-12-12 09:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0023_alter_task_status'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='task',
            name='status',
            field=models.PositiveSmallIntegerField(choices=[(1, 'In Progress'), (3, 'Rejected'), (4, 'Pending Review'), (5, 'Finished'), (6, 'Checking Beneficiary Data'), (7, 'Review Recommendations'), (8, 'Review Offers'), (9, 'Create Deal'), (10, 'Looking For Property'), (11, 'Send Property To Beneficiary'), (12, 'Property Inspection'), (13, 'Negotiate With Owner'), (14, 'Sharing Data With Client'), (15, 'Offer Sent To Beneficiary'), (16, 'Beneficiary Accept The Offer'), (17, 'Offer Received'), (18, 'Offer Agreement'), (19, 'Second Level Preferences')], db_index=True, default=1, verbose_name='Status'),
        ),
    ]
