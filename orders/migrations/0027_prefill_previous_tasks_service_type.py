# Generated by Django 3.2.25 on 2025-03-11 12:23
import logging

from django.db import migrations

logger = logging.getLogger("orders")

def prefill_previous_tasks_service_type(apps, schema_editor):
    Task = apps.get_model("orders", "Task")
    tasks = Task.objects.only("id", "form_data")
    logger.debug(
        f"prefill_previous_tasks_service_type: tasks_count: {len(tasks)}, tasks: {list(tasks.values_list('id', flat=True))}"
    )
    for task in tasks:
        logger.debug(
            f"prefill_previous_tasks_service_type: task_id: {task.id}"
        )
        form_data = task.form_data or {}
        customer_service_form = form_data.get("customer_service", {})
        negotiator_form = form_data.get("negotiator", {})
        if negotiator_form:
            if negotiator_form.get("negotiatorData", {}):
                negotiator_form["negotiatorData"]["benificiaryServiceType"] = {"serviceType": "بحث عن عقار"}
            if negotiator_form.get("drafts", {}).get("negotiatorData", {}):
                negotiator_form["drafts"]["negotiatorData"]["benificiaryServiceType"] = {"serviceType": "بحث عن عقار"}

        if customer_service_form:
            if customer_service_form.get("beneficiaryData", {}):
                customer_service_form["beneficiaryData"]["benificiaryServiceType"] = {"serviceType": "بحث عن عقار"}
            if customer_service_form.get("drafts", {}).get("beneficiaryData", {}):
                customer_service_form["drafts"]["beneficiaryData"]["benificiaryServiceType"] = {"serviceType": "بحث عن عقار"}
        task.form_data = form_data

    Task.objects.bulk_update(tasks, ["form_data"])


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0026_auto_20250108_1333'),
    ]

    operations = [
        migrations.RunPython(prefill_previous_tasks_service_type),
    ]
