# Generated by Django 3.2.25 on 2024-09-24 12:57

from django.db import migrations


def update_financial_data(financial_data_level):
    """Helper function to update financial data for both levels."""
    if financial_data_level and financial_data_level.get("purchaseMechanism") in ["كلاهما", "كاش"]:
        if not financial_data_level.get("availableAmount"):
            financial_data_level["availableAmount"] = 0

def reformat_financial_data(apps, schema_editor):
    Task = apps.get_model('orders', 'Task')
    tasks = Task.objects.all()
    for task in tasks:
        form_data = task.form_data
        financial_data_level = form_data.get("clientPreferences", {}).get("financialPreferences", {})
        update_financial_data(financial_data_level)
        # Drafts financial preferences
        drafts = form_data.get("drafts")
        if drafts:
            financial_data_level = drafts.get("clientPreferences", {}).get("financialPreferences", {})
            update_financial_data(financial_data_level)

    # Bulk update tasks with modified form_data
    Task.objects.bulk_update(tasks, ["form_data"])



class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0015_note'),
    ]

    operations = [
        migrations.RunPython(reformat_financial_data)
    ]
