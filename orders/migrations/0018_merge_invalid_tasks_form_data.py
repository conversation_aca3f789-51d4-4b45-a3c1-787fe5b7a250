# Generated by Django 3.2.25 on 2024-10-28 11:09

import logging

from django.db import migrations
from jsonschema import Draft202012Validator

from formschemas.models import FormSchema
from orders.models import Task, TaskStatus
from formschemas.models import FormSchema
from utils.ren_calculator import RealEstateFinanceCalculator
logger = logging.getLogger("orders")

BANK_MAPPER = {
    "البنك الأهلي": "البنك الأهلي السعودي",
    "البنك العربي": "البنك العربي الوطني",
    "مصرف الإنماء": "مصرف الانماء",
    "البنك الفرنسي": "البنك السعودي الفرنسي",
    "بنك الإستثمار": "البنك السعودي للاستثمار",
}

def validate_form_data(form_schema: FormSchema,form_data: dict) -> (bool, list):
    # data field validation using jsonschema validator
    form_schema = form_schema.json_schema.get("form", {})
    v = Draft202012Validator(form_schema)
    errors = sorted(v.iter_errors(form_data), key=lambda e: e.path)
    validator_errors = [err.message for err in errors]
    if validator_errors:
        return False, validator_errors
    return True, None

def implement_deprecated_form_data(form_data: dict) -> dict:
    drafts = form_data.get("drafts", {})
    drafted_beneficiary_data: dict = drafts.get("beneficiaryData", {})
    drafted_client_preferences: dict = drafts.get("clientPreferences", {})
    beneficiary_data: dict = form_data.get("beneficiaryData", {})
    client_preferences: dict = form_data.get("clientPreferences", {})
    form_data["deprecated_beneficiaryData"] = {"drafts": drafted_beneficiary_data,
                                               "beneficiaryData": beneficiary_data}
    form_data["deprecated_clientPreferences"] = {"drafts": drafted_client_preferences,
                                                 "clientPreferences": client_preferences}
    return form_data


def reformat_each_key_formschema(form_data: dict, form_schema) -> (bool, [], dict):
    drafts = form_data.get("drafts", {})
    # extract drafts form_data if exists and merge them in 1st level form_data
    drafted_beneficiary_data: dict = drafts.get("beneficiaryData", {})
    drafted_client_preferences: dict = drafts.get("clientPreferences", {})
    level_1_drafts: dict = {"drafts": {}}

    if drafted_beneficiary_data:
        level_1_drafts["drafts"]["beneficiaryData"] = drafted_beneficiary_data
    if drafted_client_preferences:
        level_1_drafts["drafts"]["clientPreferences"] = drafted_client_preferences
    level_1_drafts = {} if not level_1_drafts.get("drafts") else level_1_drafts
    # extract all form_data in 1st level and merge them in one key
    beneficiary_data: dict = form_data.get("beneficiaryData", {})
    client_preferences: dict = form_data.get("clientPreferences", {})
    beneficiary_data.update(client_preferences)
    customer_service_data: dict = {"beneficiaryData": beneficiary_data}

    # validate form data after merge
    if form_schema:
        result, form_schema_validation = validate_form_data(form_schema=form_schema, form_data=customer_service_data["beneficiaryData"])
        if result is False:
            return False, form_schema_validation, {}
    level_1_form_data = {
        "customer_service": {**customer_service_data, **level_1_drafts},
    }

    # extract drafts form_data if exists and merge them in 2nd level form_data
    level_2_drafts: dict = {"drafts": {}}
    drafted_place_order: dict = drafts.get("placeOrder", {})
    drafted_negotiator_data: dict = drafts.get("negotiatorData", {})
    if drafted_place_order:
        level_2_drafts["drafts"]["placeOrder"] = drafted_place_order
    if drafted_negotiator_data:
        level_2_drafts["drafts"]["negotiatorData"] = drafted_negotiator_data
    level_2_drafts = {} if not level_2_drafts.get("drafts") else level_2_drafts

    # extract all form_data in 2nd level and add them in one key
    level_2_form_data = {}
    place_order: dict = form_data.get("placeOrder", {})
    negotiator_data: dict = form_data.get("negotiatorData", {})
    if negotiator_data:
        level_2_form_data = {"negotiatorData": negotiator_data}
    if place_order:
        level_2_form_data["placeOrder"] = place_order
    level_2_form_data = {"negotiator": {**level_2_form_data,  **level_2_drafts}}

    deprecated_beneficiary_data: dict = form_data.get("deprecated_beneficiaryData", {})
    deprecated_client_preferences: dict = form_data.get("deprecated_clientPreferences", {})
    form_data: dict = {
        **level_1_form_data, **level_2_form_data,
        "deprecated_beneficiaryData": deprecated_beneficiary_data,
        "deprecated_clientPreferences": deprecated_client_preferences
    }
    return True, None, form_data

def reformat_tasks_formdata(task: Task, form_schema: FormSchema):
    form_data = task.form_data
    form_data = implement_deprecated_form_data(form_data=form_data)
    # reformat form_data
    result, errors, form_data  = reformat_each_key_formschema(form_data=form_data, form_schema=form_schema)
    if result:
        task.form_data = form_data
        task.save(update_fields=["form_data"])
        logger.debug(
            f"reformat_tasks_formdata: task: {task.id}, status: {task.get_status_display()}, errors: {errors}"
        )
        return True, None, form_data
    else:
        logger.debug(
            f"reformat_tasks_formdata: task: {task.id}, status: {task.get_status_display()}, errors: {errors}"
        )
        return False, errors, form_data



def fix_bank_choices(form_data:dict):
    bank = BANK_MAPPER[form_data.get("financialData", {}).get("salaryBank")]
    form_data["financialData"].update(salaryBank=bank)
    return form_data

def fix_loan_card(form_data:dict) -> dict:
    salary = form_data["financialData"]["salary"]
    monthly_deduction = form_data["financialData"]["monthlyDeduction"]
    remaining_duration_months = form_data["financialData"]["remainingDurationMonths"]
    fund_duration = 25
    calculation = RealEstateFinanceCalculator(
        salary=salary, fund_duration=fund_duration, remaining_duration_months=remaining_duration_months,
        monthly_deduction=monthly_deduction
    ).calculation()
    installment_choice = {
        "profitMargin": float(calculation["profit_margin_finance_period"]),
        "fundingAmount": float(calculation["amount_guaranteed"]),
        "fundingPeriod": fund_duration,
        "annualPercentage": 0.0365,
        "totalAmountWithProfits": float(calculation["total_profit_amount"]),
        "installmentAfterPersonalLoan": float(calculation["monthly_inst_post_pf"]),
        "installmentDuringPersonalLoan": float(calculation["monthly_inst_during_pf"]),
    }
    form_data["financialData"]["installmentChoice"] = installment_choice
    return form_data

def fix_rejection_reason(form_data:dict) -> dict:
    form_data["contact"]["reason"] = "أخرى"
    form_data["contact"]["comment"] = "قبل إضافة القائمة المنسدلة"
    return form_data

def fix_invalid_form_data(form_data:dict) -> dict:
    if form_data.get("financialData", {}).get("salaryBank") in list(BANK_MAPPER.keys()):
        # fix bank choice
        form_data = fix_bank_choices(form_data)
    if not form_data.get("financialData", {}).get("installmentChoice"):
        # fix loan plan card
        form_data = fix_loan_card(form_data)
    if form_data.get("contact", {}).get("willingnessToContinueProgram", "") == "لا يرغب في استكمال البرنامج" and not form_data.get("contact", {}).get("reason"):
        # fix reason choice
        form_data = fix_rejection_reason(form_data)
    return form_data

def reformat_invalid_tasks_formdata(apps, schema_editor):
    tasks = apps.get_model("orders", "Task").objects.filter(form_data__customer_service__isnull=True)
    form_schema = apps.get_model("formschemas", "FormSchema").objects.filter(key="Receiving_beneficiary_data").first()
    if not form_schema:
        logger.debug("reformat_tasks_formdata: form_schema not found")
    logger.debug(
        f"reformat_invalid_tasks_formdata: tasks count {tasks.count()}, tasks_ids: {list(tasks.values_list('id', flat=True))}")
    for task in tasks:
        form_data = task.form_data or {}
        if form_data.get("beneficiaryData"):
            form_data["beneficiaryData"] = fix_invalid_form_data(form_data.get("beneficiaryData"))
        if form_data.get("negotiatorData"):
            form_data["negotiatorData"] = fix_invalid_form_data(form_data.get("negotiatorData"))
        reformat_tasks_formdata(task=task, form_schema=form_schema)

class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0017_reformat_tasks_formdata'),
    ]

    operations = [
        migrations.RunPython(reformat_invalid_tasks_formdata),
    ]
