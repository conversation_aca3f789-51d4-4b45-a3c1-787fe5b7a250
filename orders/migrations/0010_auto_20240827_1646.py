# Generated by Django 3.2 on 2024-08-27 16:46

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('orders', '0009_alter_task_status'),
    ]

    operations = [
        migrations.AlterField(
            model_name='task',
            name='customer_service',
            field=models.ForeignKey(limit_choices_to={'roles__role': 3}, on_delete=django.db.models.deletion.PROTECT, related_name='tasks', to=settings.AUTH_USER_MODEL, verbose_name='Customer Service'),
        ),
        migrations.AlterField(
            model_name='task',
            name='negotiator',
            field=models.ForeignKey(limit_choices_to={'roles__role': 4}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='negotiator_tasks', to=settings.AUTH_USER_MODEL, verbose_name='Negotiator'),
        ),
    ]
