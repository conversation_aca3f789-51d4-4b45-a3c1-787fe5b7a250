# Generated by Django 3.2 on 2024-09-08 13:56

from django.db import migrations

from utils.general_utils import get_nested_value


def update_main_division_key_in_task_data(apps, schema_editor):
    Task = apps.get_model("orders", "Task")
    tasks_level1 = Task.objects.filter(
        form_data__clientPreferences__locationPreferences__mainDivision__isnull=False
    )
    tasks_level2 = Task.objects.filter(
        form_data__placeOrder__locationPreferencesLevelTwo__mainDivision__isnull=False
    )
    for task in tasks_level1:
        main_division = get_nested_value(
            task.form_data, "clientPreferences.locationPreferences.mainDivision"
        )
        if isinstance(main_division, list):
            continue
        task.form_data["clientPreferences"]["locationPreferences"]["mainDivision"] = [
            {"id": None, "label": main_division}
        ]
    for task in tasks_level2:
        main_division = get_nested_value(
            task.form_data, "placeOrder.locationPreferencesLevelTwo.mainDivision"
        )
        if isinstance(main_division, list):
            continue
        task.form_data["placeOrder"]["locationPreferencesLevelTwo"]["mainDivision"] = [
            {"id": None, "label": main_division}
        ]
    if tasks_level1:
        Task.objects.bulk_update(tasks_level1, ["form_data"])
    if tasks_level2:
        Task.objects.bulk_update(tasks_level2, ["form_data"])


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0010_auto_20240827_1646"),
    ]

    operations = [migrations.RunPython(update_main_division_key_in_task_data)]
