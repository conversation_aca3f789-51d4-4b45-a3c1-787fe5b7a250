# Generated by Django 3.2.25 on 2024-08-11 05:52
from django.db import migrations
from django.db.models import Q


def add_beneficiary_to_task_data(apps, schema_editor):
    Task = apps.get_model("orders", "Task")
    customer_service_tasks = Task.objects.filter(
        Q(form_data__isnull=True) | Q(form_data__beneficiaryData__isnull=True),
    )
    negotiator_tasks = Task.objects.filter(
        form_data__beneficiaryData__isnull=False,
        form_data__clientPreferences__isnull=False,
        negotiator__isnull=False,
    )
    # fulfill beneficiary data in customer service tasks
    for task in customer_service_tasks:
        task.form_data = task.form_data or {}
        task.form_data["beneficiaryData"] = task.beneficiary.data

    # fulfill beneficiary data in negotiator tasks
    for task in negotiator_tasks:
        task.form_data = task.form_data or {}
        task.form_data["negotiatorData"] = task.form_data.get("beneficiaryData", {})

    Task.objects.bulk_update(negotiator_tasks, fields=["form_data"])
    Task.objects.bulk_update(customer_service_tasks, fields=["form_data"])


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0006_task_assigned_status"),
    ]

    operations = [
        migrations.RunPython(add_beneficiary_to_task_data),
    ]
