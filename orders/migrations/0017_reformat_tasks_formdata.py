# Generated by Django 3.2.25 on 2024-10-20 06:09

import logging

from django.db import migrations
from jsonschema import Draft202012Validator

from formschemas.models import FormSchema
from orders.models import Task

logger = logging.getLogger("orders")


def validate_form_data(form_schema: FormSchema,form_data: dict, task: Task) -> (bool, list):
    # data field validation using jsonschema validator
    form_schema = form_schema.json_schema.get("form", {})
    v = Draft202012Validator(form_schema)
    errors = sorted(v.iter_errors(form_data), key=lambda e: e.path)
    validator_errors = [err.message for err in errors]
    if validator_errors:
        return False, validator_errors
    return True, None

def implement_deprecated_form_data(form_data: dict) -> dict:
    drafts = form_data.get("drafts", {})
    drafted_beneficiary_data: dict = drafts.get("beneficiaryData", {})
    drafted_client_preferences: dict = drafts.get("clientPreferences", {})
    beneficiary_data: dict = form_data.get("beneficiaryData", {})
    client_preferences: dict = form_data.get("clientPreferences", {})
    form_data["deprecated_beneficiaryData"] = {"drafts": drafted_beneficiary_data,
                                               "beneficiaryData": beneficiary_data}
    form_data["deprecated_clientPreferences"] = {"drafts": drafted_client_preferences,
                                                 "clientPreferences": client_preferences}
    return form_data


def reformat_each_key_formschema(form_data: dict, task: Task, form_schema) -> (bool, [], dict):
    drafts = form_data.get("drafts", {})
    # extract drafts form_data if exists and merge them in 1st level form_data
    drafted_beneficiary_data: dict = drafts.get("beneficiaryData", {})
    drafted_client_preferences: dict = drafts.get("clientPreferences", {})
    level_1_drafts: dict = {"drafts": {}}

    if drafted_beneficiary_data:
        level_1_drafts["drafts"]["beneficiaryData"] = drafted_beneficiary_data
    if drafted_client_preferences:
        level_1_drafts["drafts"]["clientPreferences"] = drafted_client_preferences
    level_1_drafts = {} if not level_1_drafts.get("drafts") else level_1_drafts
    # extract all form_data in 1st level and merge them in one key
    beneficiary_data: dict = form_data.get("beneficiaryData", {})
    client_preferences: dict = form_data.get("clientPreferences", {})
    beneficiary_data.update(client_preferences)
    customer_service_data: dict = {"beneficiaryData": beneficiary_data}

    # validate form data after merge
    if form_schema:
        result, form_schema_validation = validate_form_data(form_schema=form_schema, form_data=customer_service_data["beneficiaryData"], task=task)
        if result is False:
            return False, form_schema_validation, {}
    level_1_form_data = {
        "customer_service": {**customer_service_data, **level_1_drafts},
    }

    # extract drafts form_data if exists and merge them in 2nd level form_data
    level_2_drafts: dict = {"drafts": {}}
    drafted_place_order: dict = drafts.get("placeOrder", {})
    drafted_negotiator_data: dict = drafts.get("negotiatorData", {})
    if drafted_place_order:
        level_2_drafts["drafts"]["placeOrder"] = drafted_place_order
    if drafted_negotiator_data:
        level_2_drafts["drafts"]["negotiatorData"] = drafted_negotiator_data
    level_2_drafts = {} if not level_2_drafts.get("drafts") else level_2_drafts

    # extract all form_data in 2nd level and add them in one key
    level_2_form_data = {}
    place_order: dict = form_data.get("placeOrder", {})
    negotiator_data: dict = form_data.get("negotiatorData", {})
    if negotiator_data:
        level_2_form_data = {"negotiatorData": negotiator_data}
    if place_order:
        level_2_form_data["placeOrder"] = place_order
    level_2_form_data = {"negotiator": {**level_2_form_data,  **level_2_drafts}}

    deprecated_beneficiary_data: dict = form_data.get("deprecated_beneficiaryData", {})
    deprecated_client_preferences: dict = form_data.get("deprecated_clientPreferences", {})
    form_data: dict = {
        **level_1_form_data, **level_2_form_data,
        "deprecated_beneficiaryData": deprecated_beneficiary_data,
        "deprecated_clientPreferences": deprecated_client_preferences
    }
    return True, None, form_data

def reformat_tasks_formdata(apps, schema_editor):
    tasks = apps.get_model("orders", "Task").objects.all()
    form_schema = apps.get_model("formschemas", "FormSchema").objects.filter(key="Receiving_beneficiary_data").first()
    tasks_errors: dict = {}
    if not form_schema:
        logger.debug("reformat_tasks_formdata: form_schema not found")
    logger.debug(
        f"reformat_tasks_formdata: tasks count {tasks.count()}, tasks_ids: {list(tasks.values_list('id', flat=True))}")
    for task in tasks:
        logger.debug(f"reformat_tasks_formdata: task_id: {task.id}")
        form_data = task.form_data or {}
        # add deprecated form_data before merge two forms
        form_data = implement_deprecated_form_data(form_data=form_data)
        # reformat form_data
        result, errors, form_data  = reformat_each_key_formschema(form_data=form_data, task=task, form_schema=form_schema)
        if result:
            task.form_data = form_data
            task.save(update_fields=["form_data"])
        else:
            tasks_errors[task.id] = errors

    if tasks_errors:
        logger.debug(
            f"reformat_tasks_formdata: errors count: {len(tasks_errors)}, tasks errors {tasks_errors}")

class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0016_ajust_financial_preferences'),
    ]

    operations = [
        migrations.RunPython(reformat_tasks_formdata),
    ]
