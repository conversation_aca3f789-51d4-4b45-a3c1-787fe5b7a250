# Generated by Django 3.2.25 on 2024-10-29 10:08

import logging

from django.db import migrations
from jsonschema import Draft202012Validator

from formschemas.models import FormSchema

logger = logging.getLogger("orders")


def validate_form_data(form_schema: FormSchema,form_data: dict) -> (bool, list):
    # data field validation using jsonschema validator
    form_schema = form_schema.json_schema.get("form", {})
    v = Draft202012Validator(form_schema)
    errors = sorted(v.iter_errors(form_data), key=lambda e: e.path)
    validator_errors = [err.message for err in errors]
    if validator_errors:
        return False, validator_errors
    return True, None

def prefill_negotiator_level_1(apps, schema_editor):
    tasks = apps.get_model("orders", "Task").objects.filter(
        form_data__negotiator__negotiatorData__isnull=False,
        form_data__negotiator__negotiatorData__locationPreferences__isnull=True
    )
    form_schema = apps.get_model("formschemas", "FormSchema").objects.filter(key="negotiation-updated-data").first()
    if not form_schema:
        logger.debug("prefill_negotiator_level_1: form_schema not found")
    logger.debug(
        f"prefill_negotiator_level_1: tasks count {tasks.count()}, tasks_ids: {list(tasks.values_list('id', flat=True))}")
    for task in tasks:
        form_data = task.form_data
        level_1_form_data = form_data.get("customer_service", {}).get("beneficiaryData", {})
        negotiator_form_data = form_data["negotiator"]["negotiatorData"]
        drafted_negotiator_form_data = form_data.get("negotiator", {}).get("draft", {}).get("negotiatorData")
        negotiator_level_1_form_data = {
                "locationPreferences": level_1_form_data.get("locationPreferences") if not negotiator_form_data.get("locationPreferences") else negotiator_form_data.get("locationPreferences"),
                "financialPreferences": level_1_form_data.get("financialPreferences") if not negotiator_form_data.get("financialPreferences") else negotiator_form_data.get("financialPreferences"),
                "negotiatorContactTime": level_1_form_data.get("negotiatorContactTime") if not negotiator_form_data.get("negotiatorContactTime") else negotiator_form_data.get("negotiatorContactTime"),
                "realEstatePreferences": level_1_form_data.get("realEstatePreferences") if not negotiator_form_data.get("realEstatePreferences") else negotiator_form_data.get("realEstatePreferences"),
                "personalRealEstateData":  level_1_form_data.get("personalRealEstateData") if not negotiator_form_data.get("personalRealEstateData") else negotiator_form_data.get("personalRealEstateData"),
                "enthusiasmLevel": level_1_form_data.get("enthusiasmLevel") if not negotiator_form_data.get(
                "enthusiasmLevel") else negotiator_form_data.get("enthusiasmLevel")
        }
        negotiator_form_data.update(negotiator_level_1_form_data)
        if drafted_negotiator_form_data:
            negotiator_form_data.update(negotiator_level_1_form_data)
        result, form_schema_validation = validate_form_data(form_schema=form_schema, form_data=negotiator_form_data)
        if result:
            task.save(update_fields=["form_data"])
        logger.debug(
            f"prefill_negotiator_level_1: task: {task.id}, status: {task.get_status_display()}, errors: {form_schema_validation}"
        )

class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0018_merge_invalid_tasks_form_data'),
    ]

    operations = [
        migrations.RunPython(prefill_negotiator_level_1),
    ]
