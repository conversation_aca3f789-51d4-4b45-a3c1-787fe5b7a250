# Generated by Django 3.2.25 on 2024-09-10 12:33

from django.db import migrations

from deals.models import Beneficiary


def update_beneficiary_data(apps, schema_editor):
    beneficiaries = Beneficiary.objects.all()
    for beneficiary in beneficiaries:
        beneficiary_data = beneficiary.data.get("financialData")
        beneficiary_data["funder1"] = beneficiary_data.pop("funder", None)

    Beneficiary.objects.bulk_update(beneficiaries, ["data"])


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0013_update_locations_preferences'),
    ]

    operations = [
        migrations.RunPython(update_beneficiary_data),
    ]
