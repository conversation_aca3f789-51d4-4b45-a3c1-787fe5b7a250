# Generated by Django 3.2.25 on 2024-06-20 15:01

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("deals", "0001_initial"),
        ("orders", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="task",
            name="customer_service",
            field=models.ForeignKey(
                limit_choices_to={"role": 3},
                on_delete=django.db.models.deletion.PROTECT,
                related_name="tasks",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Customer Service",
            ),
        ),
        migrations.AddField(
            model_name="task",
            name="negotiator",
            field=models.ForeignKey(
                limit_choices_to={"role": 4},
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="negotiator_tasks",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Negotiator",
            ),
        ),
        migrations.AddField(
            model_name="task",
            name="order",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="tasks",
                to="orders.order",
                verbose_name="Order",
            ),
        ),
        migrations.AddField(
            model_name="order",
            name="beneficiaries",
            field=models.ManyToManyField(
                to="deals.Beneficiary", verbose_name="Beneficiaries"
            ),
        ),
    ]
