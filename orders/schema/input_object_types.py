import graphene

from orders.models import TaskStatus
from users.models import UserRoleChoices

RenRolesEnum = graphene.Enum.from_enum(UserRoleChoices)

TaskStatusEnum = graphene.Enum.from_enum(TaskStatus)


class CreateOrderInput(graphene.InputObjectType):
    beneficiaries = graphene.List(graphene.Int, required=True)
    customer_service = graphene.Int(required=True)
    start_date = graphene.DateTime(required=True)
    end_date = graphene.DateTime(required=True)


class UpdateTaskInput(graphene.InputObjectType):
    task_id = graphene.Int(required=True)
    status = graphene.Argument(TaskStatusEnum, required=False)
    form_data = graphene.JSONString(required=True)
    form_schema_key = graphene.String(required=True)
    is_draft = graphene.Boolean(required=True)


class AssignTaskInput(graphene.InputObjectType):
    task_id = graphene.Int(required=True)


class ReassignTaskInput(graphene.InputObjectType):
    tasks_ids = graphene.List(graphene.Int, required=True)
    user_id = graphene.Int(required=True)


class AssignTaskToNegotiatorInput(graphene.InputObjectType):
    task_ids = graphene.List(graphene.Int, required=True)
    negotiator_id = graphene.Int(required=True)


class CloseTaskInput(graphene.InputObjectType):
    task_id = graphene.Int(required=True)
    form_data = graphene.JSONString(required=True)
    form_schema_key = graphene.String(required=True)


class CreateNoteInput(graphene.InputObjectType):
    task_id = graphene.Int(required=True)
    note = graphene.String(required=True)


class Level(graphene.Enum):
    CITY = "city"
    DISTRICT = "district"
