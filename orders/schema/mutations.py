import logging
import os
from urllib.parse import urljoin

import graphene
from django.conf import settings
from django.contrib.auth import get_user_model
from django.db import transaction
from django.utils.crypto import get_random_string
from django.utils.translation import gettext_lazy as _
from gabbro.graphene.exceptions import NotFound

from orders.models import Task
from orders.permissions import (
    CreateOrderPerms,
    UpdateCustomerServiceTaskPerms,
    ReassignToNegotiatorTaskPerms,
    ReassignToCustomerServiceTaskPerms,
    DeleteTaskPerms,
    AssignTaskToProjectManagerTaskPerms,
    AssignTaskToNegotiatorTaskPerms,
    RejectTaskPerms,
    CreateNotePerms,
    UpdateNegotiatorTaskPerms,
)
from orders.schema.input_object_types import (
    CreateOrderInput,
    UpdateTaskInput,
    AssignTaskInput,
    CloseTaskInput,
    AssignTaskToNegotiatorInput,
    ReassignTaskInput,
    CreateNoteInput,
)
from orders.schema.object_types import OrderType, TaskType, NoteType
from orders.services import OrderService, TaskService, NoteService
from orders.strategies import (
    ReassignToNegotiatorTaskStrategy,
    CreateOrderStrategy,
    ReassignToCustomerServiceTaskStrategy,
    UpdateCustomerServiceTaskStrategy,
    AssignTaskToProjectManagerStrategy,
    AssignTaskToNegotiatorStrategy,
    RejectTaskStrategy,
    DeleteTaskStrategy,
    CreateNoteStrategy,
    UpdateNegotiatorTaskStrategy,
)
from orders.validators import (
    CreateOrderValidation,
    AssignTaskToNegotiatorValidation,
    ReassignToCustomerServiceTaskValidation,
    AssignTaskToProjectManagerValidation,
    UpdateTaskValidation,
    RejectTaskValidation,
    DeleteTaskValidation,
    ReassignToNegotiatorTaskValidation,
    CreateNoteValidation,
)
from utils.general_utils import generate_upload_signed_url_v4
from utils.graphene.decorators import authentication_required

MEDIA_URL = getattr(settings, "MEDIA_URL")

logger = logging.getLogger("orders")
User = get_user_model()


class CreateOrderMutation(graphene.Mutation):
    class Arguments:
        order_input = CreateOrderInput()

    order = graphene.Field(OrderType)

    @staticmethod
    @authentication_required
    def mutate(root, info, order_input):
        user = info.context.user
        order_service = OrderService(
            strategy=CreateOrderStrategy(),
            perms=CreateOrderPerms(),
            validations=CreateOrderValidation(),
        )
        order = order_service.create_order(user=user, order_input=order_input)
        return CreateOrderMutation(order=order)


class UpdateCustomerServiceTaskMutation(graphene.Mutation):
    class Arguments:
        task_input = UpdateTaskInput()

    task = graphene.Field(TaskType)

    @staticmethod
    @authentication_required
    def mutate(root, info, task_input: dict):
        user = info.context.user
        task_service = TaskService(
            strategy=UpdateCustomerServiceTaskStrategy(),
            perms=UpdateCustomerServiceTaskPerms(),
            validations=UpdateTaskValidation(),
        )
        task = task_service.update_task(task_input=task_input, user=user)
        return UpdateCustomerServiceTaskMutation(task=task)


class UpdateNegotiatorTaskMutation(graphene.Mutation):
    class Arguments:
        task_input = UpdateTaskInput()

    task = graphene.Field(TaskType)

    @staticmethod
    @authentication_required
    def mutate(root, info, task_input: dict):
        user = info.context.user
        task_service = TaskService(
            strategy=UpdateNegotiatorTaskStrategy(),
            perms=UpdateNegotiatorTaskPerms(),
            validations=UpdateTaskValidation(),
        )
        task = task_service.update_task(task_input=task_input, user=user)
        return UpdateNegotiatorTaskMutation(task=task)


class AssignTaskToProjectManagerMutation(graphene.Mutation):
    class Arguments:
        task_input = AssignTaskInput()

    task = graphene.Field(TaskType)

    @staticmethod
    @authentication_required
    def mutate(root, info, task_input: dict):
        user = info.context.user
        task_service = TaskService(
            strategy=AssignTaskToProjectManagerStrategy(),
            perms=AssignTaskToProjectManagerTaskPerms(),
            validations=AssignTaskToProjectManagerValidation(),
        )
        task = task_service.update_task(task_input=task_input, user=user)
        return AssignTaskToProjectManagerMutation(task=task)


class AssignTaskToNegotiatorMutation(graphene.Mutation):
    class Arguments:
        task_input = AssignTaskToNegotiatorInput()

    tasks = graphene.List(TaskType)

    @staticmethod
    @authentication_required
    def mutate(root, info, task_input: dict):
        user = info.context.user
        task_service = TaskService(
            strategy=AssignTaskToNegotiatorStrategy(),
            perms=AssignTaskToNegotiatorTaskPerms(),
            validations=AssignTaskToNegotiatorValidation(),
        )
        tasks = task_service.update_task(task_input=task_input, user=user)
        return AssignTaskToNegotiatorMutation(tasks=tasks)


class RejectTaskMutation(graphene.Mutation):
    class Arguments:
        task_input = CloseTaskInput()

    task = graphene.Field(TaskType)

    @staticmethod
    @authentication_required
    def mutate(root, info, task_input: dict):
        user = info.context.user
        task_service = TaskService(
            strategy=RejectTaskStrategy(),
            perms=RejectTaskPerms(),
            validations=RejectTaskValidation(),
        )
        task = task_service.update_task(user=user, task_input=task_input)
        return RejectTaskMutation(task=task)


class DeleteTaskMutation(graphene.Mutation):
    class Arguments:
        task_id = graphene.Int(required=True)

    status = graphene.String(required=True)

    @staticmethod
    @authentication_required
    def mutate(root, info, task_id: int):
        user = info.context.user
        task_service = TaskService(
            strategy=DeleteTaskStrategy(),
            perms=DeleteTaskPerms(),
            validations=DeleteTaskValidation(),
        )
        task_service.update_task(user=user, task_input={"task_id": task_id})
        return DeleteTaskMutation(status=_("Task Deleted Successfully") % {})


class ReassignTaskToCustomerServiceMutation(graphene.Mutation):
    class Arguments:
        task_input = ReassignTaskInput(required=True)

    tasks = graphene.List(TaskType)

    @staticmethod
    @authentication_required
    @transaction.atomic
    def mutate(root, info, task_input):
        user = info.context.user
        task_service = TaskService(
            strategy=ReassignToCustomerServiceTaskStrategy(),
            perms=ReassignToCustomerServiceTaskPerms(),
            validations=ReassignToCustomerServiceTaskValidation(),
        )
        tasks = task_service.update_task(user=user, task_input=task_input)
        logger.debug(
            f"[ReassignTaskToCustomerServiceMutation] task updated with ids: {list(tasks.values_list('id', flat=True))}"
        )
        return ReassignTaskToCustomerServiceMutation(tasks=tasks)


class ReassignTaskToNegotiatorMutation(graphene.Mutation):
    class Arguments:
        task_input = ReassignTaskInput(required=True)

    tasks = graphene.List(TaskType)

    @staticmethod
    @authentication_required
    @transaction.atomic
    def mutate(root, info, task_input):
        user = info.context.user
        task_service = TaskService(
            strategy=ReassignToNegotiatorTaskStrategy(),
            perms=ReassignToNegotiatorTaskPerms(),
            validations=ReassignToNegotiatorTaskValidation(),
        )
        tasks = task_service.update_task(user=user, task_input=task_input)
        logger.debug(
            f"[ReassignTaskToNegotiatorMutation] task updated with ids: {list(tasks.values_list('id', flat=True))}"
        )
        return ReassignTaskToNegotiatorMutation(tasks=tasks)


class CreateNoteMutation(graphene.Mutation):
    class Arguments:
        note_input = CreateNoteInput(required=True)

    note = graphene.Field(NoteType)

    @staticmethod
    @authentication_required
    def mutate(root, info, note_input: CreateNoteInput):
        user = info.context.user
        note_service = NoteService(
            strategy=CreateNoteStrategy(),
            perms=CreateNotePerms(),
            validations=CreateNoteValidation(),
        )
        note = note_service.create_note(user=user, note_input=note_input)
        logger.debug(f"[CreateNoteMutation] note created with id: {note.id}")
        return CreateNoteMutation(note=note)


class SignedURL(graphene.Mutation):
    class Arguments:
        task_id = graphene.Int(required=True)
        file_extension = graphene.String(required=True)

    signed_url = graphene.String()
    blob_url = graphene.String()

    @staticmethod
    @authentication_required
    def mutate(root, info, task_id, file_extension):
        task = Task.objects.filter(id=task_id).first()
        if not task:
            raise NotFound(
                reason={
                    "task_id": _("invalid %(task_id)s task id") % {"task_id": task_id}
                }
            )
        file_name = f"{get_random_string(64)}.{file_extension}"
        blob_name = os.path.join(
            "media",
            "attachments",
            "task",
            str(task_id),
            file_name,
        )
        blob_url = urljoin(MEDIA_URL, blob_name)
        signed_url = generate_upload_signed_url_v4(blob_name)
        return SignedURL(signed_url=signed_url, blob_url=blob_url)


class BeneficiariesSignedURL(graphene.Mutation):
    class Arguments:
        file_extension = graphene.String(required=True)

    signed_url = graphene.String()
    blob_url = graphene.String()

    @staticmethod
    @authentication_required
    def mutate(root, info, file_extension):
        user = info.context.user
        # check user permission
        CreateOrderPerms().check_permissions(user=user, context={})
        file_name = f"{get_random_string(20)}.{file_extension}"
        blob_name = os.path.join(
            "media",
            "attachments",
            "beneficiaries",
            file_name,
        )
        blob_url = urljoin(MEDIA_URL, blob_name)
        signed_url = generate_upload_signed_url_v4(blob_name)
        return BeneficiariesSignedURL(signed_url=signed_url, blob_url=blob_url)


class Mutation(graphene.ObjectType):
    create_order_mutation = CreateOrderMutation.Field()
    update_customer_service_task_mutation = UpdateCustomerServiceTaskMutation.Field()
    update_negotiator_task_mutation = UpdateNegotiatorTaskMutation.Field()
    reject_task_mutation = RejectTaskMutation.Field()
    assign_task_to_project_manager = AssignTaskToProjectManagerMutation.Field()
    assign_task_to_negotiator = AssignTaskToNegotiatorMutation.Field()
    delete_task_mutation = DeleteTaskMutation.Field()
    reassign_task_to_customer_service = ReassignTaskToCustomerServiceMutation.Field()
    reassign_task_to_negotiator = ReassignTaskToNegotiatorMutation.Field()
    create_note = CreateNoteMutation.Field()
    signed_url = SignedURL.Field()
    beneficiaries_signed_url = BeneficiariesSignedURL.Field()
