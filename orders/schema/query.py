import graphene
from django.db.models import Count, Max, F, Q

from orders.models.order import Order
from orders.models.task import Task, TaskStatus, TaskAssignedStatus
from orders.permissions import (
    CreateOrderPerms,
    RecommendationsPerms,
    CreateNotePerms,
)
from orders.schema.input_object_types import Level, RenRolesEnum
from orders.schema.object_types import (
    OrderListType,
    TaskListType,
    RecommendationsType,
    NoteListType,
    JsonType,
    IndicatorsEnum,
)
from orders.schema.utils import (
    makan_make_request,
    get_task,
    get_districts_ids,
    get_zones_ids,
    add_district_geometry,
    add_district_name,
)
from orders.services.recommendations import RecommendationsService
from orders.strategies import ListRecommendationStrategy
from orders.validators import RecommendationValidation, UpdateTaskValidation
from users.models import UserRoleChoices
from utils.general_utils import get_nested_value
from utils.graphene.decorators import authentication_required
from utils.graphene.query import (
    PageInfo,
    DjangoFilterInput,
    filter_qs_paginate_with_count,
    build_q,
)

TASK_QUERYSET_MAPPER = {
    "superuser": lambda _user: Task.objects.select_related(
        "customer_service", "beneficiary"
    ),
    UserRoleChoices.ADMIN: lambda _user: Task.objects.select_related(
        "customer_service", "beneficiary"
    ),
    UserRoleChoices.PROJECT_MANAGERS: lambda _user: Task.objects.select_related(
        "customer_service", "beneficiary"
    ),
    UserRoleChoices.CUSTOMER_SERVICES: lambda _user: Task.objects.filter(
        customer_service=_user,
    ).select_related("customer_service", "beneficiary"),
    UserRoleChoices.NEGOTIATORS: lambda _user: Task.objects.filter(
        negotiator=_user,
    ).select_related("customer_service", "beneficiary", "negotiator"),
}


class Query(graphene.ObjectType):
    orders = graphene.Field(
        OrderListType,
        pk=graphene.Int(),
        page_info=PageInfo(),
        filters=graphene.List(DjangoFilterInput),
    )
    tasks = graphene.Field(
        TaskListType,
        pk=graphene.Int(),
        role=graphene.Argument(RenRolesEnum, required=True),
        page_info=PageInfo(),
        filters=graphene.List(DjangoFilterInput),
    )
    rejected_tasks = graphene.Field(
        TaskListType,
        pk=graphene.Int(),
        page_info=PageInfo(),
        filters=graphene.List(DjangoFilterInput),
    )
    recommendations = graphene.Field(
        RecommendationsType,
        level=Level(required=True),
        task_id=graphene.Int(required=True),
    )
    notes = graphene.Field(
        NoteListType,
        page_info=PageInfo(),
        filters=graphene.List(DjangoFilterInput),
        task_id=graphene.Int(required=True),
        pk=graphene.Int(),
    )

    # Makan Integration
    get_moj_indicators = graphene.Field(
        JsonType,
        task_id=graphene.Int(required=True),
        indicator_type=IndicatorsEnum(required=True),
    )

    get_moj_three_months_moving_average = graphene.Field(
        JsonType,
        task_id=graphene.Int(required=True),
        indicator_type=IndicatorsEnum(required=True),
    )

    get_moj_yearly_percentage_difference_by_price_per_m2 = graphene.Field(
        JsonType,
        task_id=graphene.Int(required=True),
    )

    get_moj_top_ten_districts = graphene.Field(
        JsonType,
        task_id=graphene.Int(required=True),
        indicator_type=IndicatorsEnum(required=True),
    )

    get_moj_indicators_on_district_level = graphene.Field(
        JsonType,
        task_id=graphene.Int(required=True),
        indicator_type=IndicatorsEnum(required=True),
    )

    get_moj_modality_by_price_per_m2 = graphene.Field(
        JsonType,
        task_id=graphene.Int(required=True),
    )

    get_moj_modality_distribution_by_price_per_m2 = graphene.Field(
        JsonType,
        task_id=graphene.Int(required=True),
    )

    @staticmethod
    @authentication_required
    def resolve_orders(root, info, pk: int = None, page_info=None, filters=None):
        """
        - Retrieve all orders
        """
        user = info.context.user
        CreateOrderPerms().check_permissions(user, context={})

        queryset = (
            Order.objects.prefetch_related("beneficiaries")
            .annotate(
                last_task_update=Max("tasks__modified"),
                rejected_tasks=Count(
                    "tasks", filter=Q(tasks__status=TaskStatus.REJECTED)
                ),
                pending_review_tasks=Count(
                    "tasks", filter=Q(tasks__status=TaskStatus.PENDING_REVIEW.value)
                ),
                assigned_to_customer_services_task=Count(
                    "tasks",
                    filter=Q(
                        tasks__status=TaskStatus.IN_PROGRESS,
                        tasks__assigned_status=TaskAssignedStatus.ASSIGNED_TO_CUSTOMER_SERVICE.value,
                    ),
                ),
                assigned_to_negotiator_task=Count(
                    "tasks",
                    filter=Q(
                        tasks__assigned_status=TaskAssignedStatus.ASSIGNED_TO_NEGOTIATOR.value,
                    ),
                ),
                total_tasks=Count("tasks"),
            )
            .filter(~Q(rejected_tasks=F("total_tasks")))
            .order_by("-last_task_update")
        )
        return OrderListType(
            *filter_qs_paginate_with_count(queryset, build_q(pk, filters), page_info)
        )

    @staticmethod
    @authentication_required
    def resolve_tasks(
        root, info, role: UserRoleChoices, pk: int = None, page_info=None, filters=None
    ):
        """
        - Retrieve all Tasks
        """
        user = info.context.user
        queryset = TASK_QUERYSET_MAPPER[role](user)
        return TaskListType(
            *filter_qs_paginate_with_count(queryset, build_q(pk, filters), page_info)
        )

    @staticmethod
    @authentication_required
    def resolve_rejected_tasks(
        root, info, pk: int = None, page_info=None, filters=None
    ):
        """
        - Retrieve all Rejected Tasks
        """
        user = info.context.user
        CreateOrderPerms().check_permissions(user, context={})
        non_rejected_task_status = [
            status[0]
            for status in TaskStatus.choices
            if status[0] != TaskStatus.REJECTED.value
        ]
        queryset = Task.objects.exclude(
            beneficiary__tasks__status__in=non_rejected_task_status
        )
        return TaskListType(
            *filter_qs_paginate_with_count(queryset, build_q(pk, filters), page_info)
        )

    @staticmethod
    @authentication_required
    def resolve_recommendations(root, info, level, task_id: int):
        user = info.context.user
        service = RecommendationsService(
            validations=RecommendationValidation(),
            perms=RecommendationsPerms(),
            strategy=ListRecommendationStrategy(),
        )

        makan_data = service.resolve_recommendations(
            input_data={"task_id": task_id, "level": level}, user=user
        )
        return RecommendationsType(data=makan_data)

    @staticmethod
    @authentication_required
    def resolve_notes(
        root, info, task_id: int, pk: int = None, page_info=None, filters=None
    ):
        user = info.context.user
        task: Task = UpdateTaskValidation().get_task_if_exists(filters={"id": task_id})
        CreateNotePerms().check_permissions(user=user, context={"task": task})
        queryset = task.notes.all()
        return NoteListType(
            *filter_qs_paginate_with_count(queryset, build_q(pk, filters), page_info)
        )

    @staticmethod
    @authentication_required
    def resolve_get_moj_indicators(root, info, task_id: int, indicator_type):
        task = get_task(task_id, info.context.user)
        districts = get_districts_ids(task)
        zones = get_zones_ids(task)
        property_type = get_nested_value(
            task.form_data,
            "customer_service.beneficiaryData.realEstatePreferences.preferredPropertyType",
        )
        data = {
            "input": {
                "real_estate_types": [
                    property_type if property_type == "شقه" else "عام"
                ],
                "transaction_types": [
                    "صفقة"
                    if get_nested_value(
                        task.form_data,
                        "customer_service.beneficiaryData.benificiaryServiceType.serviceType",
                    )
                    == "بحث عن عقار"
                    else "صفقة بتمويل"
                ],
                "cities_ids": [
                    get_nested_value(
                        task.form_data,
                        "customer_service.beneficiaryData.locationPreferences.cityId",
                    )
                ],
                "zones_ids": zones,
                "districts_ids": districts,
                "metric_type": indicator_type,
            },
            "skus": ["GET_MOJ_INDICATORS"],
        }
        url = "get_moj_indicators/"
        response, error = makan_make_request(data, url)
        response = response.get("get_moj_indicators", {}).get("data", {})
        return JsonType(response, error)

    @staticmethod
    @authentication_required
    def resolve_get_moj_three_months_moving_average(
        root, info, task_id: int, indicator_type
    ):
        task = get_task(task_id, info.context.user)
        districts = get_districts_ids(task)
        zones = get_zones_ids(task)
        property_type = get_nested_value(
            task.form_data,
            "customer_service.beneficiaryData.realEstatePreferences.preferredPropertyType",
        )

        data = {
            "input": {
                "real_estate_types": [
                    property_type if property_type == "شقه" else "عام"
                ],
                "transaction_types": [
                    "صفقة"
                    if get_nested_value(
                        task.form_data,
                        "customer_service.beneficiaryData.benificiaryServiceType.serviceType",
                    )
                    == "بحث عن عقار"
                    else "صفقة بتمويل"
                ],
                "cities_ids": [
                    get_nested_value(
                        task.form_data,
                        "customer_service.beneficiaryData.locationPreferences.cityId",
                    )
                ],
                "zones_ids": zones,
                "districts_ids": districts,
                "metric_type": indicator_type,
            },
            "skus": ["GET_MOJ_THREE_MONTHS_MOVING_AVERAGE"],
        }
        url = "get_moj_three_months_moving_average/"
        response, error = makan_make_request(data, url)
        response = response.get("get_moj_three_months_moving_average", {}).get(
            "data", {}
        )
        return JsonType(response, error)

    @staticmethod
    @authentication_required
    def resolve_get_moj_yearly_percentage_difference_by_price_per_m2(
        root, info, task_id: int
    ):
        task = get_task(task_id, info.context.user)
        districts = get_districts_ids(task)
        zones = get_zones_ids(task)
        property_type = get_nested_value(
            task.form_data,
            "customer_service.beneficiaryData.realEstatePreferences.preferredPropertyType",
        )
        data = {
            "input": {
                "real_estate_types": [
                    property_type if property_type == "شقه" else "عام"
                ],
                "transaction_types": [
                    "صفقة"
                    if get_nested_value(
                        task.form_data,
                        "customer_service.beneficiaryData.benificiaryServiceType.serviceType",
                    )
                    == "بحث عن عقار"
                    else "صفقة بتمويل"
                ],
                "cities_ids": [
                    get_nested_value(
                        task.form_data,
                        "customer_service.beneficiaryData.locationPreferences.cityId",
                    )
                ],
                "zones_ids": zones,
                "districts_ids": districts,
                "metric_type": "median_price_per_m2",
            },
            "skus": ["GET_MOJ_YEARLY_PERCENTAGE_DIFFERENCE_BY_PRICE_PER_M2"],
        }

        url = "get_moj_yearly_percentage_difference_by_price_per_m2/"
        response, error = makan_make_request(data, url)
        response = response.get(
            "get_moj_yearly_percentage_difference_by_price_per_m2", {}
        ).get("data", {})
        return JsonType(response, error)

    @staticmethod
    @authentication_required
    def resolve_get_moj_top_ten_districts(root, info, task_id: int, indicator_type):
        task = get_task(task_id, info.context.user)
        districts = get_districts_ids(task)
        zones = get_zones_ids(task)
        property_type = get_nested_value(
            task.form_data,
            "customer_service.beneficiaryData.realEstatePreferences.preferredPropertyType",
        )
        data = {
            "input": {
                "real_estate_types": [
                    property_type if property_type == "شقه" else "عام"
                ],
                "transaction_types": [
                    "صفقة"
                    if get_nested_value(
                        task.form_data,
                        "customer_service.beneficiaryData.benificiaryServiceType.serviceType",
                    )
                    == "بحث عن عقار"
                    else "صفقة بتمويل"
                ],
                "cities_ids": [
                    get_nested_value(
                        task.form_data,
                        "customer_service.beneficiaryData.locationPreferences.cityId",
                    )
                ],
                "zones_ids": zones,
                "districts_ids": districts,
                "metric_type": indicator_type,
            },
            "skus": ["GET_MOJ_TOP_TEN_DISTRICTS"],
        }
        url = "get_moj_top_ten_districts/"
        response, error = makan_make_request(data, url)
        response = response.get("get_moj_top_ten_districts", {}).get("data", {})
        response = add_district_name(response)
        return JsonType(response, error)

    @staticmethod
    @authentication_required
    def resolve_get_moj_indicators_on_district_level(
        root, info, task_id: int, indicator_type
    ):
        task = get_task(task_id, info.context.user)
        districts = get_districts_ids(task)
        zones = get_zones_ids(task)
        property_type = get_nested_value(
            task.form_data,
            "customer_service.beneficiaryData.realEstatePreferences.preferredPropertyType",
        )
        data = {
            "input": {
                "real_estate_types": [
                    property_type if property_type == "شقه" else "عام"
                ],
                "transaction_types": [
                    "صفقة"
                    if get_nested_value(
                        task.form_data,
                        "customer_service.beneficiaryData.benificiaryServiceType.serviceType",
                    )
                    == "بحث عن عقار"
                    else "صفقة بتمويل"
                ],
                "cities_ids": [
                    get_nested_value(
                        task.form_data,
                        "customer_service.beneficiaryData.locationPreferences.cityId",
                    )
                ],
                "zones_ids": zones,
                "districts_ids": districts,
                "metric_type": indicator_type,
            },
            "skus": ["GET_MOJ_INDICATORS_ON_DISTRICT_LEVEL"],
        }
        url = "get_moj_indicators_on_district_level/"
        response, error = makan_make_request(data, url)
        response = response.get("get_moj_indicators_on_district_level", {}).get(
            "data", {}
        )
        response = add_district_geometry(response)
        response = add_district_name(response)
        return JsonType(response, error)

    @staticmethod
    @authentication_required
    def resolve_get_moj_modality_by_price_per_m2(root, info, task_id: int):
        task = get_task(task_id, info.context.user)
        districts = get_districts_ids(task)
        data = {
            "input": {
                "cities_ids": [
                    get_nested_value(
                        task.form_data,
                        "customer_service.beneficiaryData.locationPreferences.cityId",
                    )
                ],
                "districts_ids": districts,
            },
            "skus": ["GET_MOJ_MODALITY_BY_PRICE_PER_M2"],
        }
        url = "get_moj_modality_by_price_per_m2/"
        response, error = makan_make_request(data, url)
        response = response.get("get_moj_modality_by_price_per_m2", {}).get("data", {})
        response = add_district_name(response)
        return JsonType(response, error)

    @staticmethod
    @authentication_required
    def resolve_get_moj_modality_distribution_by_price_per_m2(root, info, task_id: int):
        task = get_task(task_id, info.context.user)
        districts = get_districts_ids(task)
        data = {
            "input": {
                "cities_ids": [
                    get_nested_value(
                        task.form_data,
                        "customer_service.beneficiaryData.locationPreferences.cityId",
                    )
                ],
                "districts_ids": districts,
            },
            "skus": ["GET_MOJ_MODALITY_DISTRIBUTION_BY_PRICE_PER_M2"],
        }
        url = "get_moj_modality_distribution_by_price_per_m2/"
        response, error = makan_make_request(data, url)
        response = response.get(
            "get_moj_modality_distribution_by_price_per_m2", {}
        ).get("data", {})
        return JsonType(response, error)
