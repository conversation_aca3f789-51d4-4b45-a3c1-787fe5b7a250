import json
from urllib.parse import urljoin
from django.utils.translation import gettext_lazy as _

from gabbro.graphene import NotFound
from gabbro.makan_client import Makan<PERSON>lient

from deals.models import Record
from orders.models import Task
from utils.general_utils import get_nested_value


def makan_make_request(data: dict, url: str):
    data = data
    makan_client = MakanClient()
    url = urljoin(makan_client.api_url, url)
    res, err = makan_client._make_request("POST", url, data=json.dumps(data))
    if res:
        res = res.json()
    else:
        res = {}
    return res, err


def get_task(task_id: int, user):
    task = Task.objects.filter(id=task_id, negotiator=user).first()
    if task is None:
        raise NotFound(
            reason={"task": _("task %(task_id)s not found") % {"task_id": task_id}}
        )
    return task


def get_districts_ids(task):
    districts = []
    districts_list = get_nested_value(
        task.form_data,
        "customer_service.beneficiaryData.locationPreferences.district",
    )
    for district in districts_list if districts_list else []:
        if district["id"] is not None:
            districts.append(district["id"])
    return districts


def get_zones_ids(task):
    zones = []
    zones_list = get_nested_value(
        task.form_data,
        "customer_service.beneficiaryData.locationPreferences.mainDivision",
    )
    for zone in zones_list if zones_list else []:
        if zone["id"] is not None:
            zones.append(zone["id"])
    return zones


def add_district_geometry(response: dict):
    districts_id = [entry.get("district_id", None) for entry in response]
    records = Record.objects.filter(
        layer__key="districts", source_properties__id__in=districts_id
    )
    geometry_map = {
        record.source_properties["id"]: json.loads(record.geometry.geojson)[
            "geometries"
        ][0]
        for record in records
    }
    for entry in response:
        district_id = entry.get("district_id", None)
        entry["geometry"] = geometry_map.get(
            district_id, None
        )  # Default to None if not found
    return response


def add_district_name(response: dict):
    districts_id = [entry.get("district_id", None) for entry in response]
    records = Record.objects.filter(
        layer__key="districts", source_properties__id__in=districts_id
    )
    names_map = {
        record.source_properties["id"]: record.source_properties.get(
            "district_name", ""
        )
        for record in records
    }
    for entry in response:
        district_id = entry.get("district_id", None)
        entry["name"] = names_map.get(district_id, None)  # Default to None if not found
    return response
