import graphene
from graphene import Enum
from graphene.types.generic import GenericScalar
from graphene_django import DjangoObjectType
from graphene_gis.scalars import JSONScalar

from deals.models.beneficiary import Beneficiary
from orders.models import Order, Task, Note
from users.schema.object_types import UserType


class TaskBeneficiaryType(DjangoObjectType):
    class Meta:
        model = Beneficiary
        fields = ["code", "external_id", "name", "id"]


class OrderType(DjangoObjectType):
    tasks_count = graphene.Int()
    rejected_tasks_count = graphene.Int()
    pending_review_tasks_count = graphene.Int()
    assigned_to_customer_services_task_count = graphene.Int()
    assigned_to_negotiator_task_count = graphene.Int()

    class Meta:
        model = Order
        fields = [
            "id",
            "start_date",
            "end_date",
            "created",
            "modified",
        ]


class TaskType(DjangoObjectType):
    customer_service = graphene.Field(UserType)
    negotiator = graphene.Field(UserType)
    beneficiary = graphene.Field(TaskBeneficiaryType)
    order = graphene.Field(OrderType)
    status = graphene.Field(JSONScalar)
    assigned_status = graphene.Field(JSONScalar)
    form_data = graphene.Field(JSONScalar)
    call_logs = graphene.Field(JSONScalar)
    service_type = graphene.String()

    class Meta:
        model = Task
        fields = [
            "id",
            "beneficiary",
            "status",
            "assigned_status",
            "customer_service",
            "negotiator",
            "created",
            "modified",
            "form_data",
        ]

    def resolve_service_type(self: Task, info):
        service_type = self.extract_data(field="service_type", path="customer_service")
        return service_type

    def resolve_status(self: Task, info):
        return {"key": self.status, "display": self.get_status_display()}

    def resolve_call_logs(self: Task, info):
        meta_data = self.meta_data or {}
        call_logs = meta_data.get("call_logs", {})
        return call_logs

    def resolve_assigned_status(self: Task, info):
        return {
            "key": self.assigned_status,
            "display": self.get_assigned_status_display(),
        }


class OrderListType(graphene.ObjectType):
    data = graphene.List(OrderType)
    count = graphene.Int()


class TaskListType(graphene.ObjectType):
    data = graphene.List(TaskType)
    count = graphene.Int()


class NoteType(DjangoObjectType):
    class Meta:
        model = Note
        fields = ["note", "task", "created_by", "created", "modified"]


class NoteListType(graphene.ObjectType):
    data = graphene.List(NoteType)
    count = graphene.Int()


class RecommendationsType(graphene.ObjectType):
    data = GenericScalar()


class IndicatorsEnum(Enum):
    NUMBER_OF_TRANSACTIONS = "number_of_transactions"
    MEDIAN_AREA = "median_area"
    MEDIA_PRICE_PER_M2 = "median_price_per_m2"


class JsonType(graphene.ObjectType):
    data = graphene.Field(JSONScalar)
    error = graphene.Field(JSONScalar)


__all__ = [
    "OrderType",
    "OrderListType",
    "TaskType",
    "TaskListType",
    "RecommendationsType",
    "NoteListType",
    "NoteType",
    "JsonType",
]
