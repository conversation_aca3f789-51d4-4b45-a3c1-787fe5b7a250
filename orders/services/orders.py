from common.permissions import PermissionsInterface
from common.validators import InputValidation
from orders.models import Order
from orders.strategies.orders import OrderStrategy


class OrderService:
    def __init__(
        self,
        strategy: OrderStrategy,
        perms: PermissionsInterface,
        validations: InputValidation,
    ):
        self.strategy = strategy
        self.perms = perms
        self.validations = validations

    def create_order(self, user, order_input) -> Order:
        context = self.validations.get_object_if_exists(user, order_input)
        self.perms.check_permissions(user=user, context=context)
        return self.strategy.create_order(
            user=user, order_input=order_input, context=context
        )
