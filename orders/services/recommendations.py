from django.contrib.auth import get_user_model

from common.permissions import PermissionsInterface
from common.validators import InputValidation
from orders.strategies import ListRecommendationStrategy

User = get_user_model()


class RecommendationsService:
    def __init__(
        self,
        strategy: ListRecommendationStrategy,
        perms: PermissionsInterface,
        validations: InputValidation,
    ):
        self.strategy = strategy
        self.perms = perms
        self.validations = validations

    def resolve_recommendations(self, user: User, input_data: dict) -> dict:
        context = self.validations.get_object_if_exists(user, input_data)
        self.perms.check_permissions(user=user, context=context)
        return self.strategy.resolve_recommendations(
            input_data=input_data, user=user, context=context
        )
