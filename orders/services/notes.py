from common.permissions import PermissionsInterface
from common.validators import InputValidation
from orders.models import Note
from orders.strategies.notes import NoteStrategy


class NoteService:
    def __init__(
        self,
        strategy: NoteStrategy,
        perms: PermissionsInterface,
        validations: InputValidation,
    ):
        self.strategy = strategy
        self.perms = perms
        self.validations = validations

    def create_note(self, user, note_input) -> Note:
        context = self.validations.get_object_if_exists(user, note_input)
        self.perms.check_permissions(user=user, context=context)
        return self.strategy.create_note(
            user=user, note_data=note_input, context=context
        )
