from typing import Optional, Union

from django.contrib.auth import get_user_model
from django.db.models import QuerySet

from formschemas.mixins import FormSchemaMixin
from common.permissions import PermissionsInterface
from common.validators import InputValidation
from orders.models import Task
from orders.strategies import TaskStrategy

User = get_user_model()


class TaskService(FormSchemaMixin):
    def __init__(
        self,
        strategy: TaskStrategy,
        perms: PermissionsInterface,
        validations: InputValidation,
    ):
        self.strategy = strategy
        self.perms = perms
        self.validations = validations

    def update_task(
        self, user: User, task_input: dict
    ) -> Optional[Union[Task, QuerySet[Task]]]:
        context = self.validations.get_object_if_exists(user, task_input)
        self.perms.check_permissions(user=user, context=context)
        return self.strategy.update_task(
            task_input=task_input, user=user, context=context
        )
