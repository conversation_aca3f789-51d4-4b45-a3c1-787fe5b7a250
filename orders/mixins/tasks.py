from django.db.models import QuerySet
from django.utils.translation import gettext_lazy as _
from gabbro.graphene import NotFound, BadRequest

from deals.models import Deal, Agreement
from orders.models import Task, TaskStatus
from users.models import User


class TaskMixin:
    @staticmethod
    def get_task_if_exists(filters: dict):
        task = (
            Task.objects.filter(**filters).select_related("beneficiary__user").first()
        )
        if not task:
            raise NotFound(reason={"task": _("task matching query doesn't found") % {}})
        return task

    @staticmethod
    def get_tasks_if_exist(filters: dict) -> QuerySet[Task] | None:
        tasks = Task.objects.filter(**filters)
        wrong_task_ids = set(filters.get("id__in", [])).difference(
            set(tasks.values_list("id", flat=True))
        )
        if wrong_task_ids:
            raise NotFound(
                reason={
                    "task_id": _("invalid %(task_ids)s task ids")
                    % {"task_ids": wrong_task_ids}
                }
            )
        return tasks

    @staticmethod
    def validate_tasks_statuses_is_in_progress(tasks: QuerySet[Task]):
        invalid_tasks_ids = tasks.filter(
            status__in=[TaskStatus.REJECTED, TaskStatus.FINISHED]
        ).values_list("id", flat=True)
        if invalid_tasks_ids:
            raise BadRequest(
                reason={
                    "task_id": _("Tasks with ids %(ids)s, can't be reassigned")
                    % {"ids": str(list(invalid_tasks_ids))}
                }
            )

    @staticmethod
    def validate_task_status_is_in_progress(task: Task):
        if task.status in [TaskStatus.REJECTED, TaskStatus.FINISHED]:
            raise BadRequest(
                reason={
                    "task_id": _("Task is %(status)s, can't be deleted or rejected")
                    % {"status": task.get_status_display()}
                }
            )

    @staticmethod
    def validate_task_has_not_an_ongoing_deal(task: Task):
        deal = (
            Deal.objects.filter(favorite_offer__task=task)
            .select_related("favorite_offer__task")
            .first()
        )
        agreement = Agreement.objects.filter(task=task).select_related("task").first()
        if any([deal, agreement]):
            raise BadRequest(reason={"task_id": _("this task has ongoing deal") % {}})

    @staticmethod
    def validate_keys_exists(
        form_data: dict, task_id: int, level: str, required_keys: list = None
    ):
        """
        Validate mandatory specific keys in the form_data JsonField are present and not empty.
        """
        form_data = form_data or {}
        required_keys = required_keys if required_keys else []
        if level == "customer_service":
            form_data = form_data.get("customer_service", {})
            # mandatory keys
            required_keys = required_keys or ["beneficiaryData"]
        if level == "negotiator":
            form_data = form_data.get("negotiator", {})
            # mandatory keys
            required_keys = required_keys or ["placeOrder", "negotiatorData"]

        drafts = form_data.pop("drafts", {})
        if drafts and any(key in drafts for key in required_keys):
            raise BadRequest(
                reason={"form_data": _("Submit Form Data and remove Drafts") % {}}
            )

        for key in required_keys:
            if not form_data.get(key):
                raise BadRequest(
                    reason={
                        "form_data": _(
                            "Key '%(key)s' is missing or empty in task %(task_id)s form data"
                        )
                        % {"key": key, "task_id": task_id}
                    }
                )

    def update_call_logs(
        self,
        task: Task,
        user: User,
        level: str,
        current_form_data: dict,
        old_form_data: dict,
        check_time=True,
    ):
        is_changed, form_data = (
            self.check_task_contact_time(
                current_task_form_data=current_form_data,
                old_task_form_data=old_form_data,
                level=level,
            )
            if check_time is True
            else (True, current_form_data)
        )
        if is_changed is True:
            meta_data = task.meta_data or {}
            call_logs = meta_data.get("call_logs", {})
            expected_call = (
                form_data.get("contact", {}).get("expectedCall", {})
                if level == "customer_service"
                else {
                    "expectedCallDate": form_data.get("contact", {}).get("date", ""),
                    "expectedCallTime": form_data.get("contact", {}).get("time", ""),
                }
            )
            new_log_entry = {
                "status": current_form_data.get("contact", {}).get("callStatus"),
                "date_time": task.modified.isoformat(),
                "user": f"{user.first_name} {user.last_name}",
                "expected_call": expected_call,
            }

            call_logs.setdefault(level, []).append(new_log_entry)

            task.meta_data = {
                **meta_data,
                "call_logs": call_logs,
            }
            task.save(update_fields=["meta_data"])

    @staticmethod
    def check_task_contact_time(
        current_task_form_data: dict, old_task_form_data: dict, level: str
    ):
        old_contact_data = old_task_form_data.get("contact", {})
        current_contact_data = current_task_form_data.get("contact", {})
        # Extract necessary fields with default values
        current_status = current_contact_data.get("callStatus", "")
        current_continue_program = current_contact_data.get(
            "willingnessToContinueProgram", ""
        )
        current_expected_call = current_contact_data.get("expectedCall", "")
        old_expected_call = old_contact_data.get("expectedCall", "")

        # Conditions when there is previous contact data
        if level == "negotiator":
            # check if its task hasn't previous call
            if not all(
                [old_contact_data.get("date", ""), old_contact_data.get("time", "")]
            ):
                return False, current_task_form_data
            negotiator_contact = current_contact_data.get(
                "date"
            ) != old_contact_data.get("date") or current_contact_data.get(
                "time"
            ) != old_contact_data.get(
                "time"
            )
            return negotiator_contact, current_task_form_data
        if (
            current_status == "تم الرد"
            and current_continue_program == "إعادة جدولة الإتصال"
            and old_expected_call != current_expected_call
        ):
            return True, current_task_form_data
        if (
            current_status == "لم يتم الرد"
            and old_expected_call != current_expected_call
        ):
            return True, current_task_form_data
        return False, current_task_form_data
