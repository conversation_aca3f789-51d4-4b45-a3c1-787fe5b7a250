from django.db.models import Count, Q
from rest_framework import serializers

from deals.models import Beneficiary
from orders.models import Order, TaskStatus


def get_beneficiaries_for_new_order_queryset():
    """
    - get all beneficiaries has no previous tasks
    - get all beneficiaries has previous rejected tasks
    - exclude rejected beneficiaries with ongoing tasks
    """
    return (
        Beneficiary.objects.alias(
            non_rejected_task_count=Count(
                "tasks", filter=~Q(tasks__status=TaskStatus.REJECTED)
            )
        )
        .filter(Q(tasks__isnull=True) | Q(non_rejected_task_count=0))
        .distinct()
    )


class CreateOrderSerializer(serializers.ModelSerializer):
    beneficiaries = serializers.PrimaryKeyRelatedField(
        many=True, queryset=get_beneficiaries_for_new_order_queryset()
    )

    class Meta:
        model = Order
        fields = ["beneficiaries", "start_date", "end_date"]
