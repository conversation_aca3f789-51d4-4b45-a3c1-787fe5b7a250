from rest_framework import serializers

from orders.models import Task, TaskStatus


class UpdateTaskSerializer(serializers.ModelSerializer):
    status = serializers.ChoiceField(
        choices=[
            (key, value)
            for key, value in TaskStatus.choices
            if key
            not in [
                TaskStatus.REJECTED.value,
                TaskStatus.FINISHED.value,
                TaskStatus.IN_PROGRESS.value,
                TaskStatus.PENDING_REVIEW.value,
                TaskStatus.CREATE_DEAL,
            ]
        ]
    )

    class Meta:
        model = Task
        fields = ["form_data", "status"]


class AssignTaskToProjectManagerSerializer(serializers.ModelSerializer):
    class Meta:
        model = Task
        fields = ["form_data", "status", "assigned_status"]


class AssignTaskToNegotiatorSerializer(serializers.ModelSerializer):
    class Meta:
        model = Task
        fields = ["negotiator", "status", "form_data", "assigned_status"]


class RejectTaskSerializer(serializers.ModelSerializer):
    class Meta:
        model = Task
        fields = ["status", "assigned_status", "form_data"]


class DeleteTaskSerializer(serializers.ModelSerializer):
    class Meta:
        model = Task
        fields = ["status"]
