from django.contrib import admin

from orders.models import Note


class NoteAdmin(admin.ModelAdmin):
    list_display = ["id", "task", "created_by"]
    search_fields = ["note"]
    list_filter = ["created_by", "task"]

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def has_change_permission(self, request, obj=None):
        return False


admin.site.register(Note, NoteAdmin)
