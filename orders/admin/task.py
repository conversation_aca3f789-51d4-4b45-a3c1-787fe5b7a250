from django.contrib import admin

from orders.models import Task


class TaskAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "order",
        "customer_service",
        "beneficiary",
        "negotiator",
        "status",
        "assigned_status",
    ]
    list_filter = ["status", "assigned_status"]
    search_fields = [
        "id",
        "beneficiary__name",
        "beneficiary__code",
    ]

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def has_change_permission(self, request, obj=None):
        return False


admin.site.register(Task, TaskAdmin)
