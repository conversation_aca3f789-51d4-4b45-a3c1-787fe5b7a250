from django.contrib import admin
from orders.models import Order
from orders.admin.forms.order import EditOrderForm


class OrderAdmin(admin.ModelAdmin):
    list_display = ["__str__", "start_date", "end_date"]

    def get_form(self, request, obj=None, **kwargs):
        if obj:
            form = EditOrderForm
            return form
        else:
            form = super(OrderAdmin, self).get_form(request, obj, **kwargs)
            return form

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def has_change_permission(self, request, obj=None):
        return False


admin.site.register(Order, OrderAdmin)
