from django import forms
from orders.models.order import Order


class EditOrderForm(forms.ModelForm):
    class Meta:
        model = Order
        fields = ["beneficiaries", "start_date", "end_date"]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        instance = kwargs.get("instance")
        self.fields["beneficiaries"].queryset = instance.beneficiaries.all()
        for field in self.fields:
            self.fields[field].widget.attrs["disabled"] = True
