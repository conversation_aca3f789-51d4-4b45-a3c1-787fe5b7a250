from django.contrib.admin import AdminSite
from django.http import HttpRequest
from django.test import TestCase

from orders.admin import NoteAdmin
from orders.models import Note
from users.models import UserRoleChoices
from utils.tests.factories import (
    UserFactory,
    NoteFactory,
    BeneficiaryFactory,
    OrderFactory,
    RenRoleFactory,
    TaskFactory,
)


class MockRequest(HttpRequest):
    def __init__(self, user=None):
        super().__init__()
        self.user = user


class NoteAdminTest(TestCase):
    def setUp(self):
        self.site = AdminSite()
        self.admin_note = NoteAdmin(Note, self.site)
        self.user = UserFactory(is_superuser=True)
        self.request = MockRequest(user=self.user)
        self.beneficiary = BeneficiaryFactory()
        self.order = OrderFactory(beneficiaries=[self.beneficiary])
        self.negotiator = UserFactory(
            roles=[RenRoleFactory(role=UserRoleChoices.NEGOTIATORS)]
        )
        self.customer_service = UserFactory(
            roles=[RenRoleFactory(rolle=UserRoleChoices.CUSTOMER_SERVICES)]
        )
        self.task = TaskFactory(
            order=self.order,
            beneficiary=self.beneficiary,
            customer_service=self.customer_service,
            negotiator=self.negotiator,
        )
        self.note = NoteFactory(task=self.task, created_by=self.user)

    def test_note_admin_registration(self):
        self.assertIsNotNone(self.admin_note)

    def test_list_display(self):
        # Check if the list_display is correctly defined in the admin class
        expected_list_display = ["id", "task", "created_by"]
        self.assertEqual(self.admin_note.list_display, expected_list_display)

    def test_search_fields(self):
        # Check if the search fields are correctly defined
        expected_search_fields = ["note"]
        self.assertEqual(self.admin_note.search_fields, expected_search_fields)

    def test_queryset(self):
        # Test that the correct queryset is returned
        queryset = self.admin_note.get_queryset(self.request)
        self.assertIn(self.note, queryset)

    def test_has_view_permission(self):
        # Test the view permission logic
        self.assertTrue(self.admin_note.has_view_permission(self.request))

    def test_has_add_permission(self):
        # Test the add permission logic
        self.assertFalse(self.admin_note.has_add_permission(self.request))

    def test_has_change_permission(self):
        # Test the change permission logic
        self.assertFalse(self.admin_note.has_change_permission(self.request))

    def test_has_delete_permission(self):
        # Test the delete permission logic
        self.assertFalse(self.admin_note.has_delete_permission(self.request))

    def test_get_fields(self):
        self.assertListEqual(
            self.admin_note.get_fields(self.request),
            ["note", "task", "created_by"],
        )
        self.assertListEqual(
            self.admin_note.get_fields(self.request, self.note),
            ["note", "task", "created_by"],
        )
