from django.contrib.admin import AdminSite
from django.http import HttpRequest
from django.test import TestCase

from orders.admin import OrderAdmin, EditOrderForm
from orders.models import Order
from utils.tests.factories import UserFactory, OrderFactory, BeneficiaryFactory


class MockRequest(HttpRequest):
    def __init__(self, user=None):
        super().__init__()
        self.user = user


class OrderAdminTest(TestCase):
    def setUp(self):
        self.site = AdminSite()
        self.admin_order = OrderAdmin(Order, self.site)
        self.user = UserFactory(is_superuser=True, email="<EMAIL>")
        self.user.set_password("password")
        self.user.is_active = True
        self.user.save()
        self.request = MockRequest(user=self.user)
        self.order = OrderFactory(beneficiaries=[BeneficiaryFactory()])

    def test_order_admin_registration(self):
        self.assertIsNotNone(self.admin_order)

    def test_update_order_form(self):
        form = self.admin_order.get_form(self.request, self.order)
        self.assertEqual(form, EditOrderForm)

    def test_list_display(self):
        # Check if the list_display is correctly defined in the admin class
        expected_list_display = ["__str__", "start_date", "end_date"]
        self.assertEqual(self.admin_order.list_display, expected_list_display)

    def test_search_fields(self):
        # Check if the search fields are correctly defined
        self.assertFalse(self.admin_order.search_fields)

    def test_queryset(self):
        # Test that the correct queryset is returned
        queryset = self.admin_order.get_queryset(self.request)
        self.assertIn(self.order, queryset)

    def test_has_view_permission(self):
        # Test the view permission logic
        self.assertTrue(self.admin_order.has_view_permission(self.request))

    def test_has_add_permission(self):
        # Test the add permission logic
        self.assertFalse(self.admin_order.has_add_permission(self.request))

    def test_has_change_permission(self):
        # Test the change permission logic
        self.assertFalse(self.admin_order.has_change_permission(self.request))

    def test_has_delete_permission(self):
        # Test the delete permission logic
        self.assertFalse(self.admin_order.has_delete_permission(self.request))

    def test_get_fields(self):
        self.assertListEqual(
            self.admin_order.get_fields(self.request),
            ["beneficiaries", "start_date", "end_date"],
        )
        self.assertListEqual(
            self.admin_order.get_fields(self.request, self.order),
            ["beneficiaries", "start_date", "end_date"],
        )
