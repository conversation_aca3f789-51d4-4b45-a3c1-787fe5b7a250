from django.contrib.admin import AdminSite
from django.http import HttpRequest
from django.test import TestCase

from orders.admin import TaskAdmin
from orders.models import Task
from users.models import UserRoleChoices
from utils.tests.factories import (
    UserFactory,
    BeneficiaryFactory,
    OrderFactory,
    RenRoleFactory,
    TaskFactory,
)


class MockRequest(HttpRequest):
    def __init__(self, user=None):
        super().__init__()
        self.user = user


class TaskAdminTest(TestCase):
    def setUp(self):
        self.site = AdminSite()
        self.admin_task = TaskAdmin(Task, self.site)
        self.user = UserFactory(is_superuser=True)
        self.request = MockRequest(user=self.user)
        self.beneficiary = BeneficiaryFactory()
        self.order = OrderFactory(beneficiaries=[self.beneficiary])
        self.negotiator = UserFactory(
            roles=[RenRoleFactory(role=UserRoleChoices.NEGOTIATORS)]
        )
        self.customer_service = UserFactory(
            roles=[RenRoleFactory(rolle=UserRoleChoices.CUSTOMER_SERVICES)]
        )
        self.task = TaskFactory(
            order=self.order,
            beneficiary=self.beneficiary,
            customer_service=self.customer_service,
            negotiator=self.negotiator,
        )

    def test_order_admin_registration(self):
        self.assertIsNotNone(self.admin_task)

    def test_list_display(self):
        # Check if the list_display is correctly defined in the admin class
        expected_list_display = [
            "id",
            "order",
            "customer_service",
            "beneficiary",
            "negotiator",
            "status",
            "assigned_status",
        ]
        self.assertEqual(self.admin_task.list_display, expected_list_display)

    def test_search_fields(self):
        # Check if the search fields are correctly defined
        expected_search_fields = ["id", "beneficiary__name", "beneficiary__code"]
        self.assertEqual(self.admin_task.search_fields, expected_search_fields)

    def test_queryset(self):
        # Test that the correct queryset is returned
        queryset = self.admin_task.get_queryset(self.request)
        self.assertIn(self.task, queryset)

    def test_has_view_permission(self):
        # Test the view permission logic
        self.assertTrue(self.admin_task.has_view_permission(self.request))

    def test_has_add_permission(self):
        # Test the add permission logic
        self.assertFalse(self.admin_task.has_add_permission(self.request))

    def test_has_change_permission(self):
        # Test the change permission logic
        self.assertFalse(self.admin_task.has_change_permission(self.request))

    def test_has_delete_permission(self):
        # Test the delete permission logic
        self.assertFalse(self.admin_task.has_delete_permission(self.request))

    def test_get_fields(self):
        self.assertListEqual(
            self.admin_task.get_fields(self.request),
            [
                "order",
                "customer_service",
                "negotiator",
                "beneficiary",
                "status",
                "assigned_status",
                "form_data",
                "meta_data",
            ],
        )
        self.assertListEqual(
            self.admin_task.get_fields(self.request, self.task),
            [
                "order",
                "customer_service",
                "negotiator",
                "beneficiary",
                "status",
                "assigned_status",
                "form_data",
                "meta_data",
            ],
        )
