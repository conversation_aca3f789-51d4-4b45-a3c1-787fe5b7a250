import datetime
import random

from django.test import TestCase

from orders.models import TaskAssignedStatus
from orders.models.order import Order
from orders.models.task import Task, TaskStatus
from users.models import UserRoleChoices
from utils.tests.factories import (
    BeneficiaryFactory,
    OrderFactory,
    UserFactory,
    RenRoleFactory,
)


class OrderBaseTestCase(TestCase):
    def setUp(self):
        self.beneficiaries = BeneficiaryFactory.create_batch(10)


class OrderTestCase(OrderBaseTestCase):
    def setUp(self):
        super(OrderTestCase, self).setUp()

    def test_model_creation(self):
        """
        Test creation of Order Model
        """
        orders = []
        for i in range(1, 6):
            order = Order(
                start_date=datetime.datetime.now(),
                end_date=datetime.datetime.now(),
            )
            orders.append(order)
        orders = Order.objects.bulk_create(orders)
        for order in orders:
            order.beneficiaries.set(self.beneficiaries)
            order.save()
        # check orders count
        self.assertEqual(Order.objects.count(), 5)
        # check beneficiary count for specific order
        self.assertEqual(Order.objects.first().beneficiaries.count(), 10)

    def test_order_str(self):
        """
        Test string representation of Order
        """
        order = OrderFactory(beneficiaries=self.beneficiaries)
        self.assertEqual(str(order), str(order.id))

    def test_order_tasks_count(self):
        order: Order = OrderFactory(beneficiaries=self.beneficiaries)
        customer_service = UserFactory(
            roles=[RenRoleFactory(role=UserRoleChoices.CUSTOMER_SERVICES)]
        )
        negotiator = UserFactory(
            roles=[RenRoleFactory(role=UserRoleChoices.NEGOTIATORS)]
        )
        tasks = []
        for obj in self.beneficiaries:
            status = random.choices(TaskStatus.choices)[0]
            task = Task(
                order=order,
                customer_service=customer_service,
                negotiator=negotiator,
                beneficiary=obj,
                status=status[0],
            )
            tasks.append(task)
        Task.objects.bulk_create(tasks)
        tasks = Task.objects.all()
        rejected_tasks_count = tasks.filter(status=TaskStatus.REJECTED).count()
        pending_review_tasks_count = tasks.filter(
            status=TaskStatus.PENDING_REVIEW
        ).count()
        assigned_to_customer_services_task_count = tasks.filter(
            assigned_status=TaskAssignedStatus.ASSIGNED_TO_CUSTOMER_SERVICE,
            status=TaskStatus.IN_PROGRESS,
        ).count()
        assigned_to_negotiator_task_count = tasks.filter(
            assigned_status=TaskAssignedStatus.ASSIGNED_TO_NEGOTIATOR,
            status=TaskStatus.IN_PROGRESS,
        ).count()
        self.assertEqual(rejected_tasks_count, order.rejected_tasks_count)
        self.assertEqual(pending_review_tasks_count, order.pending_review_tasks_count)
        self.assertEqual(
            assigned_to_customer_services_task_count,
            order.assigned_to_customer_services_task_count,
        )
        self.assertEqual(
            assigned_to_negotiator_task_count, order.assigned_to_negotiator_task_count
        )
