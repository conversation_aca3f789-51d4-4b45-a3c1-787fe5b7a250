from django.test import TestCase

from orders.models.task import Task, TaskStatus, TaskAssignedStatus
from users.models import UserRoleChoices
from utils.tests.factories import (
    BeneficiaryFactory,
    OrderFactory,
    UserFactory,
    RenRoleFactory,
)


class TaskBaseTestCase(TestCase):
    def setUp(self):
        self.beneficiaries = BeneficiaryFactory.create_batch(10)
        self.order = OrderFactory(beneficiaries=self.beneficiaries)
        self.customer_service = UserFactory(
            roles=[RenRoleFactory(role=UserRoleChoices.CUSTOMER_SERVICES)]
        )
        self.negotiator = UserFactory(
            roles=[RenRoleFactory(role=UserRoleChoices.NEGOTIATORS)]
        )


class TaskTestCase(TaskBaseTestCase):
    def setUp(self):
        super(TaskTestCase, self).setUp()

    def test_task_creation(self):
        tasks = []
        for obj in self.beneficiaries:
            task = Task(
                order=self.order,
                beneficiary=obj,
                negotiator=self.negotiator,
                customer_service=self.customer_service,
            )
            tasks.append(task)
        Task.objects.bulk_create(tasks)
        self.assertEqual(Task.objects.count(), len(self.beneficiaries))
        for task in Task.objects.all():
            self.assertEqual(task.status, TaskStatus.IN_PROGRESS)
            self.assertEqual(
                task.assigned_status, TaskAssignedStatus.ASSIGNED_TO_CUSTOMER_SERVICE
            )
            self.assertIsNone(task.form_data)

    def test_model_str(self):
        task = Task.objects.create(
            order=self.order,
            beneficiary=self.beneficiaries[0],
            negotiator=self.negotiator,
            customer_service=self.customer_service,
        )
        self.assertEqual(str(task), f"{task.beneficiary.name} - {task.id}")
