import os

from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.management import call_command
from django.test import override_settings
from django.utils.translation import ugettext_lazy as _
from gabbro.graphene import BadRequest, NotFound

from deals.management.commands.load_ren_locations import LAYERS_KEYS
from deals.models import Record, Layer
from orders.models import TaskAssignedStatus
from orders.tests.test_schema.test_queries import BaseTestQueries
from orders.validators import UpdateTaskValidation
from utils.tests.factories import (
    TaskFactory,
    OrderFactory,
    BeneficiaryFactory,
)

User = get_user_model()


class TaskLocationValidationTestCase(BaseTestQueries):
    @classmethod
    @override_settings(
        MEDIA_ROOT=os.path.join(settings.BASE_DIR, "deals", "tests", "fixtures")
    )
    def setUpTestData(cls):
        super().setUpTestData()
        files_path = os.path.join(
            settings.BASE_DIR, "deals", "tests", "fixtures", "sample_geopackage_data"
        )
        files = os.listdir(files_path)
        for file in files:
            # create records per layer
            file_path = os.path.join(files_path, file)
            layer_name = file.split(".")[0]
            call_command(
                "load_ren_locations",
                layer_key=LAYERS_KEYS[layer_name],
                file_path=file_path,
            )

    def setUp(
        self,
    ):
        super().setUp()
        self.beneficiaries = BeneficiaryFactory.create_batch(size=5)
        self.order = OrderFactory(beneficiaries=self.beneficiaries)
        beneficiary = self.order.beneficiaries.first()
        self.task = TaskFactory(
            assigned_status=TaskAssignedStatus.ASSIGNED_TO_NEGOTIATOR,
            order=self.order,
            beneficiary=beneficiary,
            customer_service=self.customer_service[0],
            negotiator=self.auth_request.user,
            form_data={"customer_service": {"beneficiaryData": {}}},
        )
        self.region = Record.objects.filter(layer__key=LAYERS_KEYS["region"]).first()
        region_id = self.region.source_properties.get("id")
        self.city = Record.objects.filter(
            layer__key=LAYERS_KEYS["city"], source_properties__region_id=region_id
        ).first()
        city_id = self.city.source_properties.get("id")
        self.zone = Record.objects.filter(
            layer__key=LAYERS_KEYS["zone"], source_properties__city_id=city_id
        ).first()
        zone_id = self.zone.source_properties.get("id")
        self.district = Record.objects.filter(
            layer__key=LAYERS_KEYS["districts"],
            source_properties__city_id=city_id,
            source_properties__zone_id__in=[zone_id],
        ).first()
        district_id = self.district.source_properties.get("id")
        self.form_data = {
            "placeOrder": {
                "benificiaryServiceType": {
                    "serviceType": "بحث عن عقار",
                },
                "locationPreferencesLevelTwo": {
                    "district": [
                        {
                            "id": district_id,
                            "label": self.district.source_properties.get(
                                "district_name"
                            ),
                        }
                    ],
                    "regionId": region_id,
                    "cityId": city_id,
                    "mainDivision": [
                        {
                            "id": self.zone.source_properties.get("id"),
                            "label": self.zone.source_properties.get("zone_name"),
                        }
                    ],
                    "preferredCity": self.city.source_properties.get("city_name"),
                    "preferredRegion": self.region.source_properties.get("region_name"),
                },
                "financialPreferencesLevelTwo": {
                    "preferredPurchaseAmount": 15000000,
                    "minimumPurchaseAmount": 100,
                    "maximumPurchaseAmount": 55,
                },
                "realEstatePreferences": {"preferredPurchaseAmount": "أرض"},
            }
        }

    def test_location_validation_success(self):
        result = UpdateTaskValidation().task_location_validation(
            self.form_data, task=self.task
        )
        self.assertIsNone(result)

    def test_location_validation_with_invalid_district(self):
        self.form_data["placeOrder"]["locationPreferencesLevelTwo"]["district"][0][
            "id"
        ] = -1
        with self.assertRaises(BadRequest):
            result = UpdateTaskValidation().task_location_validation(
                self.form_data, task=self.task
            )
            self.assertDictEqual(
                result,
                _(
                    "District %(wrong_districts_ids)s Doesnt Relate to this Zone/City "
                    % {"wrong_districts_ids": -1}
                ),
            )

    def test_location_validation_without_district(self):
        self.form_data["placeOrder"]["locationPreferencesLevelTwo"].pop("district")
        location = UpdateTaskValidation().task_location_validation(
            self.form_data, task=self.task
        )
        self.assertIsNone(location)

    def test_location_validation_without_level(self):
        self.form_data = {}
        location = UpdateTaskValidation().task_location_validation(
            self.form_data, task=self.task
        )
        self.assertIsNone(location)

    def test_location_validation_without_city_layer(self):
        Layer.objects.filter(key=LAYERS_KEYS["city"]).delete()
        with self.assertRaises(NotFound):
            UpdateTaskValidation().task_location_validation(
                self.form_data, task=self.task
            )

    def test_location_validation_without_region_layer(self):
        Layer.objects.filter(key=LAYERS_KEYS["region"]).delete()
        with self.assertRaises(NotFound):
            UpdateTaskValidation().task_location_validation(
                self.form_data, task=self.task
            )

    def test_location_validation_without_district_layer(self):
        Layer.objects.filter(key=LAYERS_KEYS["districts"]).delete()
        with self.assertRaises(NotFound):
            UpdateTaskValidation().task_location_validation(
                self.form_data, task=self.task
            )

    def test_location_validation_without_zone(self):
        self.form_data["placeOrder"]["locationPreferencesLevelTwo"].update(
            mainDivision=[]
        )
        with self.assertRaises(BadRequest):
            UpdateTaskValidation().task_location_validation(
                self.form_data, task=self.task
            )

    def test_location_validation_wit_invalid_region(self):
        self.form_data["placeOrder"]["locationPreferencesLevelTwo"]["regionId"] = -1
        with self.assertRaises(NotFound):
            UpdateTaskValidation().task_location_validation(
                self.form_data, task=self.task
            )

    def test_location_validation_wit_invalid_city(self):
        self.form_data["placeOrder"]["locationPreferencesLevelTwo"]["cityId"] = -1
        with self.assertRaises(NotFound):
            UpdateTaskValidation().task_location_validation(
                self.form_data, task=self.task
            )
