from unittest.mock import patch

from django.contrib.auth import get_user_model
from django.contrib.auth.models import AnonymousUser
from django.test import TestCase
from django.utils import timezone
from django.utils.translation import activate
from django.utils.translation import gettext_lazy as _
from graphene.test import Client
from mock.mock import MagicMock

from app.schema import schema
from deals.models import Beneficiary
from orders.models import Task, TaskStatus, TaskAssignedStatus
from users.models import UserRoleChoices
from utils.tests.factories import (
    UserFactory,
    RenRoleFactory,
    BeneficiaryFactory,
    OrderFactory,
    FormSchemaFactory,
    DealFactory,
    FavoriteOfferFactory,
    RecordFactory,
    LayerFactory,
)

User = get_user_model()


class BaseTestQueries(TestCase):
    def setUp(self):
        activate("ar")
        self.roles = {
            UserRoleChoices.CUSTOMER_SERVICES.value: RenRoleFactory(
                role=UserRoleChoices.CUSTOMER_SERVICES
            ),
            UserRoleChoices.PROJECT_MANAGERS.value: RenRoleFactory(
                role=UserRoleChoices.PROJECT_MANAGERS
            ),
            UserRoleChoices.NEGOTIATORS.value: RenRoleFactory(
                role=UserRoleChoices.NEGOTIATORS
            ),
        }
        self.user = UserFactory()
        self.superuser = UserFactory(
            is_superuser=True,
            roles=[self.roles[UserRoleChoices.PROJECT_MANAGERS.value]],
        )
        self.negotiator = UserFactory(
            roles=[self.roles[UserRoleChoices.NEGOTIATORS.value]]
        )
        self.project_manager = UserFactory(
            roles=[self.roles[UserRoleChoices.PROJECT_MANAGERS.value]]
        )
        self.customer_service = UserFactory.create_batch(
            size=5, roles=[self.roles[UserRoleChoices.CUSTOMER_SERVICES.value]]
        )
        self.region_layer = LayerFactory(key="region")
        self.region_record = RecordFactory(
            layer=self.region_layer, source_properties={"id": 96874054}
        )
        self.city_layer = LayerFactory(key="city")
        self.city_record = RecordFactory(
            layer=self.city_layer, source_properties={"id": 1, "region_id": 96874054}
        )
        self.district_layer = LayerFactory(key="districts")
        self.district_record = RecordFactory(
            layer=self.district_layer,
            source_properties={"id": 119531769, "zone_id": 1, "city_id": 1},
        )
        self.client = Client(schema)
        self.auth_request = MagicMock()
        self.project_manager_auth_request = MagicMock()
        self.super_user_auth_request = MagicMock()
        self.super_user_auth_request.user = self.superuser
        self.project_manager_auth_request.user = self.project_manager
        self.negotiator_auth_request = MagicMock()
        self.negotiator_auth_request.user = self.negotiator
        self.auth_request.user = self.user
        self.non_auth_request = MagicMock()
        self.non_auth_request.user = AnonymousUser()
        self.maxDiff = None


class CreateOrderTestCase(BaseTestQueries):
    def setUp(self):
        super().setUp()
        self.beneficiaries = BeneficiaryFactory.create_batch(5)
        self.customer_service = UserFactory(
            roles=[self.roles[UserRoleChoices.CUSTOMER_SERVICES.value]]
        )
        self.mutation = """
            mutation MyMutation($beneficiaries: [Int]!, $customerService: Int!, $endDate: DateTime!, $startDate: DateTime!) {
              createOrderMutation(
                orderInput: {beneficiaries: $beneficiaries, customerService: $customerService, startDate: $startDate, endDate: $endDate}
              ) {
                order {
                  assignedToCustomerServicesTaskCount
                  assignedToNegotiatorTaskCount
                  created
                  endDate
                  id
                  modified
                  pendingReviewTasksCount
                  rejectedTasksCount
                  startDate
                  tasksCount
                }
              }
            }
        """
        beneficiaries_ids = Beneficiary.objects.values_list("id", flat=True)
        self.variables = {
            "beneficiaries": list(beneficiaries_ids),
            "customerService": self.customer_service.id,
            "startDate": f"{timezone.now().isoformat()}",
            "endDate": f"{timezone.now().isoformat()}",
        }

    def test_mutation_authorizations(self):
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))
        self.auth_request.user = self.customer_service
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {
                "user": _("Permission Denied") % {},
            },
        )

    def test_mutation_with_invalid_customer_service(self):
        self.variables["customerService"] = -1
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.project_manager_auth_request,
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Not Found"))
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {
                "customerService": _("invalid customer service id") % {},
            },
        )

    def test_mutation_with_invalid_date(self):
        self.variables["beneficiaries"] = [-1]
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.project_manager_auth_request,
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Bad Request"))

    def test_create_order_transaction_success(self):
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.project_manager_auth_request,
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["createOrderMutation"]["order"]
        self.assertEqual(data["assignedToCustomerServicesTaskCount"], 5)
        self.assertEqual(data["assignedToNegotiatorTaskCount"], 0)
        self.assertEqual(data["pendingReviewTasksCount"], 0)
        self.assertEqual(data["rejectedTasksCount"], 0)
        self.assertEqual(data["endDate"], self.variables["endDate"])
        self.assertEqual(data["startDate"], self.variables["startDate"])
        self.assertEqual(data["tasksCount"], 5)

    def test_create_order_transaction_success_with_previous_tasks(self):
        order = OrderFactory(beneficiaries=self.beneficiaries)
        tasks = []
        for beneficiary in order.beneficiaries.all():
            task = Task(
                order=order,
                status=TaskStatus.REJECTED,
                beneficiary=beneficiary,
                customer_service=self.customer_service,
                negotiator=self.auth_request.user,
                form_data={
                    "customer_service": {
                        "drafts": {"beneficiaryData": beneficiary.data, "test": "test"},
                        "beneficiaryData": beneficiary.data,
                    }
                },
            )
            tasks.append(task)
        Task.objects.bulk_create(tasks)
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.project_manager_auth_request,
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)
        task = order.beneficiaries.first().tasks.last()
        self.assertEqual(task.form_data["customer_service"]["drafts"]["test"], "test")

    def test_update_task_after_create_order(self):
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.project_manager_auth_request,
        )
        task = Beneficiary.objects.first().tasks.first()
        # Check if the query is successful
        self.assertNotIn("errors", response)
        self.assertDictEqual(
            task.form_data.get("customer_service", {})
            .get("beneficiaryData")
            .get("basicTransactionData"),
            {
                "assignmentDate": task.created.strftime("%Y-%m-%d"),
                "assignmentTime": task.created.strftime("%H:%M:%S"),
                "assignmentNumber": str(task.id),
                "customerServiceEmployeeName": f"{task.customer_service.first_name} {task.customer_service.last_name}",
            },
        )


class UpdateCustomerServiceTaskTestCase(BaseTestQueries):
    def setUp(self):
        super().setUp()
        self.form_schema = FormSchemaFactory(
            key="form-schema-1", json_schema={"form": {}, "UISchema": {}}
        )
        self.beneficiaries = BeneficiaryFactory.create_batch(size=5)
        self.order = OrderFactory(beneficiaries=self.beneficiaries)
        tasks = []
        for beneficiary in self.order.beneficiaries.all():
            form_data = {
                **beneficiary.data,
                "locationPreferences": {
                    "cityId": 1,
                    "regionId": 96874054,
                    "mainDivision": [
                        {"id": 2, "label": "شرق"},
                        {"id": 1, "label": "جنوب"},
                        {"id": 3, "label": "شمال"},
                        {"id": 4, "label": "غرب"},
                    ],
                    "preferredCity": "الرياض",
                    "preferredRegion": "منطقة الرياض",
                },
            }
            task = Task(
                assigned_status=TaskAssignedStatus.ASSIGNED_TO_CUSTOMER_SERVICE,
                order=self.order,
                beneficiary=beneficiary,
                customer_service=self.customer_service[0],
                negotiator=self.auth_request.user,
                form_data={"customer_service": {"beneficiaryData": form_data}},
            )
            tasks.append(task)
        Task.objects.bulk_create(tasks)
        self.mutation = """
            mutation MyMutation($formData: JSONString!, $formSchemaKey: String!, $isDraft: Boolean!, $taskId: Int!) {
            updateCustomerServiceTaskMutation(
                taskInput: {taskId: $taskId, formData: $formData, formSchemaKey: $formSchemaKey, isDraft: $isDraft}
                ) {
                task {
                created
                assignedStatus
                formData
                id
                modified
                status
                beneficiary {
                code
                }
                customerService {
                id
                }
                order {
                id
                }
                negotiator {
                id
                }
                }
                }
}
        """
        self.task = self.order.tasks.first()
        self.variables = {
            "taskId": self.task.id,
            "formData": '{"beneficiaryData":{"ID_CODE":"REDF-01-30","locationPreferences":{"cityId":1,"district":[{"id":119531769}],"regionId":96874054,"mainDivision":[{"id":1,"label":"شرق"}],"preferredCity":"الرياض","preferredRegion":"منطقة الرياض"}}}',
            "formSchemaKey": self.form_schema.key,
            "isDraft": False,
        }

    def test_mutation_authorizations(self):
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_mutation_with_invalid_task(self):
        self.variables["taskId"] = -1
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.project_manager_auth_request,
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Not Found"))
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"task": _("task matching query doesn't found") % {}},
        )
        # check with invalid permission
        self.variables["taskId"] = self.task.id
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.negotiator_auth_request,
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"user": _("Permission Denied") % {}},
        )

    def test_mutation_with_invalid_form_schema(self):
        self.variables["formSchemaKey"] = ""
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.super_user_auth_request,
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Not Found"))
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"formSchemaKey": _("invalid Form Schema key") % {}},
        )

    def test_mutation_with_valid_data(self):
        form_data = self.task.form_data or {}
        self.variables["isDraft"] = True
        self.task.negotiator = self.super_user_auth_request.user
        form_data["beneficiaryData"] = {"data": "data"}
        self.task.form_data = form_data
        self.task.save()
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.super_user_auth_request,
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)

    def test_mutation_with_invalid_data(self):
        self.task.status = TaskStatus.REJECTED
        self.task.save()
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.super_user_auth_request,
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Bad Request"))
        self.assertEqual(
            response["errors"][0]["extensions"]["http"]["reason"]["status"],
            _("Task is %(status)s, can't be updated")
            % {"status": self.task.get_status_display()},
        )

    def test_call_logs_changes_for_customer_service(self):
        self.variables[
            "formData"
        ] = '{"beneficiaryData":{"ID_CODE":"REDF-01-13","contact":{"reason":"عدم الرغبة في الوقت الحالي","callStatus":"تم الرد","expectedCall":{"expectedCallDate":"2024-11-07","expectedCallTime":"02:00 مساءًا - 04:00 مساءًا"},"willingnessToContinueProgram":"إعادة جدولة الإتصال"},"locationPreferences":{"cityId":1,"district":[{"id":119531769}],"regionId":96874054,"mainDivision":[{"id":1,"label":"شرق"}],"preferredCity":"الرياض","preferredRegion":"منطقة الرياض"}}}'
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.super_user_auth_request,
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)
        task = Task.objects.get(id=self.task.id)
        call_logs = task.meta_data.get("call_logs", {}).get("customer_service", [])[0]
        self.assertDictEqual(
            call_logs,
            {
                "status": task.form_data.get("customer_service", {})
                .get("beneficiaryData", {})
                .get("contact", {})
                .get("callStatus"),
                "date_time": f"{task.modified.isoformat()}",
                "user": f"{self.task.customer_service.first_name} {self.task.customer_service.last_name}",
                "expected_call": task.form_data.get("customer_service", {})
                .get("beneficiaryData", {})
                .get("contact", {})
                .get("expectedCall", {}),
            },
        )


class UpdateNegotiatorTaskTestCase(BaseTestQueries):
    def setUp(self):
        super().setUp()
        self.form_schema = FormSchemaFactory(
            key="form-schema-1", json_schema={"form": {}, "UISchema": {}}
        )
        self.beneficiaries = BeneficiaryFactory.create_batch(size=5)
        self.order = OrderFactory(beneficiaries=self.beneficiaries)
        tasks = []
        for beneficiary in self.order.beneficiaries.all():
            task = Task(
                order=self.order,
                beneficiary=beneficiary,
                customer_service=self.customer_service[0],
                negotiator=self.auth_request.user,
                assigned_status=TaskAssignedStatus.ASSIGNED_TO_NEGOTIATOR,
                form_data={
                    "negotiator": {
                        "negotiatorData": {
                            "contact": {
                                "date": "2024-11-08",
                                "time": "02:00 مساءًا - 04:00 مساءًا",
                            },
                        }
                    }
                },
            )
            tasks.append(task)
        Task.objects.bulk_create(tasks)
        self.mutation = """
            mutation MyMutation($formData: JSONString!, $formSchemaKey: String!, $isDraft: Boolean!, $taskId: Int!) {
            updateNegotiatorTaskMutation(
                taskInput: {taskId: $taskId, formData: $formData, formSchemaKey: $formSchemaKey, isDraft: $isDraft}
                ) {
                task {
                created
                assignedStatus
                formData
                id
                modified
                status
                beneficiary {
                code
                }
                customerService {
                id
                }
                order {
                id
                }
                negotiator {
                id
                }
                }
                }
}
        """
        self.task = self.order.tasks.first()
        self.variables = {
            "taskId": self.task.id,
            "formData": '{"placeOrder":{"ID":3329,"locationPreferencesLevelTwo":{"cityId":1,"regionId":96874054,"district":[{"id":119531769}],"mainDivision":[{"id":1,"label":"جنوب"}],"preferredCity":"الرياض","preferredRegion":"منطقة الرياض"}}}',
            "formSchemaKey": self.form_schema.key,
            "isDraft": False,
        }

    def test_mutation_authorizations(self):
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_mutation_with_invalid_task(self):
        self.variables["taskId"] = -1
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.project_manager_auth_request,
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Not Found"))
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"task": _("task matching query doesn't found") % {}},
        )
        # check with invalid permission
        self.variables["taskId"] = self.task.id
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.negotiator_auth_request,
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"user": _("Permission Denied") % {}},
        )

    def test_mutation_with_invalid_form_schema(self):
        self.variables["formSchemaKey"] = ""
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.super_user_auth_request,
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Not Found"))
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"formSchemaKey": _("invalid Form Schema key") % {}},
        )

    def test_mutation_with_valid_data(self):
        form_data = {
            "negotiatorData": {"data": "data"},
        }
        self.variables["isDraft"] = True
        self.task.negotiator = self.super_user_auth_request.user
        self.task.form_data = form_data
        self.task.save()
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.super_user_auth_request,
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)

    def test_mutation_with_invalid_data(self):
        self.task.status = TaskStatus.REJECTED
        self.task.save()
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.super_user_auth_request,
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Bad Request"))
        self.assertEqual(
            response["errors"][0]["extensions"]["http"]["reason"]["status"],
            _("Task is %(status)s, can't be updated")
            % {"status": self.task.get_status_display()},
        )

    def test_mutation_with_multiple_dict_data(self):
        self.variables[
            "formData"
        ] = '{"clientPreferences":{"ID":3329}, "negotiatorData": {}}'
        form_data = self.task.form_data or {}
        self.task.negotiator = self.super_user_auth_request.user
        form_data["placeOrder"] = {"data": "data"}
        self.task.form_data = form_data
        self.task.save()
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.super_user_auth_request,
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Bad Request"))
        self.assertEqual(
            response["errors"][0]["extensions"]["http"]["reason"]["formData"],
            _("invalid key") % {},
        )

    @patch("orders.validators.tasks.UpdateTaskValidation.task_location_validation")
    def test_call_logs_for_negotiator(self, mocked_location_validation):
        mocked_location_validation.return_value = True
        task_form_data = self.task.form_data or {}
        form_data = {"negotiatorData": {"data": "data"}}
        task_form_data.update(form_data)
        self.task.form_data = task_form_data
        self.variables[
            "formData"
        ] = '{"negotiatorData":{"contact": {"date": "2024-11-07","time": "02:00 مساءًا - 04:00 مساءًا" }}}'
        self.task.negotiator = self.super_user_auth_request.user
        self.task.save()
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.super_user_auth_request,
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)
        task = Task.objects.get(id=self.task.id)
        meta_data = task.meta_data or {}
        call_logs = meta_data.get("call_logs", {}).get("negotiator", [])[0]
        self.assertDictEqual(
            call_logs,
            {
                "status": task.form_data.get("customer_service", {})
                .get("beneficiaryData", {})
                .get("contact", {})
                .get("callStatus"),
                "date_time": f"{task.modified.isoformat()}",
                "user": f"{self.task.negotiator.first_name} {self.task.negotiator.last_name}",
                "expected_call": {
                    "expectedCallDate": "2024-11-07",
                    "expectedCallTime": "02:00 مساءًا - 04:00 مساءًا",
                },
            },
        )


class AssignTaskToProjectManagerTestCase(BaseTestQueries):
    def setUp(self):
        super().setUp()
        self.beneficiaries = BeneficiaryFactory.create_batch(size=5)
        self.order = OrderFactory(beneficiaries=self.beneficiaries)
        tasks = []
        for beneficiary in self.order.beneficiaries.all():
            task = Task(
                order=self.order,
                beneficiary=beneficiary,
                customer_service=self.auth_request.user,
                negotiator=self.auth_request.user,
                form_data={
                    "customer_service": {
                        "beneficiaryData": {"data": "data"},
                    },
                },
            )
            tasks.append(task)
        Task.objects.bulk_create(tasks)
        self.task = self.order.tasks.first()
        self.mutation = """
        mutation MyMutation($taskId: Int!) {
          assignTaskToProjectManager(taskInput: {taskId: $taskId}) {
            task {
              assignedStatus
              formData
              id
              status
              order {
                id
              }
              customerService {
                id
              }
              beneficiary {
                code
              }
            }
          }
        }
        """
        self.variables = {"taskId": self.task.id}

    def test_mutation_authorizations(self):
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_mutation_with_invalid_task(self):
        self.variables["taskId"] = -1
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.super_user_auth_request,
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Not Found"))
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"task": _("task matching query doesn't found") % {}},
        )

    def test_mutation_with_invalid_permission(self):
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.negotiator_auth_request,
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"user": _("Permission Denied") % {}},
        )

    def test_mutation_with_valid_data(self):
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["assignTaskToProjectManager"]["task"]
        self.assertDictEqual(
            data["assignedStatus"],
            {
                "key": TaskAssignedStatus.ASSIGNED_TO_PROJECT_MANAGER,
                "display": TaskAssignedStatus.ASSIGNED_TO_PROJECT_MANAGER.label,
            },
        )
        self.assertDictEqual(
            data["status"],
            {
                "key": TaskStatus.PENDING_REVIEW,
                "display": TaskStatus.PENDING_REVIEW.label,
            },
        )
        self.assertDictEqual(
            data["formData"],
            {
                "customer_service": {
                    "beneficiaryData": {"data": "data"},
                },
            },
        )
        self.assertEqual(data["id"], str(self.task.id))
        self.assertEqual(data["order"]["id"], str(self.task.order.id))
        self.assertEqual(
            data["customerService"]["id"], str(self.task.customer_service.id)
        )
        self.assertEqual(data["beneficiary"]["code"], self.task.beneficiary.code)

    def test_mutation_with_invalid_status(self):
        self.task.status = TaskStatus.REJECTED
        self.task.save()
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Bad Request"))
        self.assertEqual(
            response["errors"][0]["extensions"]["http"]["reason"]["status"],
            _("Task is %(status)s, can't be updated")
            % {"status": self.task.get_status_display()},
        )

    def test_mutation_with_invalid_form_data(self):
        self.task.form_data = None
        self.task.save()
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Bad Request"))

        self.assertEqual(
            response["errors"][0]["extensions"]["http"]["reason"]["formData"],
            _("Key '%(key)s' is missing or empty in task %(task_id)s form data")
            % {"key": "beneficiaryData", "task_id": self.task.id},
        )


class AssignTaskToNegotiatorTestCase(BaseTestQueries):
    def setUp(self):
        super().setUp()
        self.beneficiaries = BeneficiaryFactory.create_batch(size=5)
        self.order = OrderFactory(beneficiaries=self.beneficiaries)
        tasks = []
        for beneficiary in self.order.beneficiaries.all():
            task = Task(
                order=self.order,
                beneficiary=beneficiary,
                status=TaskStatus.PENDING_REVIEW,
                customer_service=self.auth_request.user,
                negotiator=self.auth_request.user,
                form_data={
                    "customer_service": {
                        "beneficiaryData": {"data": "data"},
                    },
                },
            )
            tasks.append(task)
        Task.objects.bulk_create(tasks)
        self.task = self.order.tasks.first()
        self.mutation = """
            mutation MyMutation($negotiatorId: Int!, $taskId: [Int]!) {
              assignTaskToNegotiator(
                taskInput: {taskIds: $taskId, negotiatorId: $negotiatorId}
              ) {
                tasks {
                  assignedStatus
                  beneficiary {
                    code
                  }
                  customerService {
                    id
                  }
                  id
                  formData
                  order {
                    id
                  }
                  status
                  negotiator {
                    id
                  }
                }
              }
            }
        """
        self.variables = {"negotiatorId": self.negotiator.id, "taskId": [self.task.id]}

    def test_mutation_authorizations(self):
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_mutation_with_invalid_task(self):
        self.variables["taskId"].append(-1)
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.project_manager_auth_request,
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Not Found"))
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"taskId": _("invalid %(task_ids)s task ids") % {"task_ids": {-1}}},
        )

    def test_mutation_with_invalid_negotiator(self):
        self.variables["negotiatorId"] = -1
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.negotiator_auth_request,
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Not Found"))
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"userId": _("User with id %(user_id)s not found") % {"user_id": -1}},
        )

    def test_mutation_with_invalid_permission(self):
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.negotiator_auth_request,
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"user": _("Permission Denied") % {}},
        )

    def test_mutation_with_valid_data(self):
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.project_manager_auth_request,
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["assignTaskToNegotiator"]["tasks"][0]
        self.assertDictEqual(
            data["status"],
            {
                "key": TaskStatus.CHECK_BENEFICIARY_DATA,
                "display": TaskStatus.CHECK_BENEFICIARY_DATA.label,
            },
        )
        self.assertDictEqual(
            data["formData"],
            {
                "customer_service": {
                    "beneficiaryData": {"data": "data"},
                },
                "negotiator": {
                    "drafts": {"negotiatorData": {"data": "data"}},
                    "negotiatorData": {"data": "data"},
                },
            },
        )
        self.assertEqual(data["id"], str(self.task.id))
        self.assertEqual(data["order"]["id"], str(self.task.order.id))
        self.assertEqual(
            data["customerService"]["id"], str(self.task.customer_service.id)
        )
        self.assertEqual(data["beneficiary"]["code"], self.task.beneficiary.code)

    def test_mutation_with_invalid_status(self):
        self.task.form_data = {}
        self.task.status = TaskStatus.REJECTED
        self.task.save()
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.project_manager_auth_request,
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Bad Request"))
        self.assertEqual(
            response["errors"][0]["extensions"]["http"]["reason"]["status"],
            _("Task is %(status)s, can't be updated")
            % {"status": self.task.get_status_display()},
        )

    def test_mutation_with_invalid_form_data(self):
        self.task.form_data = {}
        self.task.save()
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.project_manager_auth_request,
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Bad Request"))

        self.assertEqual(
            response["errors"][0]["extensions"]["http"]["reason"]["formData"],
            _("Key '%(key)s' is missing or empty in task %(task_id)s form data")
            % {"key": "beneficiaryData", "task_id": self.task.id},
        )


class RejectTaskTestCase(BaseTestQueries):
    def setUp(self):
        super().setUp()
        self.beneficiaries = BeneficiaryFactory.create_batch(size=5)
        self.order = OrderFactory(beneficiaries=self.beneficiaries)
        tasks = []
        for beneficiary in self.order.beneficiaries.all():
            task = Task(
                order=self.order,
                beneficiary=beneficiary,
                status=TaskStatus.PENDING_REVIEW,
                customer_service=self.auth_request.user,
                form_data={"customer_service": {"beneficiaryData": {"data": "data"}}},
            )
            tasks.append(task)
        Task.objects.bulk_create(tasks)
        self.task = self.order.tasks.first()
        self.form_schema = FormSchemaFactory(
            json_schema={"form": {}, "UISchema": {}}, key="client_preferences"
        )
        self.form_data = '{"beneficiaryData": {"data":"data"}}'
        self.mutation = """
        mutation MyMutation($formData: JSONString!, $formSchemaKey: String!, $taskId: Int!) {
          rejectTaskMutation(
            taskInput: {taskId: $taskId, formData: $formData, formSchemaKey: $formSchemaKey}
          ) {
            task {
              assignedStatus
              formData
              id
              status
              order {
                id
              }
              customerService {
                id
              }
              beneficiary {
                code
              }
            }
          }
        }
        """
        self.variables = {
            "formData": self.form_data,
            "taskId": self.task.id,
            "formSchemaKey": self.form_schema.key,
        }

    def test_mutation_authorizations(self):
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_mutation_with_invalid_task(self):
        self.variables["taskId"] = -1
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.project_manager_auth_request,
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Not Found"))
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"task": _("task matching query doesn't found") % {}},
        )

    def test_mutation_with_invalid_permission(self):
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.negotiator_auth_request,
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"user": _("Permission Denied") % {}},
        )

    def test_mutation_with_invalid_form_schema(self):
        self.variables["formSchemaKey"] = "rty"
        self.task.negotiator = self.negotiator_auth_request.user
        self.task.save()
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.negotiator_auth_request,
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Not Found"))
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"formSchemaKey": _("invalid Form Schema key") % {}},
        )

    def test_mutation_with_valid_data(self):
        self.task.negotiator = self.negotiator_auth_request.user
        self.task.save()
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.negotiator_auth_request,
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["rejectTaskMutation"]["task"]
        self.assertDictEqual(
            data["assignedStatus"],
            {
                "key": TaskAssignedStatus.NOT_ASSIGNED,
                "display": TaskAssignedStatus.NOT_ASSIGNED.label,
            },
        )
        self.assertDictEqual(
            data["status"],
            {
                "key": TaskStatus.REJECTED,
                "display": TaskStatus.REJECTED.label,
            },
        )
        self.assertDictEqual(
            data["formData"],
            self.task.form_data,
        )
        self.assertEqual(data["id"], str(self.task.id))
        self.assertEqual(data["order"]["id"], str(self.task.order.id))
        self.assertEqual(
            data["customerService"]["id"], str(self.task.customer_service.id)
        )
        self.assertEqual(data["beneficiary"]["code"], self.task.beneficiary.code)

    def test_mutation_with_invalid_data(self):
        self.task.form_data = {}
        self.task.status = TaskStatus.REJECTED
        self.task.save()
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.super_user_auth_request,
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Bad Request"))
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {
                "taskId": _("Task is %(status)s, can't be deleted or rejected")
                % {"status": self.task.get_status_display()}
            },
        )
        self.task.status = TaskStatus.IN_PROGRESS
        self.task.save()
        favorite_offer = FavoriteOfferFactory(task=self.task, offer_id=100)
        DealFactory(favorite_offer=favorite_offer)
        # Check if the query is successful
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.super_user_auth_request,
        )
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Bad Request"))
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"taskId": _("this task has ongoing deal") % {}},
        )


class DeleteTaskTestCase(BaseTestQueries):
    def setUp(self):
        super().setUp()
        self.beneficiaries = BeneficiaryFactory.create_batch(size=5)
        self.order = OrderFactory(beneficiaries=self.beneficiaries)
        tasks = []
        for beneficiary in self.order.beneficiaries.all():
            task = Task(
                order=self.order,
                beneficiary=beneficiary,
                status=TaskStatus.PENDING_REVIEW,
                customer_service=self.auth_request.user,
                form_data={
                    "clientPreferences": {"data": "data"},
                    "beneficiaryData": {"data": "data"},
                },
            )
            tasks.append(task)
        Task.objects.bulk_create(tasks)
        self.task = self.order.tasks.first()
        self.mutation = """
            mutation MyMutation($taskId: Int!) {
              deleteTaskMutation(taskId: $taskId) {
                status
              }
            }
        """
        self.variables = {"taskId": self.task.id}

    def test_mutation_authorizations(self):
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_mutation_with_invalid_task(self):
        self.variables["taskId"] = -1
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.project_manager_auth_request,
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Not Found"))
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"task": _("task matching query doesn't found") % {}},
        )

    def test_mutation_with_invalid_permission(self):
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.negotiator_auth_request,
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"user": _("Permission Denied") % {}},
        )

    def test_mutation_with_valid_data(self):
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.project_manager_auth_request,
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["deleteTaskMutation"]
        self.assertEqual(data["status"], _("Task Deleted Successfully") % {})

    def test_mutation_with_invalid_data(self):
        self.task.status = TaskStatus.REJECTED
        self.task.save()
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.project_manager_auth_request,
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Bad Request"))
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {
                "taskId": _("Task is %(status)s, can't be deleted or rejected")
                % {"status": self.task.get_status_display()}
            },
        )
        self.task.status = TaskStatus.IN_PROGRESS
        self.task.save()
        favorite_offer = FavoriteOfferFactory(task=self.task, offer_id=100)
        DealFactory(favorite_offer=favorite_offer)
        # Check if the query is successful
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.project_manager_auth_request,
        )
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Bad Request"))
        self.assertEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"taskId": _("this task has ongoing deal") % {}},
        )


class ReassignTaskToCustomerServiceTestCase(BaseTestQueries):
    def setUp(self):
        super().setUp()
        self.beneficiaries = BeneficiaryFactory.create_batch(size=5)
        self.order = OrderFactory(beneficiaries=self.beneficiaries)
        tasks = []
        for beneficiary in self.order.beneficiaries.all():
            task = Task(
                order=self.order,
                beneficiary=beneficiary,
                status=TaskStatus.PENDING_REVIEW,
                customer_service=self.auth_request.user,
                form_data={
                    "clientPreferences": {"data": "data"},
                    "beneficiaryData": {"data": "data"},
                },
            )
            tasks.append(task)
        Task.objects.bulk_create(tasks)
        self.task = self.order.tasks.first()
        self.mutation = """
        mutation MyMutation($tasksIds: [Int]!, $userId: Int!) {
          reassignTaskToCustomerService(taskInput: {tasksIds: $tasksIds, userId: $userId}) {
            tasks {
            formData
              status
              order {
                id
              }
              id
              customerService {
                id
              }
              beneficiary {
                code
              }
              assignedStatus
            }
          }
        }
        """
        self.variables = {
            "tasksIds": [self.task.id],
            "userId": self.customer_service[0].id,
        }

    def test_mutation_authorizations(self):
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_mutation_with_invalid_permission(self):
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.negotiator_auth_request,
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"user": _("Permission Denied") % {}},
        )

    def test_mutation_with_invalid_user(self):
        self.variables["userId"] = -1
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.project_manager_auth_request,
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Not Found"))
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"userId": _("User with id %(user_id)s not found") % {"user_id": -1}},
        )

    def test_mutation_with_invalid_task(self):
        self.variables["tasksIds"].append(-1)
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.project_manager_auth_request,
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Not Found"))
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"taskId": _("invalid %(task_ids)s task ids") % {"task_ids": {-1}}},
        )

    def test_mutation_with_valid_data(self):
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.project_manager_auth_request,
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["reassignTaskToCustomerService"]["tasks"][0]
        self.assertDictEqual(
            data["assignedStatus"],
            {
                "key": TaskAssignedStatus.ASSIGNED_TO_CUSTOMER_SERVICE,
                "display": TaskAssignedStatus.ASSIGNED_TO_CUSTOMER_SERVICE.label,
            },
        )
        self.assertDictEqual(
            data["status"],
            {
                "key": self.task.status,
                "display": self.task.get_status_display(),
            },
        )
        self.assertDictEqual(
            data["formData"],
            self.task.form_data,
        )
        self.assertEqual(data["id"], str(self.task.id))
        self.assertEqual(data["order"]["id"], str(self.task.order.id))
        self.assertEqual(
            data["customerService"]["id"], str(self.customer_service[0].id)
        )
        self.assertEqual(data["beneficiary"]["code"], self.task.beneficiary.code)

    def test_mutation_with_invalid_data(self):
        self.task.status = TaskStatus.REJECTED
        self.task.save()
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.project_manager_auth_request,
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Bad Request"))
        self.assertEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {
                "taskId": _("Tasks with ids %(ids)s, can't be reassigned")
                % {"ids": [self.task.id]}
            },
        )


class ReassignTaskToNegotiatorTestCase(BaseTestQueries):
    def setUp(self):
        super().setUp()
        self.beneficiaries = BeneficiaryFactory.create_batch(size=5)
        self.order = OrderFactory(beneficiaries=self.beneficiaries)
        tasks = []
        for beneficiary in self.order.beneficiaries.all():
            task = Task(
                order=self.order,
                beneficiary=beneficiary,
                status=TaskStatus.IN_PROGRESS,
                assigned_status=TaskAssignedStatus.ASSIGNED_TO_NEGOTIATOR,
                customer_service=self.customer_service[0],
                negotiator=self.negotiator,
                form_data={
                    "clientPreferences": {"data": "data"},
                    "beneficiaryData": {"data": "data"},
                },
            )
            tasks.append(task)
        Task.objects.bulk_create(tasks)
        self.task = self.order.tasks.first()
        self.mutation = """
            mutation MyMutation($tasksIds: [Int]!, $userId: Int!) {
              reassignTaskToNegotiator(taskInput: {tasksIds: $tasksIds, userId: $userId}) {
                tasks {
                  assignedStatus
                  formData
                  id
                  status
                  order {
                    id
                  }
                  customerService {
                    id
                  }
                negotiator {
                    id
                  }
                  beneficiary {
                    code
                  }
                }
              }
            }
        """
        self.variables = {"tasksIds": [self.task.id], "userId": self.negotiator.id}

    def test_mutation_authorizations(self):
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_mutation_with_invalid_permission(self):
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.negotiator_auth_request,
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"user": _("Permission Denied") % {}},
        )

    def test_mutation_with_invalid_user(self):
        self.variables["userId"] = -1
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.project_manager_auth_request,
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Not Found"))
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"userId": _("User with id %(user_id)s not found") % {"user_id": -1}},
        )

    def test_mutation_with_invalid_task(self):
        self.variables["tasksIds"].append(-1)
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.project_manager_auth_request,
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Not Found"))
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"taskId": _("invalid %(task_ids)s task ids") % {"task_ids": {-1}}},
        )

    def test_mutation_with_valid_data(self):
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.project_manager_auth_request,
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["reassignTaskToNegotiator"]["tasks"][0]
        self.assertDictEqual(
            data["assignedStatus"],
            {
                "key": TaskAssignedStatus.ASSIGNED_TO_NEGOTIATOR,
                "display": TaskAssignedStatus.ASSIGNED_TO_NEGOTIATOR.label,
            },
        )
        self.assertDictEqual(
            data["status"],
            {
                "key": self.task.status,
                "display": self.task.get_status_display(),
            },
        )
        self.assertDictEqual(
            data["formData"],
            self.task.form_data,
        )
        self.assertEqual(data["id"], str(self.task.id))
        self.assertEqual(data["order"]["id"], str(self.task.order.id))
        self.assertEqual(
            data["customerService"]["id"], str(self.customer_service[0].id)
        )
        self.assertEqual(data["negotiator"]["id"], str(self.negotiator.id))
        self.assertEqual(data["beneficiary"]["code"], self.task.beneficiary.code)

    def test_mutation_with_invalid_data(self):
        self.task.status = TaskStatus.REJECTED
        self.task.save()
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.project_manager_auth_request,
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Bad Request"))
        self.assertEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {
                "taskId": _("Tasks with ids %(ids)s, can't be reassigned")
                % {"ids": [self.task.id]}
            },
        )
        self.task.status = TaskStatus.IN_PROGRESS
        self.task.negotiator = None
        self.task.save()
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.project_manager_auth_request,
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Bad Request"))
        self.assertEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {
                "tasksIds": _(
                    "Tasks with ids %(ids)s have no negotiator, please assign to negotiator first"
                )
                % {"ids": [self.task.id]}
            },
        )


class CreateNoteTestCase(BaseTestQueries):
    def setUp(self):
        super().setUp()
        self.beneficiaries = BeneficiaryFactory.create_batch(size=5)
        self.order = OrderFactory(beneficiaries=self.beneficiaries)
        tasks = []
        for beneficiary in self.order.beneficiaries.all():
            task = Task(
                order=self.order,
                beneficiary=beneficiary,
                status=TaskStatus.IN_PROGRESS,
                assigned_status=TaskAssignedStatus.ASSIGNED_TO_NEGOTIATOR,
                customer_service=self.customer_service[0],
                form_data={
                    "clientPreferences": {"data": "data"},
                    "beneficiaryData": {"data": "data"},
                },
            )
            tasks.append(task)
        Task.objects.bulk_create(tasks)
        self.task = self.order.tasks.first()
        self.mutation = """
        mutation MyMutation($note: String!, $taskId: Int!) {
          createNote(noteInput: {taskId: $taskId, note: $note}) {
            note {
              created
              note
              task {
                id
              }
              createdBy {
                email
                id
              }
              modified
            }
          }
        }
        """
        self.variables = {"note": "any note", "taskId": self.task.id}

    def test_mutation_authorizations(self):
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_mutation_with_invalid_permission(self):
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.negotiator_auth_request,
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"user": _("Permission Denied") % {}},
        )

    def test_mutation_with_invalid_task(self):
        self.variables["taskId"] = -1
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.project_manager_auth_request,
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Not Found"))
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"task": _("task matching query doesn't found") % {}},
        )

    def test_mutation_with_valid_data(self):
        response = self.client.execute(
            self.mutation,
            variables=self.variables,
            context=self.project_manager_auth_request,
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["createNote"]["note"]
        self.assertEqual(data["task"]["id"], str(self.task.id))
        self.assertEqual(
            data["createdBy"]["id"], str(self.project_manager_auth_request.user.id)
        )


class TestSignedUrl(BaseTestQueries):
    def setUp(self):
        super().setUp()
        self.beneficiaries = BeneficiaryFactory.create_batch(size=5)
        self.order = OrderFactory(beneficiaries=self.beneficiaries)
        tasks = []
        for beneficiary in self.order.beneficiaries.all():
            task = Task(
                order=self.order,
                beneficiary=beneficiary,
                status=TaskStatus.IN_PROGRESS,
                assigned_status=TaskAssignedStatus.ASSIGNED_TO_NEGOTIATOR,
                customer_service=self.customer_service[0],
                negotiator=self.negotiator,
                form_data={
                    "clientPreferences": {"data": "data"},
                    "beneficiaryData": {"data": "data"},
                },
            )
            tasks.append(task)
        Task.objects.bulk_create(tasks)
        self.task = self.order.tasks.first()
        self.mutation = """
            mutation MyMutation($fileExtension: String!, $taskId: Int!) {
              signedUrl(fileExtension: $fileExtension, taskId: $taskId) {
                blobUrl
                signedUrl
              }
            }
        """
        self.variables = {"fileExtension": "png", "taskId": self.task.id}

    def test_mutation_authorizations(self):
        result = self.client.execute(
            self.mutation, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", result)
        self.assertEqual(result["errors"][0]["message"], "Unauthorized")

    def test_mutation_with_invalid_task(self):
        self.variables["taskId"] = -1
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Not Found") % {})
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"taskId": _("invalid %(task_id)s task id") % {"task_id": -1}},
        )

    @patch("orders.schema.mutations.generate_upload_signed_url_v4")
    def test_mutation_with_valid_signed_url(self, mock_generate_upload_signed_url_v4):
        mock_generate_upload_signed_url_v4.return_value = (
            f"http://mock_url/media/attachments/task/{self.task.id}/image.jpg"
        )
        result = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", result)
        data = result["data"]["signedUrl"]
        self.assertEqual(
            data["signedUrl"], mock_generate_upload_signed_url_v4.return_value
        )
