from unittest.mock import patch

from dateutil.parser import parse
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AnonymousUser
from django.db.models import Max
from django.test import TestCase
from django.utils.translation import activate
from django.utils.translation import gettext_lazy as _
from graphene.test import Client
from mock.mock import MagicMock

from app.schema import schema
from orders.models import Task, Order, TaskAssignedStatus
from orders.strategies.recommendations import (
    LOCATION_PREFERENCES_MAPPER,
    ListRecommendationStrategy,
)
from users.models import UserRoleChoices
from utils.tests.factories import (
    UserFactory,
    RenRoleFactory,
    BeneficiaryFactory,
    OrderFactory,
    NoteFactory,
)

User = get_user_model()


class BaseTestQueries(TestCase):
    def setUp(self):
        activate("ar")
        self.roles = {
            UserRoleChoices.CUSTOMER_SERVICES.value: RenRoleFactory(
                role=UserRoleChoices.CUSTOMER_SERVICES
            ),
            UserRoleChoices.PROJECT_MANAGERS.value: RenRoleFactory(
                role=UserRoleChoices.PROJECT_MANAGERS
            ),
            UserRoleChoices.NEGOTIATORS.value: RenRoleFactory(
                role=UserRoleChoices.NEGOTIATORS
            ),
        }
        self.user = UserFactory()
        self.superuser = UserFactory(
            is_superuser=True,
            roles=[self.roles[UserRoleChoices.PROJECT_MANAGERS.value]],
        )
        self.negotiator = UserFactory(
            roles=[self.roles[UserRoleChoices.NEGOTIATORS.value]]
        )
        self.project_manager = UserFactory(
            roles=[self.roles[UserRoleChoices.PROJECT_MANAGERS.value]]
        )
        self.customer_service = UserFactory.create_batch(
            size=5, roles=[self.roles[UserRoleChoices.CUSTOMER_SERVICES.value]]
        )
        self.client = Client(schema)
        self.auth_request = MagicMock()
        self.auth_request.user = self.user
        self.non_auth_request = MagicMock()
        self.non_auth_request.user = AnonymousUser()
        self.maxDiff = None


class OrderTestCase(BaseTestQueries):
    def setUp(self):
        super().setUp()
        self.beneficiaries = BeneficiaryFactory.create_batch(size=5)
        self.order = OrderFactory(beneficiaries=self.beneficiaries)
        tasks = []
        for beneficiary in self.order.beneficiaries.all():
            task = Task(
                order=self.order,
                beneficiary=beneficiary,
                customer_service=self.customer_service[0],
                negotiator=self.auth_request.user,
            )
            tasks.append(task)
        Task.objects.bulk_create(tasks)

        self.query = """
        query MyQuery {
          orders {
            data {
              tasksCount
              startDate
              rejectedTasksCount
              modified
              id
              pendingReviewTasksCount
              endDate
              created
              assignedToNegotiatorTaskCount
              assignedToCustomerServicesTaskCount
            }
            count
          }
}
        """

    def test_query_authorization(self):
        response = self.client.execute(self.query, context=self.non_auth_request)
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_query_with_invalid_permission(self):
        auth_request = MagicMock()
        auth_request.user = self.customer_service[0]
        response = self.client.execute(self.query, context=auth_request)
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))
        self.assertEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"user": _("Permission Denied") % {}},
        )

    def test_list_query(self):
        auth_request = MagicMock()
        auth_request.user = self.project_manager
        response = self.client.execute(self.query, context=auth_request)
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["orders"]
        self.assertEqual(data["count"], 1)
        data = data["data"][0]
        order = Order.objects.get(id=data["id"])
        self.assertEqual(data["tasksCount"], 5)
        self.assertEqual(parse(data["startDate"]), order.start_date)
        self.assertEqual(parse(data["endDate"]), order.end_date)
        self.assertEqual(data["assignedToCustomerServicesTaskCount"], 5)
        self.assertEqual(data["assignedToNegotiatorTaskCount"], 0)
        self.assertEqual(data["pendingReviewTasksCount"], 0)
        self.assertEqual(data["rejectedTasksCount"], 0)

    def test_list_order_qs(self):
        auth_request = MagicMock()
        auth_request.user = self.project_manager
        # create 2nd order
        beneficiaries = BeneficiaryFactory.create_batch(size=5)
        order = OrderFactory(beneficiaries=beneficiaries)
        tasks = []
        for beneficiary in order.beneficiaries.all():
            task = Task(
                order=order,
                beneficiary=beneficiary,
                customer_service=self.customer_service[0],
                negotiator=self.auth_request.user,
            )
            tasks.append(task)
        Task.objects.bulk_create(tasks)  # update task in 1st order
        task = self.order.tasks.first()
        task.form_data = {}
        task.save(update_fields=["form_data"])
        order = Order.objects.annotate(
            last_task_update=Max("tasks__modified")
        ).order_by("-last_task_update")
        response = self.client.execute(self.query, context=auth_request)
        self.assertNotIn("errors", response)
        data = response["data"]["orders"]
        self.assertEqual(data["count"], 2)
        data = data["data"][0]
        self.assertEqual(data["id"], str(order[0].id))


class TaskTestCase(BaseTestQueries):
    def setUp(self):
        super().setUp()
        self.beneficiaries = BeneficiaryFactory.create_batch(size=5)
        self.order = OrderFactory(beneficiaries=self.beneficiaries)
        tasks = []
        for beneficiary in self.order.beneficiaries.all():
            task = Task(
                order=self.order,
                beneficiary=beneficiary,
                customer_service=self.customer_service[0],
            )
            tasks.append(task)
        Task.objects.bulk_create(tasks)

        self.query = """
        query MyQuery {
          tasks(role: ADMIN) {
        data {
          assignedStatus
          created
          formData
          id
          modified
          status
          beneficiary {
            code
            externalId
            name
          }
          customerService {
            avatar
            email
            firstName
            isStaff
            id
            isSuperuser
            lastName
            phone
            roles {
              role
            }
          }
          negotiator {
            avatar
            email
            firstName
            id
            isStaff
            isSuperuser
            phone
            roles {
              role
            }
            lastName
          }
          order {
            assignedToCustomerServicesTaskCount
            assignedToNegotiatorTaskCount
            created
            endDate
            id
            modified
            pendingReviewTasksCount
            rejectedTasksCount
            startDate
            tasksCount
          }
        }
        count
      }
    }
        """

    def test_query_authorization(self):
        response = self.client.execute(self.query, context=self.non_auth_request)
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_query_with_customer_service(self):
        auth_request = MagicMock()
        auth_request.user = self.customer_service[0]
        response = self.client.execute(self.query, context=auth_request)
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["tasks"]
        self.assertEqual(data["count"], 5)
        data = data["data"][0]
        task = Task.objects.get(id=data["id"])
        self.assertDictEqual(
            data["assignedStatus"],
            {
                "key": task.assigned_status,
                "display": task.get_assigned_status_display(),
            },
        )
        self.assertDictEqual(
            data["status"], {"key": task.status, "display": task.get_status_display()}
        )
        self.assertIsNone(data["formData"])
        beneficiary = data["beneficiary"]
        customer_service = data["customerService"]
        order = data["order"]
        self.assertEqual(beneficiary["code"], task.beneficiary.code)
        self.assertEqual(customer_service["id"], str(task.customer_service_id))
        self.assertIsNone(data["negotiator"])
        self.assertEqual(order["id"], str(task.order_id))

    def test_query_with_negotiator(self):
        for task in self.order.tasks.all():
            task.negotiator = self.auth_request.user
            task.save()
        auth_request = MagicMock()
        response = self.client.execute(self.query, context=auth_request)
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["tasks"]
        self.assertEqual(data["count"], 5)

    def test_query_with_project_manager(self):
        self.auth_request.user = self.project_manager
        auth_request = MagicMock()
        response = self.client.execute(self.query, context=auth_request)
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["tasks"]
        self.assertEqual(data["count"], 5)


class RecommendationTestCase(BaseTestQueries):
    def setUp(self):
        super().setUp()
        self.beneficiaries = BeneficiaryFactory.create_batch(size=5)
        self.order = OrderFactory(beneficiaries=self.beneficiaries)
        tasks = []
        self.auth_request.user = self.negotiator
        for beneficiary in self.order.beneficiaries.all():
            task = Task(
                order=self.order,
                beneficiary=beneficiary,
                customer_service=self.customer_service[0],
            )
            tasks.append(task)
        Task.objects.bulk_create(tasks)
        self.task = self.order.tasks.first()
        self.task.assigned_status = TaskAssignedStatus.ASSIGNED_TO_NEGOTIATOR
        self.task.negotiator = self.auth_request.user
        self.task.form_data = {
            "customer_service": {
                "beneficiaryData": {
                    "enthusiasmLevel": {"enthusiasm": "60%"},
                    "locationPreferences": {
                        "cityId": 25,
                        "regionId": 96873934,
                        "mainDivision": [
                            {"id": 12, "label": "شرق"},
                            {"id": 13, "label": "شمال"},
                            {"id": 15, "label": "وسط"},
                        ],
                        "preferredCity": "مكة المكرمة",
                        "preferredRegion": "منطقة مكة المكرمة",
                    },
                    "financialPreferences": {
                        "purchaseDesire": "الحد الأعلى للتمويل",
                        "purchaseMechanism": "تمويل",
                        "preferredPurchaseAmount": 2500000,
                    },
                    "negotiatorContactTime": {
                        "contactDate": "2024-08-29",
                        "contactTime": "12:00 مساءًا - 02:00 مساءًا",
                    },
                    "realEstatePreferences": {"preferredPropertyType": "فيلا دوبلكس"},
                }
            },
            "negotiator": {
                "placeOrder": {
                    "realEstatePreferences": {"preferredPurchaseAmount": "فيلا دوبلكس"},
                    "locationPreferencesLevelTwo": {
                        "cityId": 25,
                        "district": [
                            {"id": 119581858, "label": "اجياد"},
                            {"id": 119660024, "label": "التنعيم"},
                            {"id": 119805565, "label": "البركه"},
                            {"id": 119805541, "label": "التيسير"},
                            {"id": 119688964, "label": "احد"},
                            {"id": 119805544, "label": "البحيرات"},
                            {"id": 119805559, "label": "الترويه"},
                        ],
                        "regionId": 96873934,
                        "mainDivision": [
                            {"id": 12, "label": "شرق"},
                            {"id": 13, "label": "شمال"},
                            {"id": 15, "label": "وسط"},
                        ],
                        "preferredCity": "مكة المكرمة",
                        "preferredRegion": "منطقة مكة المكرمة",
                    },
                    "financialPreferencesLevelTwo": {
                        "maximumPurchaseAmount": 5000000,
                        "minimumPurchaseAmount": 500000,
                        "preferredPurchaseAmount": 2500000,
                    },
                },
                "negotiatorData": {"test": "test"},
            },
        }
        self.task.save()
        self.query = """
        query MyQuery($taskId: Int!, $level: Level!) {
          recommendations(level: $level, taskId: $taskId) {
            data
          }
        }
        """
        self.variables = {"taskId": self.task.id, "level": "CITY"}

    def test_query_authorization(self):
        response = self.client.execute(
            self.query, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    @patch("utils.locations.Locations.preferences")
    def test_list_query(self, gateway_request_data):
        gateway_request_data.return_value = {}, None
        response = self.client.execute(
            self.query, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["recommendations"]
        self.assertEqual(data, {"data": {"boundaries": None, "suitabilities": []}})

    def test_query_with_invalid_task(self):
        self.variables["taskId"] = 123
        response = self.client.execute(
            self.query, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Not Found"))
        self.assertEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {
                "task": _("task matching query doesn't found") % {},
            },
        )

    def test_query_with_invalid_user(self):
        self.auth_request.user = self.customer_service[0]
        response = self.client.execute(
            self.query, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))
        self.assertEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"user": _("Permission Denied") % {}},
        )
        negotiator = UserFactory(roles=[self.roles[UserRoleChoices.NEGOTIATORS.value]])
        self.auth_request.user = negotiator
        response = self.client.execute(
            self.query, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))
        self.assertEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"user": _("Permission Denied") % {}},
        )

    def test_query_with_invalid_form_data(self):
        self.task.form_data = None
        self.task.save()
        response = self.client.execute(
            self.query, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Bad Request"))
        self.assertEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"formData": _("Please Submit Form Data First") % {}},
        )
        self.task.form_data = {"data": "data"}
        self.variables["level"] = "DISTRICT"
        self.task.save()
        response = self.client.execute(
            self.query, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Bad Request"))
        self.assertEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"formData": _("please Submit first level") % {}},
        )
        self.task.form_data = {
            "customer_service": {"beneficiaryData": "test"},
            "negotiator": {"placeOrder": "test"},
        }
        self.task.save()
        response = self.client.execute(
            self.query, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Bad Request"))
        self.assertEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"formData": _("please Submit second level") % {}},
        )

    @patch("utils.locations.Locations.preferences")
    def test_level_two_query_with_invalid_district(self, gateway_request_data):
        self.variables["level"] = "DISTRICT"
        self.task.form_data["negotiator"].update(
            {
                "placeOrder": {
                    "realEstatePreferences": {"preferredPurchaseAmount": "فيلا دوبلكس"},
                    "locationPreferencesLevelTwo": {
                        "cityId": 25,
                        "regionId": 96873934,
                        "mainDivision": [
                            {"id": 12, "label": "شرق"},
                            {"id": 13, "label": "شمال"},
                            {"id": 15, "label": "وسط"},
                        ],
                        "preferredCity": "مكة المكرمة",
                        "preferredRegion": "منطقة مكة المكرمة",
                    },
                    "financialPreferencesLevelTwo": {
                        "maximumPurchaseAmount": 5000000,
                        "minimumPurchaseAmount": 500000,
                        "preferredPurchaseAmount": 2500000,
                    },
                }
            }
        )
        self.task.save()
        gateway_request_data.return_value = {}, None
        response = self.client.execute(
            self.query, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Bad Request"))
        self.assertEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"district": _("District is Required") % {}},
        )

    @patch("utils.locations.Locations.preferences")
    def test_makan_response_with_invalid_response(self, gateway_request_data):
        gateway_request_data.return_value = None, "invalid data"
        response = self.client.execute(
            self.query, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Bad Request"))
        self.assertEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"error": "invalid data"},
        )

    def test_get_location_preferences_level_key(self):
        form_data = self.task.form_data
        level = ListRecommendationStrategy().get_location_preferences_level_key(
            form_data
        )
        self.assertEqual(level, "level1")
        self.task.form_data = {}
        self.task.save()
        level = ListRecommendationStrategy().get_location_preferences_level_key(
            self.task.form_data
        )
        self.assertEqual(level, "level1")

    def test_location_preferences_mapper(self):
        mapper = LOCATION_PREFERENCES_MAPPER
        self.assertIn("level1", mapper)
        self.assertIn("level2", mapper)
        level_1 = mapper["level1"]
        self.assertIn("city", level_1)
        self.assertIn("district", level_1)
        self.assertIn("division", level_1)
        self.assertIn("prefer_price", level_1)
        self.assertIn("property_type", level_1)
        level_2 = mapper["level2"]
        self.assertIn("city", level_2)
        self.assertIn("district", level_2)
        self.assertIn("division", level_2)
        self.assertIn("prefer_price", level_2)
        self.assertIn("property_type", level_2)


class NoteTestCase(BaseTestQueries):
    def setUp(self):
        super().setUp()
        self.beneficiaries = BeneficiaryFactory.create_batch(size=5)
        self.order = OrderFactory(beneficiaries=self.beneficiaries)
        tasks = []
        for beneficiary in self.order.beneficiaries.all():
            task = Task(
                order=self.order,
                beneficiary=beneficiary,
                customer_service=self.customer_service[0],
            )
            tasks.append(task)
        Task.objects.bulk_create(tasks)
        self.task = Task.objects.last()
        self.notes = NoteFactory.create_batch(
            5, task=self.task, created_by=self.auth_request.user
        )
        self.query = """
        query MyQuery2($taskId: Int!) {
          notes(taskId: $taskId) {
          count
            data {
              created
              modified
              note
              task {
                id
              }
              createdBy {
                id
              }
            }
          }
        }
        """
        self.variables = {"taskId": self.task.id}

    def test_query_authorization(self):
        response = self.client.execute(
            self.query, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_query_with_invalid_task(self):
        self.variables["taskId"] = 123
        response = self.client.execute(
            self.query, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Not Found"))
        self.assertEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {
                "task": _("task matching query doesn't found") % {},
            },
        )

    def test_query_with_invalid_permission(self):
        self.auth_request.user = self.negotiator
        response = self.client.execute(
            self.query, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))
        self.assertEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"user": _("Permission Denied") % {}},
        )

    def test_list_query(self):
        self.auth_request.user = self.negotiator
        self.task.negotiator = self.auth_request.user
        self.task.save()
        response = self.client.execute(
            self.query, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["notes"]
        self.assertEqual(data["count"], 5)
