import logging
from abc import ABC, abstractmethod

from gabbro.graphene.exceptions import BadRequest

from auditing.mixins import AuditMixin
from orders.models import Order
from users.models import User
from utils.general_utils import get_nested_value
from utils.locations import locations

logger = logging.getLogger("orders")

LOCATION_PREFERENCES_MAPPER = {
    "level1": {
        "city": "negotiatorData.locationPreferences.cityId",
        "district": "negotiatorData.locationPreferences.district",
        "division": "negotiatorData.locationPreferences.mainDivision",
        "prefer_price": "negotiatorData.financialPreferences.preferredPurchaseAmount",
        "property_type": "negotiatorData.realEstatePreferences.preferredPropertyType",
    },
    "level2": {
        "city": "placeOrder.locationPreferencesLevelTwo.cityId",
        "district": "placeOrder.locationPreferencesLevelTwo.district",
        "division": "placeOrder.locationPreferencesLevelTwo.mainDivision",
        "prefer_price": "placeOrder.financialPreferencesLevelTwo.preferredPurchaseAmount",
        "property_type": "placeOrder.realEstatePreferences.preferredPropertyType",
    },
}


class RecommendationStrategy(ABC):
    @abstractmethod
    def resolve_recommendations(
        self, user: User, input_data: dict, context: dict
    ) -> Order:
        """Method that all recommendations strategies must implement"""
        pass


class ListRecommendationStrategy(RecommendationStrategy, AuditMixin):
    @staticmethod
    def get_property_type_key(property_type):
        return "Apartment" if property_type and property_type == "شقة" else "General"

    @staticmethod
    def get_location_preferences_level_key(form_data: dict) -> str:
        if form_data.get("placeOrder", {}):
            return "level2"
        return "level1"

    def get_gateway_request_data(
        self, prefer_price, property_type, level, city, districts, zones
    ) -> dict:

        return {
            "input": {
                "prefer_price": prefer_price,
                "property_type": self.get_property_type_key(property_type),
                "retrun_details": False if level == "city" else True,
                **locations.get_location_for_integration_request_input(
                    level=level, city=city, districts=districts, zones=zones
                ),
            },
            "skus": ["RECOMMEND_DISTRICTS_BY_PREFERENCES"],
        }

    def resolve_recommendations(
        self, user: User, input_data: dict, context: dict
    ) -> dict:
        task = context["task"]
        level = context["level"]
        form_data = task.form_data.get("negotiator")
        level_key = self.get_location_preferences_level_key(form_data=form_data)
        key_path = LOCATION_PREFERENCES_MAPPER[level_key]
        preferences_keys = [
            "city",
            "district",
            "division",
            "prefer_price",
            "property_type",
        ]
        preferences_values = list(
            get_nested_value(data=form_data, path=key_path[key])
            for key in preferences_keys
        )

        city, districts, zones, prefer_price, property_type = preferences_values
        request_body = self.get_gateway_request_data(
            prefer_price=prefer_price,
            property_type=property_type,
            level=level,
            city=city,
            districts=districts,
            zones=zones,
        )
        makan_data, error = locations.preferences(data=request_body)
        if error:
            raise BadRequest(reason={"error": error})
        # implement geometry per record
        locations.get_location_coordinates(makan_data=makan_data)
        return makan_data
