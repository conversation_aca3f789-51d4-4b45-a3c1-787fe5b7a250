import logging
from abc import ABC, abstractmethod
from typing import Union, Optional, Dict

from django.contrib.auth import get_user_model
from django.db.models import QuerySet
from gabbro.graphene import BadRequest
from notification_manager.mixins import NotificationMixin
from auditing.mixins import AuditMixin
from formschemas.mixins import FormSchemaMixin
from orders.mixins import TaskMixin
from orders.models import Task, TaskStatus, TaskAssignedStatus
from orders.serializers import (
    UpdateTaskSerializer,
    AssignTaskToProjectManagerSerializer,
    AssignTaskToNegotiatorSerializer,
    RejectTaskSerializer,
    DeleteTaskSerializer,
)
from users.models import UserRoleChoices

logger = logging.getLogger("orders")

User = get_user_model()


class TaskStrategy(ABC, TaskMixin, FormSchemaMixin, AuditMixin, NotificationMixin):
    @abstractmethod
    def update_task(
        self, user: User, task_input: dict, context: dict
    ) -> Optional[Union[Task, QuerySet[Task]]]:
        """Method that all task strategies must implement"""
        pass


class UpdateCustomerServiceTaskStrategy(TaskStrategy):
    def update_task(self, user: User, task_input: dict, context: dict) -> Task:
        task = context["task"]
        task_form_data = task.form_data or dict()
        task_form_data = task_form_data.get("customer_service", {})
        form_schema = self.validate_and_get_form_schema(
            form_schema_key=task_input["form_schema_key"]
        )
        form_data = self.get_valid_form_data(
            input_data=task_input,
            form_schema=form_schema,
            origin_form_data=task_form_data,
            multiple_forms=True,
        )
        form_data = {
            **task.form_data,
            "customer_service": {**task_form_data, **form_data},
        }
        data = {"form_data": form_data}
        self.validate_and_update_task(task=task, data=data)
        self.update_call_logs(
            user=user,
            level="customer_service",
            task=task,
            current_form_data=task.form_data.get("customer_service", {}).get(
                "beneficiaryData", {}
            ),
            old_form_data=task_form_data.get("beneficiaryData", {}),
        )
        self.audit_for_object_updating(
            content_object=task,
            user=user,
            data={
                "form_data": task.form_data,
                "status": {task.status: task.get_status_display()},
                "customer_service_id": task.customer_service.id,
                "negotiator_id": task.negotiator.id if task.negotiator else None,
            },
        )
        return task

    @staticmethod
    def validate_and_update_task(task: Task, data: dict):
        serializer = UpdateTaskSerializer(
            instance=task,
            data=data,
            partial=True,
        )
        if not serializer.is_valid():
            raise BadRequest(reason=serializer.errors)
        serializer.save()


class UpdateNegotiatorTaskStrategy(UpdateCustomerServiceTaskStrategy):
    def update_task(self, user: User, task_input: dict, context: dict) -> Task:
        task = context["task"]
        task_form_data = task.form_data or dict()
        task_form_data = task_form_data.get("negotiator", {})
        form_schema = self.validate_and_get_form_schema(
            form_schema_key=task_input["form_schema_key"]
        )
        form_data = self.get_valid_form_data(
            input_data=task_input,
            form_schema=form_schema,
            origin_form_data=task_form_data,
            multiple_forms=True,
        )  # remove drafts from form_data
        if not task_form_data.get("drafts", {}):
            task_form_data.pop("drafts", None)
        form_data = {**task.form_data, "negotiator": {**task_form_data, **form_data}}
        # remove 'placeOrder' if 'negotiatorData' exists
        form_data = self.remove_level2_on_change(
            task_form_data=form_data, task_input=task_input
        )
        data = {
            "form_data": form_data,
        }
        if task_input.get("status"):
            data["status"] = task_input["status"]
        self.validate_and_update_task(task=task, data=data)
        self.update_call_logs(
            user=user,
            level="negotiator",
            task=task,
            current_form_data=task.form_data.get("negotiator", {}).get(
                "negotiatorData", {}
            ),
            old_form_data=task_form_data.get("negotiatorData", {}),
        )
        self.audit_for_object_updating(
            content_object=task,
            user=user,
            data={
                "form_data": task.form_data,
                "status": {task.status: task.get_status_display()},
                "customer_service_id": task.customer_service.id,
                "negotiator_id": task.negotiator.id if task.negotiator else None,
            },
        )

        return task

    @staticmethod
    def remove_level2_on_change(task_form_data: dict, task_input: dict):
        if task_input.get("form_data", {}).get("negotiatorData") and task_form_data.get(
            "negotiator", {}
        ).get("placeOrder"):
            task_form_data["negotiator"].pop("placeOrder", None)
        return task_form_data


class AssignTaskToProjectManagerStrategy(TaskStrategy):
    def update_task(self, user: User, task_input: dict, context: dict) -> Task:
        task = context["task"]
        task_form_data = task.form_data or dict()
        data = {
            "form_data": task_form_data,
            "status": TaskStatus.PENDING_REVIEW,
            "assigned_status": TaskAssignedStatus.ASSIGNED_TO_PROJECT_MANAGER,
        }
        serializer = AssignTaskToProjectManagerSerializer(
            data=data, partial=True, instance=task
        )
        if not serializer.is_valid():
            raise BadRequest(reason=serializer.errors)
        serializer.save()
        self.audit_for_object_updating(
            content_object=task,
            user=user,
            data={
                "form_data": task.form_data,
                "status": {task.status: task.get_status_display()},
                "customer_service_id": task.customer_service.id,
                "negotiator_id": task.negotiator.id if task.negotiator else None,
            },
        )
        # add last call logs to task meta_data
        self.update_call_logs(
            user=user,
            level="customer_service",
            task=task,
            current_form_data=task_form_data.get("customer_service", {}).get(
                "beneficiaryData", {}
            ),
            old_form_data={},
            check_time=False,
        )
        return task


class AssignTaskToNegotiatorStrategy(TaskStrategy):
    def update_task(
        self, user: User, task_input: dict, context: Dict[str, QuerySet[Task] and User]
    ) -> QuerySet[Task]:
        tasks: QuerySet[Task] = context["tasks"]
        negotiator = context["negotiator"]
        for task in tasks:
            self.update_task_and_status_for_negotiator(task=task, negotiator=negotiator)
            self.audit_for_object_updating(
                content_object=task,
                user=user,
                data={
                    "form_data": task.form_data or {},
                    "status": {task.status: task.get_status_display()},
                    "customer_service_id": task.customer_service.id,
                    "negotiator_id": task.negotiator.id,
                },
            )
        return tasks

    @staticmethod
    def update_task_and_status_for_negotiator(task: Task, negotiator: User) -> Task:
        beneficiary_data = task.form_data.get("customer_service", {}).get(
            "beneficiaryData", {}
        )
        data = {
            "status": TaskStatus.CHECK_BENEFICIARY_DATA,
            "negotiator": negotiator.id,
            "assigned_status": TaskAssignedStatus.ASSIGNED_TO_NEGOTIATOR,
            "form_data": {
                **task.form_data,
                "negotiator": {
                    "negotiatorData": beneficiary_data,
                    "drafts": {"negotiatorData": beneficiary_data},
                },
            },
        }
        serializer = AssignTaskToNegotiatorSerializer(
            data=data, partial=True, instance=task
        )
        if not serializer.is_valid():
            raise BadRequest(reason=serializer.errors)
        return serializer.save()


class RejectTaskStrategy(TaskStrategy):
    def update_task(
        self, user: User, task_input: dict, context: Dict[str, Task and User]
    ) -> Task:
        task = context["task"]
        form_schema = context.get("form_schema")
        valid_form_data = self.get_valid_form_data(
            input_data=task_input,
            form_schema=form_schema,
            origin_form_data=task.form_data,
            multiple_forms=True,
        )
        self.update_task_to_be_rejected(task=task, valid_form_data=valid_form_data)
        self.audit_for_object_updating(
            content_object=task,
            user=user,
            data={
                "form_data": task.form_data or {},
                "status": {task.status: task.get_status_display()},
                "customer_service_id": task.customer_service.id,
                "negotiator_id": task.negotiator.id if task.negotiator else None,
            },
        )
        # add last call logs to task meta_data
        current_form_data = task.form_data.get("negotiator", {}).get(
            "negotiatorData", {}
        ) or task.form_data.get("customer_service", {}).get("beneficiaryData", {})
        self.update_call_logs(
            level="negotiator" if task.negotiator else "customer_service",
            user=user,
            task=task,
            current_form_data=current_form_data,
            old_form_data={},
            check_time=False,
        )
        return task

    @staticmethod
    def update_task_to_be_rejected(task: Task, valid_form_data: dict) -> Task:
        if valid_form_data.get("beneficiaryData"):
            task.form_data["customer_service"]["beneficiaryData"] = valid_form_data[
                "beneficiaryData"
            ]
        elif valid_form_data.get("negotiatorData"):
            task.form_data["negotiator"]["negotiatorData"] = valid_form_data[
                "negotiatorData"
            ]
        data = {
            "status": TaskStatus.REJECTED,
            "assigned_status": TaskAssignedStatus.NOT_ASSIGNED,
            "form_data": {**task.form_data},
        }
        serializer = RejectTaskSerializer(data=data, partial=True, instance=task)
        if not serializer.is_valid():
            raise BadRequest(reason=serializer.errors)
        return serializer.save()


class DeleteTaskStrategy(TaskStrategy):
    def update_task(self, user: User, task_input: dict, context: Dict[str, Task]):
        task = context["task"]
        serializer = DeleteTaskSerializer(
            instance=task, partial=True, data={"status": task.status}
        )
        if not serializer.is_valid():
            raise BadRequest(reason=serializer.errors)
        self.audit_for_object_deleting(
            object_id=task.id,
            user=user,
            data={
                "form_data": task.form_data or {},
                "status": {task.status: task.get_status_display()},
                "customer_service_id": task.customer_service.id,
                "negotiator_id": task.negotiator.id if task.negotiator else None,
                "model": str(task.__class__.__name__),
                "id": task.id,
            },
        )
        task.delete()


class ReassignToCustomerServiceTaskStrategy(TaskStrategy):
    def update_task(
        self, user: User, task_input: dict, context: Dict[str, QuerySet[Task] and User]
    ) -> QuerySet[Task]:
        tasks = context["tasks"]
        customer_service = context["customer_service"]
        for task in tasks:
            task.customer_service = customer_service
            self.audit_for_object_updating(
                user=user,
                content_object=task,
                data={
                    "customer_service_id": task.customer_service.id,
                    "negotiator_id": task.negotiator.id if task.negotiator else None,
                },
            )
        self.send_notification_for_bulk_assign(
            tasks=tasks,
            recipient=customer_service,
            user=user,
            role=UserRoleChoices.CUSTOMER_SERVICES,
        )
        Task.objects.bulk_update(objs=tasks, fields=["customer_service"])
        return tasks


class ReassignToNegotiatorTaskStrategy(TaskStrategy):
    def update_task(
        self, user: User, task_input: dict, context: Dict[str, QuerySet[Task] and User]
    ) -> QuerySet[Task]:
        tasks = context["tasks"]
        negotiator = context["negotiator"]
        for task in tasks:
            task.negotiator = negotiator
            self.audit_for_object_updating(
                user=user,
                content_object=task,
                data={
                    "customer_service_id": task.customer_service.id,
                    "negotiator_id": task.negotiator.id,
                },
            )
        self.send_notification_for_bulk_assign(
            tasks=tasks,
            recipient=negotiator,
            user=user,
            role=UserRoleChoices.NEGOTIATORS,
        )
        Task.objects.bulk_update(objs=tasks, fields=["negotiator"])
        return tasks
