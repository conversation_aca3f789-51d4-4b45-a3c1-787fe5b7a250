import logging
from abc import ABC, abstractmethod
from typing import List

from django.db import transaction
from django.db.models import Q
from gabbro.graphene.exceptions import BadRequest

from auditing.mixins import AuditMixin
from deals.models import Beneficiary
from orders.models import Order, Task, TaskStatus
from orders.serializers.order_serializer import CreateOrderSerializer
from users.models import User

logger = logging.getLogger("orders")


class OrderStrategy(ABC):
    def __init__(self):
        self.debug = lambda method, message: logger.debug(
            f"[{self.__class__}][{method}] {message}"
        )

    @abstractmethod
    def create_order(self, user: User, order_input: dict, context: dict) -> Order:
        """Method that all order strategies must implement"""
        pass


class CreateOrderStrategy(OrderStrategy, AuditMixin):
    @transaction.atomic
    def create_order(self, user: User, order_input: dict, context: dict) -> Order:
        order = self.create_order_db_object(order_input=order_input)
        self.debug("create_order", f"Order created with id: {order.id}")
        beneficiaries = order.beneficiaries.only("id")
        self.debug("create_order", f"beneficiaries count {len(beneficiaries)}")
        tasks = [
            self.prepare_task_object_with_data(
                beneficiary=beneficiary,
                order=order,
                customer_service=context["customer_service"],
            )
            for beneficiary in beneficiaries
        ]
        tasks = Task.objects.bulk_create(tasks)
        self.debug("create_order", f"Created tasks for beneficiaries: {tasks}")
        self.update_task_form_data_with_basic_transaction_data(tasks=tasks)
        self.audit_for_object_creation(content_object=order, user=user)
        return order

    @staticmethod
    def create_order_db_object(order_input: dict) -> Order:
        data = {
            "beneficiaries": order_input.get("beneficiaries"),
            "start_date": order_input.get("start_date"),
            "end_date": order_input.get("end_date"),
        }
        serializer = CreateOrderSerializer(data=data)
        if not serializer.is_valid():
            raise BadRequest(reason=serializer.errors)
        return serializer.save()

    @staticmethod
    def prepare_task_object_with_data(
        beneficiary: Beneficiary, order: Order, customer_service: User
    ) -> Task:
        new_task = Task(
            beneficiary=beneficiary,
            order=order,
            customer_service=customer_service,
            form_data={
                "customer_service": {
                    "drafts": {
                        "beneficiaryData": beneficiary.data,
                    },
                    "beneficiaryData": beneficiary.data,
                }
            },
        )
        if latest_beneficiary_task := (
            beneficiary.tasks.filter(
                Q(favorite_offers__deal__isnull=False) | Q(status=TaskStatus.REJECTED)
            )
            .distinct()
            .order_by("id")
            .last()
        ):
            new_task.form_data = latest_beneficiary_task.form_data
        return new_task

    @staticmethod
    def update_task_form_data_with_basic_transaction_data(tasks: List[Task]):
        for task in tasks:
            if task.form_data.get("customer_service", {}).get("beneficiaryData", {}):
                task.form_data["customer_service"]["beneficiaryData"][
                    "basicTransactionData"
                ] = {
                    "assignmentDate": task.created.strftime("%Y-%m-%d"),
                    "assignmentTime": task.created.strftime("%H:%M:%S"),
                    "assignmentNumber": str(task.id),
                    "customerServiceEmployeeName": f"{task.customer_service.first_name} {task.customer_service.last_name}",
                }
                if (
                    not task.form_data["customer_service"]
                    .get("drafts", {})
                    .get("beneficiaryData")
                ):
                    task.form_data["customer_service"]["drafts"] = {
                        "beneficiaryData": task.form_data["customer_service"][
                            "beneficiaryData"
                        ]
                    }
        Task.objects.bulk_update(objs=tasks, fields=["form_data"])
