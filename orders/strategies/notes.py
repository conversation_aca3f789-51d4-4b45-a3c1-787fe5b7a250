import logging
from abc import ABC, abstractmethod
from typing import Dict

from django.contrib.auth import get_user_model
from gabbro.graphene.exceptions import BadRequest

from auditing.mixins import AuditMixin
from orders.models import Note, Task
from orders.serializers.note import CreateNoteSerializer

logger = logging.getLogger("orders")

User = get_user_model()


class NoteStrategy(ABC):
    @abstractmethod
    def create_note(self, user: User, note_data: dict, context: dict) -> Note:
        """Method that all notes strategies must implement"""
        pass


class CreateNoteStrategy(NoteStrategy, AuditMixin):
    def create_note(
        self, user: User, note_data: dict, context: Dict[str, Task]
    ) -> Note:
        serializer = CreateNoteSerializer(
            data={
                "note": note_data["note"],
                "task": context["task"].id,
                "created_by": user.id,
            }
        )
        if not serializer.is_valid():
            raise BadRequest(reason=serializer.errors)
        return serializer.save()
