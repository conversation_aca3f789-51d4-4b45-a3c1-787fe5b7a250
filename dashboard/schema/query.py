import graphene
from django.db.models import Count, Case, When, IntegerField

from orders.models import Task, TaskAssignedStatus, TaskStatus
from utils.graphene.decorators import authentication_required
from .object_types import CustomerServiceListTaskType, NegotiatorListTaskType


class Query(graphene.ObjectType):
    customer_service_tasks_count = graphene.Field(
        CustomerServiceListTaskType,
    )
    negotiators_tasks_count = graphene.Field(
        NegotiatorListTaskType,
    )

    @staticmethod
    @authentication_required
    def resolve_customer_service_tasks_count(
        root,
        info,
    ):
        user = info.context.user

        queryset = Task.objects.filter(
            customer_service=user,
            assigned_status=TaskAssignedStatus.ASSIGNED_TO_CUSTOMER_SERVICE,
        ).only("status", "meta_data")

        aggregated_data = queryset.aggregate(
            in_progress_count=Count("id", output_field=IntegerField()),
            uncounted_beneficiaries_count=Count(
                Case(
                    When(meta_data__call_logs__customer_service__isnull=True, then=1),
                    default=None,
                    output_field=IntegerField(),
                )
            ),
            rescheduled_tasks_count=Count(
                Case(
                    When(
                        form_data__customer_service__beneficiaryData__contact__willingnessToContinueProgram="إعادة جدولة الإتصال",
                        meta_data__call_logs__customer_service__isnull=False,
                        then=1,
                    ),
                    default=None,
                    output_field=IntegerField(),
                )
            ),
        )
        in_progress_count = aggregated_data.get("in_progress_count", 0)
        uncounted_beneficiaries_count = aggregated_data.get(
            "uncounted_beneficiaries_count", 0
        )
        rescheduled_tasks_count = aggregated_data.get("rescheduled_tasks_count", 0)

        return CustomerServiceListTaskType(
            in_progress_tasks=in_progress_count,
            uncounted_beneficiaries=uncounted_beneficiaries_count,
            rescheduled_tasks=rescheduled_tasks_count,
        )

    @staticmethod
    @authentication_required
    def resolve_negotiators_tasks_count(
        root,
        info,
    ):
        user = info.context.user

        queryset = (
            Task.objects.filter(
                negotiator=user,
                assigned_status=TaskAssignedStatus.ASSIGNED_TO_NEGOTIATOR,
            )
            .select_related("negotiator")
            .only("status", "negotiator")
        )

        aggregated_data = queryset.aggregate(
            beneficiaries_verifications=Count(
                Case(
                    When(status=TaskStatus.CHECK_BENEFICIARY_DATA, then=1),
                    default=None,
                    output_field=IntegerField(),
                )
            ),
            in_progress_tasks=Count(
                "id",
                output_field=IntegerField(),
            ),
            level_2_preferences=Count(
                Case(
                    When(status=TaskStatus.SECOND_LEVEL_PREFERENCES, then=1),
                    default=None,
                    output_field=IntegerField(),
                )
            ),
            identify_properties=Count(
                Case(
                    When(status=TaskStatus.IDENTIFY_SUITABLE_PROPERTY, then=1),
                    default=None,
                    output_field=IntegerField(),
                )
            ),
            property_search=Count(
                Case(
                    When(
                        status__in=[
                            TaskStatus.SEND_PROPERTY_TO_BENEFICIARY,
                            TaskStatus.LOOKING_FOR_PROPERTY,
                            TaskStatus.PROPERTY_INSPECTION,
                            TaskStatus.NEGOTIATE_WITH_OWNER,
                            TaskStatus.IDENTIFY_SUITABLE_PROPERTY,
                        ],
                        then=1,
                    ),
                    default=None,
                    output_field=IntegerField(),
                )
            ),
            property_found=Count(
                Case(
                    When(status=TaskStatus.LOOKING_FOR_PROPERTY, then=1),
                    default=None,
                    output_field=IntegerField(),
                )
            ),
            property_send=Count(
                Case(
                    When(status=TaskStatus.SEND_PROPERTY_TO_BENEFICIARY, then=1),
                    default=None,
                    output_field=IntegerField(),
                )
            ),
            property_viewed=Count(
                Case(
                    When(status=TaskStatus.PROPERTY_INSPECTION, then=1),
                    default=None,
                    output_field=IntegerField(),
                )
            ),
            negotiate_with_owner=Count(
                Case(
                    When(status=TaskStatus.NEGOTIATE_WITH_OWNER, then=1),
                    default=None,
                    output_field=IntegerField(),
                )
            ),
            create_deal=Count(
                Case(
                    When(status=TaskStatus.CREATE_DEAL, then=1),
                    default=None,
                    output_field=IntegerField(),
                )
            ),
            deal_started=Count(
                Case(
                    When(
                        status__in=[
                            TaskStatus.SHARING_DATA_WITH_CLIENT,
                            TaskStatus.OFFER_SENT_TO_BENEFICIARY,
                            TaskStatus.BENEFICIARY_ACCEPT_THE_OFFER,
                            TaskStatus.OFFER_RECEIVED,
                            TaskStatus.OFFER_AGREEMENT,
                            TaskStatus.CREATE_DEAL,
                        ],
                        then=1,
                    ),
                    default=None,
                    output_field=IntegerField(),
                )
            ),
            data_shared=Count(
                Case(
                    When(status=TaskStatus.SHARING_DATA_WITH_CLIENT, then=1),
                    default=None,
                    output_field=IntegerField(),
                )
            ),
            initial_offer_sent=Count(
                Case(
                    When(status=TaskStatus.OFFER_SENT_TO_BENEFICIARY, then=1),
                    default=None,
                    output_field=IntegerField(),
                )
            ),
            offer_accepted=Count(
                Case(
                    When(status=TaskStatus.BENEFICIARY_ACCEPT_THE_OFFER, then=1),
                    default=None,
                    output_field=IntegerField(),
                )
            ),
            final_offer_received=Count(
                Case(
                    When(status=TaskStatus.OFFER_RECEIVED, then=1),
                    default=None,
                    output_field=IntegerField(),
                )
            ),
            contract_signed=Count(
                Case(
                    When(status=TaskStatus.OFFER_AGREEMENT, then=1),
                    default=None,
                    output_field=IntegerField(),
                )
            ),
            deal_finished=Count(
                Case(
                    When(status=TaskStatus.FINISHED, then=1),
                    default=None,
                    output_field=IntegerField(),
                )
            ),
            review_recommendation=Count(
                Case(
                    When(status=TaskStatus.REVIEW_RECOMMENDATIONS, then=1),
                    default=None,
                    output_field=IntegerField(),
                )
            ),
        )

        return NegotiatorListTaskType(
            in_progress_tasks=aggregated_data.get("in_progress_tasks", 0),
            beneficiaries_verifications=aggregated_data.get(
                "beneficiaries_verifications", 0
            ),
            level_2_preferences=aggregated_data.get("level_2_preferences", 0),
            property_search=aggregated_data.get("property_search", 0),
            property_found=aggregated_data.get("property_found", 0),
            property_send=aggregated_data.get("property_send", 0),
            property_viewed=aggregated_data.get("property_viewed", 0),
            negotiate_with_owner=aggregated_data.get("negotiate_with_owner", 0),
            deal_started=aggregated_data.get("deal_started", 0),
            data_shared=aggregated_data.get("data_shared", 0),
            initial_offer_sent=aggregated_data.get("initial_offer_sent", 0),
            offer_accepted=aggregated_data.get("offer_accepted", 0),
            final_offer_received=aggregated_data.get("final_offer_received", 0),
            contract_signed=aggregated_data.get("contract_signed", 0),
            deal_finished=aggregated_data.get("deal_finished", 0),
            identify_properties=aggregated_data.get("identify_properties", 0),
            create_deal=aggregated_data.get("create_deal", 0),
            review_recommendation=aggregated_data.get("review_recommendation", 0),
        )
