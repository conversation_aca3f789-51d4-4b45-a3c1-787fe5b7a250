import graphene


class NegotiatorListTaskType(graphene.ObjectType):
    in_progress_tasks = graphene.Int()
    beneficiaries_verifications = graphene.Int()
    level_2_preferences = graphene.Int()
    property_search = graphene.Int()
    property_found = graphene.Int()
    property_send = graphene.Int()
    property_viewed = graphene.Int()
    negotiate_with_owner = graphene.Int()
    deal_started = graphene.Int()
    data_shared = graphene.Int()
    initial_offer_sent = graphene.Int()
    offer_accepted = graphene.Int()
    final_offer_received = graphene.Int()
    contract_signed = graphene.Int()
    deal_finished = graphene.Int()
    identify_properties = graphene.Int()
    create_deal = graphene.Int()
    review_recommendation = graphene.Int()


class CustomerServiceListTaskType(graphene.ObjectType):
    in_progress_tasks = graphene.Int()
    uncounted_beneficiaries = graphene.Int()
    rescheduled_tasks = graphene.Int()


__all__ = ["CustomerServiceListTaskType", "NegotiatorListTaskType"]
