from abc import ABC, abstractmethod

from users.models import User


class InputValidation(ABC):
    @abstractmethod
    def get_object_if_exists(self, user: User, order_input: dict) -> dict:
        """Method that all order validators must implement"""
        pass


class QueryValidation(ABC):
    @abstractmethod
    def get_object_if_exists(self, slug):
        """Method that all order validators must implement"""
        pass
