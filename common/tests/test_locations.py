import json
import os
from unittest.mock import patch, MagicMock

from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.management import call_command
from django.test import TestCase, override_settings

from deals.management.commands.load_ren_locations import LAYERS_KEYS
from deals.models import Record
from utils.locations import Locations, calculate_boundaries_postgis

User = get_user_model()


class LocationsTestCase(TestCase):
    @classmethod
    @override_settings(
        MEDIA_ROOT=os.path.join(settings.BASE_DIR, "deals", "tests", "fixtures")
    )
    def setUpTestData(cls):
        super().setUpTestData()
        files_path = os.path.join(
            settings.BASE_DIR, "deals", "tests", "fixtures", "sample_geopackage_data"
        )
        files = os.listdir(files_path)
        for file in files:
            # create records per layer
            file_path = os.path.join(files_path, file)
            layer_name = file.split(".")[0]
            call_command(
                "load_ren_locations",
                layer_key=LAYERS_KEYS[layer_name],
                file_path=file_path,
            )

    def setUp(self):
        super().setUp()

        self.city = (
            Record.objects.filter(layer__key=LAYERS_KEYS["city"])
            .values_list("source_properties__id", flat=True)
            .last()
        )
        self.zone = (
            Record.objects.filter(layer__key=LAYERS_KEYS["zone"])
            .values_list("source_properties__id", flat=True)
            .last()
        )
        self.district = Record.objects.filter(
            layer__key=LAYERS_KEYS["districts"], source_properties__city_id=self.city
        ).values_list("source_properties__id", flat=True)
        self.prefer_price = 100
        self.property_type = "Apartment"
        self.level = "city"
        self.input_data = {
            "input": {
                "prefer_price": self.prefer_price,
                "property_type": self.property_type,
                "retrun_details": False if self.level == "city" else True,
                "cities_ids_list": [self.city],
                "zones_ids_list": [self.zone],
                "district_ids_list": [self.district],
            },
            "skus": ["RECOMMEND_DISTRICTS_BY_PREFERENCES"],
        }
        self.cls = Locations()

    @patch("utils.locations.Locations._make_request")
    def test_preferences_success_response(self, mock_preferences):
        self.input_data["input"].pop("district_ids_list")
        mock_response = MagicMock()
        mock_response.json.return_value = {"key": "value"}
        mock_preferences.return_value = (mock_response, None)

        response, error = self.cls.preferences(data=self.input_data)
        self.assertEqual(error, None)
        self.assertEqual(response, {"key": "value"})

    @patch("utils.locations.Locations._make_request")
    def test_preferences_failed_response(self, mock_preferences):
        self.input_data["input"].pop("district_ids_list")
        MagicMock()
        mock_preferences.return_value = None, None
        response, error = self.cls.preferences(data=self.input_data)
        self.assertEqual(response, None)

    def test_get_location_coordinates(self):
        qs = Record.objects.filter(layer__key=LAYERS_KEYS["districts"])
        qs_values = qs.values("source_properties__id", "geometry")
        district = qs_values.last()
        _id, _geometry = district["source_properties__id"], district["geometry"]
        makan_data = {
            "recommend_districts_by_preferences": [
                {
                    "id": _id,
                    "main_zone": "شمال",
                    "sutiability": "good_match",
                }
            ]
        }

        response = self.cls.get_location_coordinates(makan_data)
        self.assertIsInstance(response, dict)
        self.assertIn("suitabilities", response)
        self.assertEqual(response["suitabilities"], ["good_match"])
        self.assertIn("boundaries", response)
        boundaries_geojson = calculate_boundaries_postgis(
            records=Record.objects.filter(source_properties__id=_id), field="geometry"
        ).geojson
        self.assertDictEqual(response["boundaries"], json.loads(boundaries_geojson))

        self.assertIn("recommend_districts_by_preferences", response)
        recommend_district = response["recommend_districts_by_preferences"][0]
        self.assertDictEqual(
            recommend_district["geometry"], json.loads(_geometry.geojson)
        )
        # test response without geometry
        makan_data = {
            "recommend_districts_by_preferences": [
                {
                    "id": -1,
                    "main_zone": "شمال",
                }
            ]
        }
        response = self.cls.get_location_coordinates(makan_data)
        self.assertIn("boundaries", response)
        self.assertIsNone(response["boundaries"])
        self.assertIn("suitabilities", response)
        recommend_district = response["recommend_districts_by_preferences"][0]
        self.assertIsNone(recommend_district["geometry"])
