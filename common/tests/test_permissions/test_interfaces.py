from django.test import TestCase

from common.permissions import PermissionsInterface
from users.models import User
from utils.tests.factories import UserFactory


class ConcretePermissions(PermissionsInterface):
    def check_permissions(self, user: User, context: dict) -> None:
        if not user.is_superuser:
            raise PermissionError("User is not authenticated.")


class PermissionsInterfaceTestCase(TestCase):
    def setUp(self):
        self.user = UserFactory()
        self.permissions = ConcretePermissions()
        PermissionsInterface.check_permissions(
            self.permissions, self.user, context=dict()
        )

    def test_check_permissions_authenticated_user(self):
        self.user.is_superuser = True
        try:
            self.permissions.check_permissions(self.user, context=dict())
        except PermissionError:
            self.fail("check_permissions raised PermissionError unexpectedly!")

    def test_check_permissions_unauthenticated_user(self):
        self.user.is_superuser = False
        with self.assertRaises(PermissionError):
            self.permissions.check_permissions(self.user, context=dict())
