from django.test import TestCase

from common.validators import InputValidation, QueryValidation
from users.models import User
from utils.tests.factories import UserFactory


class ConcreteInputValidation(InputValidation):
    def get_object_if_exists(self, user: User, order_input: dict) -> dict:
        if user.is_superuser and "order_id" in order_input:
            return {"status": "exists"}
        return {"status": "not found"}


class ConcreteQueryValidation(QueryValidation):
    def get_object_if_exists(self, slug):
        if slug == "valid_slug":
            return {"status": "exists"}
        return {"status": "not found"}


class InputValidationTestCase(TestCase):
    def setUp(self):
        self.user = UserFactory()
        self.input_validation = ConcreteInputValidation()
        InputValidation.get_object_if_exists(self.input_validation, self.user, dict())

    def test_get_object_if_exists_authenticated_user_with_order(self):
        self.user.is_superuser = True
        order_input = {"order_id": 123}

        result = self.input_validation.get_object_if_exists(self.user, order_input)
        self.assertEqual(result, {"status": "exists"})

    def test_get_object_if_exists_unauthenticated_user(self):
        self.user.is_superuser = False
        order_input = {"order_id": 123}

        result = self.input_validation.get_object_if_exists(self.user, order_input)
        self.assertEqual(result, {"status": "not found"})

    def test_get_object_if_exists_authenticated_user_without_order(self):
        self.user.is_superuser = True
        order_input = {}

        result = self.input_validation.get_object_if_exists(self.user, order_input)
        self.assertEqual(result, {"status": "not found"})


class QueryValidationTestCase(TestCase):
    def setUp(self):
        self.query_validation = ConcreteQueryValidation()
        QueryValidation.get_object_if_exists(self.query_validation, "valid_slug")

    def test_get_object_if_exists_valid_slug(self):
        slug = "valid_slug"

        result = self.query_validation.get_object_if_exists(slug)
        self.assertEqual(result, {"status": "exists"})

    def test_get_object_if_exists_invalid_slug(self):
        slug = "invalid_slug"

        result = self.query_validation.get_object_if_exists(slug)
        self.assertEqual(result, {"status": "not found"})
