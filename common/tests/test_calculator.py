from django.contrib.auth import get_user_model
from django.test import TestCase

from utils.ren_calculator import RealEstateFinanceCalculator

User = get_user_model()


class RenCalculatorTestCase(TestCase):
    def setUp(self):
        self.calculator = RealEstateFinanceCalculator(
            salary=50000.00,
            monthly_deduction=500.00,
            remaining_duration_months=36,
            fund_duration=10,
        )

    def test_validations(self):
        self.assertTrue(self.calculator.validations())
        self.calculator.salary = None
        self.assertFalse(self.calculator.validations())

    def test_calculation(self):
        result = self.calculator.calculation()
        self.assertIsNotNone(result)
        expected_keys = [
            "amount_guaranteed",
            "monthly_inst_during_pf",
            "monthly_inst_post_pf",
            "profit_margin_finance_period",
            "total_profit_amount",
            "fund_duration",
        ]
        self.assertCountEqual(result.keys(), expected_keys)
        self.assertEqual(result["amount_guaranteed"], 2431111)
        self.assertEqual(result["monthly_inst_during_pf"], 27000)
        self.assertEqual(result["monthly_inst_post_pf"], 27500)
        self.assertEqual(result["profit_margin_finance_period"], 850889)
        self.assertEqual(result["total_profit_amount"], 3282000)
        self.assertEqual(result["fund_duration"], 10)
