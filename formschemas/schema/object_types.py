import graphene
from graphene_django import DjangoObjectType
from graphene_gis.scalars import J<PERSON><PERSON>calar

from formschemas.models.form_schema import (
    FormSchema,
)


class FormSchemaType(DjangoObjectType):
    json_schema = graphene.Field(JSONScalar)

    class Meta:
        model = FormSchema
        fields = ["id", "key", "created", "modified", "json_schema"]


class FormSchemaListType(graphene.ObjectType):
    data = graphene.List(FormSchemaType)
    count = graphene.Int()


__all__ = [
    "FormSchemaType",
    "FormSchemaListType",
]
