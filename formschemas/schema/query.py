import graphene

from formschemas.models.form_schema import FormSchema
from formschemas.schema.object_types import FormSchemaListType
from utils.graphene.decorators import (
    authentication_required,
)
from utils.graphene.query import (
    PageInfo,
    DjangoFilterInput,
    filter_qs_paginate_with_count,
    build_q,
)


class Query(graphene.ObjectType):
    schemas = graphene.Field(
        FormSchemaListType,
        pk=graphene.Int(),
        page_info=PageInfo(),
        filters=graphene.List(DjangoFilterInput),
    )

    @staticmethod
    @authentication_required
    def resolve_schemas(root, info, pk: int = None, page_info=None, filters=None):
        """
        retrieve all buyers
        """
        qs = FormSchema.objects.all()
        return FormSchemaListType(
            *filter_qs_paginate_with_count(qs, build_q(pk, filters), page_info)
        )
