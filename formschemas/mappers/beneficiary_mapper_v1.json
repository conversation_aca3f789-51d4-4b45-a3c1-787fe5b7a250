{"ID_CODE": {"column_name": "ID_CODE", "type": "str", "default": null}, "رقم التقنيات": {"column_name": "رقم التقنيات", "type": "int", "default": null}, "الاسم": {"column_name": "name", "path": "personalData", "type": "str", "default": null}, "العمر (هجري)": {"column_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "path": "personalData", "type": "int", "default": 0}, "الوظيفة": {"column_name": "job", "path": "professionalData", "type": "str", "default": null, "enum": true}, "رقم الجوال": {"column_name": "mobileNumber", "path": "personalData", "type": "str", "default": "0", "is_mobile": true}, "المنطقة": {"column_name": "region", "path": "location", "type": "str", "default": null, "enum": true}, "المدينة": {"column_name": "city", "path": "location", "type": "str", "default": null, "enum": true}, "عدد افراد الاسرة": {"column_name": "familyMembersCount", "path": "familyData", "type": "int", "default": 0}, "المنتج": {"column_name": "product", "path": "supportPackages", "type": "str", "default": null}, "لديه ارض": {"column_name": "hasLand", "path": "personalRealEstateData", "type": "str", "default": null, "enum": true}, "لديه رخصة بناء": {"column_name": "hasBuildingLicense", "path": "personalRealEstateData", "type": "str", "default": null, "enum": true}, "بدا بالبناء": {"column_name": "startedBuilding", "path": "personalRealEstateData", "type": "str", "default": null, "enum": true}, "الراتب": {"column_name": "salary", "path": "financialData", "type": "int", "default": 0}, "بدل السكن": {"column_name": "housingAllowance", "path": "financialData", "type": "int", "default": 0}, "الاستقطاع الشهري": {"column_name": "monthlyDeduction", "path": "financialData", "type": "int", "default": 0}, "المدة المتبقية (شهر)": {"column_name": "remainingDurationMonths", "path": "financialData", "type": "int", "default": 0}, "مداخيل اضافية": {"column_name": "additionalIncome", "path": "financialData", "type": "int", "default": 0}, "تاريخ التسجيل": {"column_name": "registrationDate", "is_date": true, "path": "request", "type": "str", "default": "0"}, "بنك الراتب": {"column_name": "salaryBank", "path": "financialData", "type": "str", "default": null, "enum": true}, "AIP1": {"column_name": "AIP1", "type": "int", "default": 0, "path": "financialData"}, "AIP2": {"column_name": "AIP2", "type": "int", "default": 0, "path": "financialData"}, "AIP3": {"column_name": "AIP3", "type": "int", "default": 0, "path": "financialData"}, "AIP4": {"column_name": "AIP4", "type": "int", "default": 0, "path": "financialData"}, "AIP5": {"column_name": "AIP5", "type": "int", "default": 0, "path": "financialData"}, "الممول 1": {"column_name": "funder1", "path": "financialData", "type": "str", "default": null, "enum": true}, "2 الممول": {"column_name": "funder2", "type": "str", "default": null, "enum": true, "path": "financialData"}, "3 الممول": {"column_name": "funder3", "type": "str", "default": null, "enum": true, "path": "financialData"}, "الممول 4": {"column_name": "funder4", "type": "str", "default": null, "enum": true, "path": "financialData"}, "الممول 5": {"column_name": "funder5", "type": "str", "default": null, "enum": true, "path": "financialData"}, "الاستحقاق": {"column_name": "entitlement", "path": "financialData", "type": "str", "default": null, "enum": true}, "متوسط قيمة التمويل": {"column_name": "fundingAmount", "path": "financialData", "type": "int", "default": 0}, "جهة الطلب": {"column_name": "destination", "type": "str", "default": null, "is_destination": true}}