from django.db import transaction
from django.db.utils import IntegrityError
from django.test import TestCase
from gabbro.utils import generate_random_string

from formschemas.models.form_schema import FormSchema


class FormSchemaBaseTestCase(TestCase):
    def setUp(self):
        pass


class FormSchemaTestCase(FormSchemaBaseTestCase):
    def setUp(self):
        super(FormSchemaTestCase, self).setUp()

    def test_model_creation(self):
        """
        Test creation of a FormSchema Model
        """
        schemas = []
        for i in range(1, 6):
            beneficiary = FormSchema(
                key=generate_random_string(length=20),
            )
            schemas.append(beneficiary)
        FormSchema.objects.bulk_create(schemas)
        self.assertEqual(FormSchema.objects.count(), 5)

    def test_form_schema_str(self):
        """
        Test string representation of a FormSchema
        """
        form_schema = FormSchema.objects.create(
            key=generate_random_string(length=20),
        )
        self.assertEqual(str(form_schema), form_schema.key)

    def test_form_schema_with_duplicate_key(self):
        """
        Create Bulk Form Schema with duplicate key
        """
        key = generate_random_string(length=20)
        schemas = []
        for i in range(1, 6):
            beneficiary = FormSchema(
                key=key,
            )
            schemas.append(beneficiary)
        with self.assertRaises(IntegrityError):
            with transaction.atomic():
                FormSchema.objects.bulk_create(schemas)
        self.assertEqual(FormSchema.objects.count(), 0)
