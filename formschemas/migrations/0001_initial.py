# Generated by Django 3.2.25 on 2024-06-20 15:01

from django.db import migrations, models
import django_extensions.db.fields
import jsoneditor.fields.django3_jsonfield


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="FormSchema",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                ("title", models.CharField(max_length=200, verbose_name="Title")),
                (
                    "schema_type",
                    models.PositiveSmallIntegerField(
                        choices=[
                            (0, "Beneficiary"),
                            (1, "Customer Services"),
                            (2, "Negotiators"),
                        ],
                        default=0,
                        verbose_name="Schema type",
                    ),
                ),
                (
                    "json_schema",
                    jsoneditor.fields.django3_jsonfield.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Form and UI Schemas",
                        verbose_name="JSON Schema",
                    ),
                ),
            ],
            options={
                "verbose_name": "Form Schema",
                "verbose_name_plural": "Form Schemas",
            },
        ),
    ]
