# Generated by Django 3.2.25 on 2024-06-26 13:15

from django.db import migrations, models
import formschemas.models.form_schema
import jsoneditor.fields.django3_jsonfield


class Migration(migrations.Migration):

    dependencies = [
        ("formschemas", "0001_initial"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="formschema",
            name="schema_type",
        ),
        migrations.AlterField(
            model_name="formschema",
            name="json_schema",
            field=jsoneditor.fields.django3_jsonfield.JSONField(
                blank=True,
                default=formschemas.models.form_schema.default_form_schema,
                help_text="Form and UI Schemas",
                verbose_name="JSON Schema",
            ),
        ),
        migrations.AlterField(
            model_name="formschema",
            name="title",
            field=models.SlugField(unique=True, verbose_name="Unique Form Key"),
        ),
        migrations.RenameField(
            model_name="formschema",
            old_name="title",
            new_name="key",
        ),
    ]
