from django.utils.translation import ugettext_lazy as _
from gabbro.graphene import NotFound, BadRequest

from formschemas.models import FormSchema
from utils.general_utils import form_schema_validate


class FormSchemaMixin:
    @classmethod
    def get_valid_form_data(
        cls,
        form_schema: FormSchema,
        input_data: dict,
        origin_form_data: dict,
        multiple_forms: bool = False,
    ):
        key = None
        is_draft = input_data.get("is_draft")
        drafts = origin_form_data.get("drafts", {})
        input_form_data = input_data["form_data"]
        if multiple_forms:
            cls.validate_form_data_with_multiple_forms(input_data)
            key, input_form_data = cls.get_input_form_data_for_multiple_forms(
                input_data
            )
        if is_draft:
            origin_form_data.pop("drafts", None)
            input_form_data = {
                "drafts": {**drafts, **input_form_data},
                **origin_form_data,
            }
            return input_form_data
        form_schema = form_schema.json_schema.get("form")
        form_schema_validate(
            form_schema=form_schema,
            form_data=input_form_data[key]
            if multiple_forms is True
            else input_form_data,
        )
        drafts.pop(key, None)
        return input_form_data

    @staticmethod
    def validate_and_get_form_schema(form_schema_key: str) -> FormSchema:
        form_schema = FormSchema.objects.filter(key=form_schema_key).first()
        if not form_schema:
            raise NotFound(
                reason={"form_schema_key": _("invalid Form Schema key") % {}}
            )
        return form_schema

    @staticmethod
    def validate_form_data_with_multiple_forms(input_data: dict):
        if not (0 < len(input_data.get("form_data", {})) < 2):
            raise BadRequest(reason={"form_data": _("only 1 form allowed") % {}})

    @staticmethod
    def get_input_form_data_for_multiple_forms(input_data: dict):
        key, form_data = next(iter(input_data.get("form_data", {}).items()))
        return key, {key: form_data}

    @staticmethod
    def get_form_schema_if_exists(key: str):
        form_schema = FormSchema.objects.filter(key=key).first()
        if not form_schema:
            raise NotFound(
                reason={"form_schema_key": _("invalid Form Schema key") % {}}
            )
        return form_schema
