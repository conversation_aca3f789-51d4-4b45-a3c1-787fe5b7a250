from django.db import models
from django.utils.translation import gettext_lazy as _
from django_extensions.db.models import TimeStampedModel
from jsoneditor.fields.django3_jsonfield import <PERSON><PERSON><PERSON><PERSON>


def default_form_schema():
    return {"form": dict(), "UISchema": dict()}


class FormSchema(TimeStampedModel):
    key = models.SlugField(unique=True, verbose_name=_("Unique Form Key"))
    json_schema = JSONField(
        blank=True,
        default=default_form_schema,
        verbose_name=_("JSON Schema"),
        help_text=_("Form and UI Schemas"),
    )

    class Meta:
        verbose_name = _("Form Schema")
        verbose_name_plural = _("Form Schemas")

    def __str__(self):
        return self.key
