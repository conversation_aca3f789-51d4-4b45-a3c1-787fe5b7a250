from decimal import Decimal, ROUND_HALF_UP


class RealEstateFinanceCalculator:
    """
    monthly_deduction: Monthly deduction(الاستقطاع الشهري)
    deduction_pct_income: Percentage of Deduction from the Client’s Income (نسبة الاستقطاع من دخل العميل)
    discount_pct_product: The Discount Percentage for the Product (نسبة الاستقطاع للمنتج )
    deduction_pct_personal_finance: Deduction Percentage for Personal Finance (نسبة الاستقطاع لتمويل الشخصي )
    deduction_pct_post_finance: The Deduction Percentage After the End of Personal Financing (نسبة الاستقطاع بعد انتهاء التمويل الشخصي)
    deduction_pct_remaining_period: The deduction percentage during the remaining personal financing period (نسبة الاستقطاع خلال فترة التمويل الشخصي المتبقيه .)
    total_instalments_personal_finance: Total installments during the personal financing period (اجمالي الاقساط خلال فترة التمويل الشخصي )
    installment_remaining_pf: Installment during the remaining personal financing period (القسط خلال فترة التمويل  الشخصي المتبقيه)
    installment_post_pf: Installment after the end of personal financing.(القسط بعد انتهاء التمويل الشخصي)
    total_inst_remaining_post_pf: The total installments during the remaining financing period after personal financing (اجمالي الاقساط خلال فترة التمويل المتبقيه  بعد التمويل الشخصي )
    monthly_inst_during_pf: The monthly installment for the financing period during the personal installment (القسط الشهري لفترة التمويل  اثناء القسط الشخصي)
    monthly_inst_post_pf: The monthly installment for the financing period after the end of the personal installment (القسط الشهري لفترة التمويل  بعد انتهاء القسط الشخصي)
    profit_margin_finance_period: Profit margin for the financing period (هامش الربحيه لمدة التمويل)
    total_profit_amount: The total amount of profits (اجمالي المبلغ بالارباح)
    """

    def __init__(
        self,
        salary: float,
        monthly_deduction: float,
        remaining_duration_months: int,
        fund_duration: int,
    ):
        self.YEARLY_PERCENTAGE_MAPPER = {"10": 0.035, "15": 0.0365, "25": 0.0396}
        self.salary: float = salary
        self.monthly_deduction: float = monthly_deduction
        self.remaining_duration_months: int = remaining_duration_months
        self.fund_duration: int = fund_duration
        self.yearly_percentage: float = self.YEARLY_PERCENTAGE_MAPPER.get(
            str(self.fund_duration), 10
        )
        self.deduction_pct_income: float = 0.55
        self.discount_pct_product: float = 0.55

    def validations(self):
        required_fields = [
            self.salary,
            self.monthly_deduction,
            self.remaining_duration_months,
            self.fund_duration,
        ]
        if any(field is None for field in required_fields):
            return False
        if str(self.fund_duration) not in self.YEARLY_PERCENTAGE_MAPPER.keys():
            return False
        return True

    def calculate_deduction_pct_income(self):
        """
        Calculate the Percentage of Deduction from the Client’s Income
        """
        return self.salary * self.deduction_pct_income

    def calculate_discount_pct_product(self):
        """
        Calculate The Discount Percentage for the Product
        """
        return self.salary * self.discount_pct_product

    def calculate_fund_duration(self):
        """
        Calculate Fund Duration (year)
        """
        return self.fund_duration * 12

    @staticmethod
    def round_result(number: float):
        # convert result to decimal
        number = Decimal(number)
        return number.quantize(Decimal("1"), rounding=ROUND_HALF_UP).normalize()

    @staticmethod
    def is_close_enough(input_value: float, expected_value: float, margin=2.0):
        # Calculate the absolute difference
        difference = abs(input_value - expected_value)
        # Check if the difference is within the acceptable margin
        if difference > margin:
            return False
        else:
            return True

    def calculation(self) -> dict:
        """
        implement calculation for RealEstate Finance Calculator
        """
        deduction_pct_personal_finance = self.monthly_deduction / self.salary
        deduction_pct_remaining_period = (
            self.deduction_pct_income - deduction_pct_personal_finance
        )
        installment_post_pf = self.calculate_discount_pct_product()
        total_inst_remaining_post_pf = installment_post_pf * (
            self.calculate_fund_duration() - self.remaining_duration_months
        )
        monthly_inst_during_pf = deduction_pct_remaining_period * self.salary
        installment_remaining_pf = min(
            monthly_inst_during_pf, self.calculate_discount_pct_product()
        )
        total_instalments_personal_finance = (
            installment_remaining_pf * self.remaining_duration_months
        )

        total_profit_amount = (
            total_inst_remaining_post_pf + total_instalments_personal_finance
        )
        amount_guaranteed = total_profit_amount / (
            1 + (self.yearly_percentage * self.fund_duration)
        )
        monthly_inst_post_pf = installment_post_pf
        profit_margin_finance_period = total_profit_amount - amount_guaranteed
        return {
            "amount_guaranteed": self.round_result(amount_guaranteed),
            "monthly_inst_during_pf": self.round_result(monthly_inst_during_pf)
            if all([self.monthly_deduction > 0, self.remaining_duration_months > 0])
            else 0,
            "monthly_inst_post_pf": self.round_result(monthly_inst_post_pf),
            "profit_margin_finance_period": self.round_result(
                profit_margin_finance_period
            ),
            "total_profit_amount": self.round_result(total_profit_amount),
            "fund_duration": self.fund_duration,
        }
