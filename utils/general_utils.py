import datetime

import jsonschema
from django.conf import settings
from django.core.exceptions import ImproperlyConfigured
from django.core.exceptions import ValidationError
from django.core.files.storage import default_storage
from django.utils import timezone
from google.cloud import storage
from jsonschema.exceptions import (
    ValidationError as JsonSchemaValidationError,
)
from notifications.models import Notification

STORAGE_TYPE = getattr(settings, "STORAGE_TYPE", None)
GS_CREDENTIALS = getattr(settings, "GS_CREDENTIALS", None)
GS_MEDIA_BUCKET_NAME = getattr(settings, "GS_MEDIA_BUCKET_NAME", None)
ORIGIN = getattr(settings, "PLATFORM_WEB_URL", "").split(",") or ["*"]
if all([STORAGE_TYPE == "GS", GS_MEDIA_BUCKET_NAME is None, GS_CREDENTIALS is None]):
    raise ImproperlyConfigured("GCS signed urls wrong configurations")


def form_schema_validate(form_schema: dict, form_data: dict):
    try:
        jsonschema.validate(instance=form_data, schema=form_schema)
    except JsonSchemaValidationError as js_error:
        raise ValidationError(
            message={"form_data": f"JsonSchema Validation Error: {js_error.message}"}
        )


def get_nested_value(data: dict, path: str):
    """
    Retrieves a value from a nested dictionary given a path.
    """
    try:
        keys = path.split(".")
        for key in keys:
            data = data[key]
        return data
    except (KeyError, TypeError):
        return None


def open_storage_file(file_path: str):
    try:
        return default_storage.open(f"{file_path}")
    except FileNotFoundError:
        return None


def remove_null_values(data):
    """Recursively remove keys with null values from a nested dictionary."""
    if isinstance(data, dict):
        for key, value in list(data.items()):
            if value is None:
                del data[key]
            elif isinstance(value, (dict, list)):
                remove_null_values(value)
            if isinstance(value, list) and not any(value):
                del data[key]
    elif isinstance(data, list):
        for item in data:
            remove_null_values(item)
            if isinstance(item, dict) and not any(item.values()):
                data.remove(item)
    return data


def generate_upload_signed_url_v4(
    blob_name,
    bucket_name=GS_MEDIA_BUCKET_NAME,
    service_account_file=None,
    expiration: int = 15,
):
    """Generates a v4 signed URL for uploading a blob using HTTP PUT."""
    storage_client = storage.Client(credentials=GS_CREDENTIALS)
    bucket = storage_client.bucket(bucket_name)
    blob = bucket.blob(blob_name)
    return blob.generate_signed_url(
        version="v4",
        expiration=datetime.timedelta(minutes=expiration),
        method="PUT",
        content_type="application/octet-stream",
    )


def create_notification(notifications: [dict]) -> [Notification]:
    applied_notifications = []
    for notification in notifications:
        applied_notifications.append(
            Notification(
                target=notification.get("target"),
                level=notification.get("level", "info"),
                recipient=notification["recipient"],
                actor=notification["actor"],
                data=notification.get("data", {}),
                verb=notification["title"],
                description=notification["description"],
                timestamp=timezone.now(),
            )
        )
    notifications = Notification.objects.bulk_create(applied_notifications)
    return notifications


__all__ = [
    "form_schema_validate",
    "open_storage_file",
    "remove_null_values",
    "generate_upload_signed_url_v4",
    "get_nested_value",
]
