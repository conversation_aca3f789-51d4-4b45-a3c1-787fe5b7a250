import datetime
import json
import typing
from enum import Enum

import graphene
from django.core.exceptions import FieldError
from django.db.models import Q
from django.utils.translation import gettext_lazy as _
from gabbro.graphene.exceptions import NotFound


def evaluate_boolean_string(s):
    if s.lower() == "false" or s == "0":
        return False
    return bool(s)


_conversion_methods = {
    # string conversions
    "exact": lambda x: x,
    "iexact": lambda x: x,
    "contains": lambda x: x,
    "icontains": lambda x: x,
    # numbers conversions
    "gt": float,
    "lt": float,
    "gte": float,
    "lte": float,
    "range": lambda x: json.loads(x.replace("'", '"')),
    # date time conversions
    "date__gt": lambda x: datetime.date.fromisoformat(x).isoformat(),
    "date__lt": lambda x: datetime.date.fromisoformat(x).isoformat(),
    "date__gte": lambda x: datetime.date.fromisoformat(x).isoformat(),
    "date__lte": lambda x: datetime.date.fromisoformat(x).isoformat(),
    "time__gt": lambda x: datetime.time.fromisoformat(x).isoformat(),
    "time__lt": lambda x: datetime.time.fromisoformat(x).isoformat(),
    "time__gte": lambda x: datetime.time.fromisoformat(x).isoformat(),
    "time__lte": lambda x: datetime.time.fromisoformat(x).isoformat(),
    # ids_ in clause conversions
    "in": lambda x: json.loads(x.replace("'", '"')),
    # is null
    "isnull": evaluate_boolean_string,
    "isempty": lambda x: None,
}


class BoundedInt(graphene.Int):
    """A custom Graphene Int that has bounded values between a min and max."""

    @staticmethod
    def parse_literal(node):
        # Convert the AST node to a Python int
        try:
            value = int(node.value)
        except ValueError:
            raise ValueError(_("Value provided is not an integer"))
        # Here you would include your custom validation logic, for example:
        return BoundedInt.validate(value)

    @staticmethod
    def parse_value(value):
        return BoundedInt.validate(value)

    @staticmethod
    def validate(value, min_value=0):
        """Validate the integer is within the min and max bounds."""
        if value is None:
            return None

        if min_value > value:
            raise ValueError(
                _("Value must be bigger than %(min_value)s" % {"min_value": min_value})
            )
        return value


class PageInfo(graphene.InputObjectType):
    limit = BoundedInt()
    offset = BoundedInt()
    order_by = graphene.String()


class DjangoFilterClauses(Enum):
    # text clauses
    exact = "exact"
    iexact = "iexact"
    contains = "contains"
    icontains = "icontains"

    # values in clauses
    values_in = "in"

    # numbers
    gt = "gt"
    lt = "lt"
    gte = "gte"
    lte = "lte"
    range = "range"
    # dates
    date__gt = "date__gt"
    date__lt = "date__lt"
    date__gte = "date__gte"
    date__lte = "date__lte"

    # times
    time__gt = "time__gt"
    time__lt = "time__lt"
    time__gte = "time__gte"
    time__lte = "time__lte"

    # is null
    isnull = "isnull"

    # is empty
    isempty = "isempty"


DjangoFilterChoices = graphene.Enum.from_enum(DjangoFilterClauses)


class DjangoFilterInput(graphene.InputObjectType):
    field = graphene.String(required=True)
    value = graphene.String(required=True)
    clause = graphene.Field(DjangoFilterChoices)
    is_not = graphene.Boolean()


def filter_qs_paginate_with_count(qs, q: Q, page_info: typing.Dict = None):
    page_info = page_info or dict()
    limit = page_info.get("limit", 100_000)
    offset = page_info.get("offset", 0)
    order_by = "__".join(page_info.get("order_by", "").strip().split("."))
    if order_by:
        # validate if field exists in qs fields
        try:
            return (
                qs.filter(q).order_by(order_by)[offset : limit + offset],
                qs.filter(q).count(),
            )
        except FieldError:
            raise NotFound(reason={"order_by": f"invalid '{order_by}' field name"})
    return qs.filter(q)[offset : limit + offset], qs.filter(q).count()


def build_q(pk: int = None, filters: typing.List = None):
    # check if there is no filters list
    if filters is None:
        filters = list()

    # clone filters to avoid changing reference of filters in memory
    _filters = [*filters]

    # inject pk filter to return list of one object
    if pk is not None:
        _filters.append({"field": "pk", "value": pk})

    q_objects = []
    for f in _filters:
        field = f.get("field")
        clause = f.get("clause", "exact")
        value = _conversion_methods.get(clause)(f.get("value"))
        q_dict = (
            {f"{field}": value}
            if clause == "isempty"
            else {f"{field}__{clause}": value}
        )

        # if is_not flag
        is_not = f.get("is_not", False)
        if is_not:
            q_objects.append(~Q(**q_dict))
        else:
            q_objects.append(Q(**q_dict))
    return Q(*q_objects)


def reshape_beneficiary_filters(field_path, filters=None, page_info=None):
    from deals.models.beneficiary import BENEFICIARY_DATA_MAPPER

    beneficiary_mapper = BENEFICIARY_DATA_MAPPER
    filters = filters or list()
    page_info = page_info or dict()
    for field in filters:
        if not field["field"].startswith("beneficiary"):
            continue
        field_name = "__".join(field["field"].split(".")[1:])
        if field_name.startswith("data__"):
            extracted_name = "".join(field_name.split("__")[1:])
            field_name = "__".join(
                beneficiary_mapper.get(extracted_name, "").split(".")
            )
        path = f"{field_path}__{field_name}"
        field["field"] = path

    order_by = page_info.get("order_by", "")
    if order_by.startswith(("beneficiary.data", "-beneficiary.data")):
        field_name = beneficiary_mapper.get("_".join(order_by.split(".")[2:]))
        page_info["order_by"] = f"{field_path}__{field_name}"
        if order_by.startswith("-"):
            page_info["order_by"] = "-" + page_info["order_by"]
    return filters, page_info


def reshape_locations_filter(fields: dict, filters=None):
    filters = filters or list()
    items = (
        (key, value) for key, value in fields.items() if value["value"] is not None
    )
    for key, value in items:
        filters.append(
            {
                "field": f"source_properties__{key}",
                "value": f"{value['value']}",
                "clause": f"{value['clause']}" if value.get("clause") else "iexact",
            }
        )
    return filters


__all__ = [
    "DjangoFilterInput",
    "PageInfo",
    "filter_qs_paginate_with_count",
    "build_q",
    "reshape_beneficiary_filters",
    "reshape_locations_filter",
]
