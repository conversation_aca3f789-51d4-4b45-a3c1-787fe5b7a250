import json

from django.contrib.gis.db.models import Extent
from django.contrib.gis.gdal import Envelope
from django.contrib.gis.geos import GEOSGeometry, GeometryCollection
from django.contrib.gis.geos.prototypes.io import WKBWriter
from django.utils.translation import ugettext_lazy as _
from rest_framework import serializers


def create_geometry_collection(geojson_str):
    if geojson_str:
        geom = GEOSGeometry(geojson_str)
        if geom.geom_type.lower() != "geometrycollection":
            geom = GeometryCollection([geom])
        return geom


def get_valid_geometry(geometry):
    geos_geometry = create_geometry_collection(json.dumps(geometry))
    if geos_geometry.hasz:
        wkb_writer = WKBWriter()
        geos_geometry = create_geometry_collection(wkb_writer.write(geos_geometry))
    geos_geometry = geos_geometry if geos_geometry.valid else geos_geometry.buffer(0)
    return geos_geometry


def calculate_boundaries_postgis(records, field: str, geojson_data=None):
    if geojson_data:
        # If geojson_data is provided, handle it
        min_x, min_y, max_x, max_y = None, None, None, None

        for geo in geojson_data:
            # Extract the geometries
            for geometry in geo.get("geometries", []):
                coords = geometry.get("coordinates")
                if coords and len(coords) == 2:  # It's a Point
                    lon, lat = coords
                    if min_x is None or lon < min_x:
                        min_x = lon
                    if max_x is None or lon > max_x:
                        max_x = lon
                    if min_y is None or lat < min_y:
                        min_y = lat
                    if max_y is None or lat > max_y:
                        max_y = lat

        if (
            min_x is not None
            and min_y is not None
            and max_x is not None
            and max_y is not None
        ):
            # Create a bounding box from the min/max values found
            return GEOSGeometry(
                f"POLYGON(({min_x} {min_y}, {min_x} {max_y}, {max_x} {max_y}, {max_x} {min_y}, {min_x} {min_y}))"
            )
    records_extent = (
        records.aggregate(extent=Extent(field))["extent"] if records else None
    )
    if records_extent is not None:
        return GEOSGeometry(Envelope(records_extent).wkt)


def validate_geometry(value):
    if value:
        """
        Validate and clean the 'geometry' field.
        """
        try:
            geometry = convert_geometry(value)
        except Exception:
            raise serializers.ValidationError(
                {"geometry": _("Invalid geometry format") % {}}
            )

        if not geometry.valid:
            raise serializers.ValidationError(
                {"geometry": _("Invalid geometry: Not a valid geometry object.") % {}}
            )

        return geometry


def convert_geometry(geom_string: str):
    # convert a geometry collection if required
    # expected types WKT, HEX, WKB, and GeoJSON
    geom = GEOSGeometry(geom_string)
    if geom.geom_type.lower() != "geometrycollection":
        geom = GeometryCollection([geom])
    return geom
