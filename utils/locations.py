import json
import logging
from urllib.parse import urljoin

from django.conf import settings
from gabbro.graphene import BadRequest
from gabbro.makan_client import MakanClient

from deals.management.commands.load_ren_locations import LAYERS_KEYS
from deals.models import Record
from utils.geometry import calculate_boundaries_postgis

offers_settings = getattr(settings, "OFFERS", {})

logger = logging.getLogger("utils")


class Locations(MakanClient):
    def preferences(self, data: dict):
        url = urljoin(self.api_url, "recommend_districts_by_preferences/")
        logger.debug(f"[Locations][preferences] url: {url}")
        logger.debug(f"[Locations][preferences] data: {data}")
        response, error = self._make_request("POST", url, data=json.dumps(data))
        if response is not None:
            logger.debug(f"[Locations][preferences] response data: {response.json()}")
            return response.json(), None
        logger.debug(f"[Locations][preferences] response error: {error}")
        return None, error

    def get_location_coordinates(self, makan_data: dict):
        districts_data = makan_data.get("recommend_districts_by_preferences", [])
        ids = [record.get("id") for record in districts_data if record.get("id")]
        suitabilities = set()
        records = Record.objects.filter(
            source_properties__id__in=ids, layer__key=LAYERS_KEYS["districts"]
        )
        records_lookup = {
            record.source_properties.get("id"): record for record in records
        }
        for district_data in districts_data:
            district_id = district_data.get("id")
            record = records_lookup.get(district_id)
            self.update_record_geometry(district_data=district_data, record=record)
            if district_data.get("sutiability"):
                suitabilities.add(district_data["sutiability"])

        # implement suitabilities
        makan_data["suitabilities"] = list(suitabilities)
        # implement boundaries
        boundaries = calculate_boundaries_postgis(records=records, field="geometry")
        makan_data["boundaries"] = (
            json.loads(boundaries.geojson) if boundaries else None
        )
        return makan_data

    @staticmethod
    def update_record_geometry(district_data: dict, record: Record):
        """Helper function to update the record with geometry data."""
        if record:
            geometry = record.geometry.geojson
            district_data["geometry"] = json.loads(geometry)
        else:
            district_data["geometry"] = None

    def get_offer_location_data(self, latitude: float, longitude: float):
        url = urljoin(self.api_url, "reverse_geocoding/")
        data = {
            "input": {
                "geometry": {"coordinates": [latitude, longitude], "type": "point"},
                "info_list": [
                    "all",
                    "amana",
                    "city",
                    "district",
                    "municipality",
                    "plan",
                    "region",
                    "road",
                    "sub_zone",
                    "zone",
                    "parcel",
                    "national_address",
                ],
            },
            "skus": ["REVERSE_GEOCODING"],
        }
        res, err = self._make_request("POST", url, data=json.dumps(data))
        if res is not None:
            return res.json(), None
        return None, err

    def get_makan_offers(self, data: dict):
        data = {
            "input": {
                "prefer_price": data.get("prefer_price"),
                "property_type": data.get("property_type"),
                "return_details": False,
                **locations.get_location_for_integration_request_input(
                    level="district",
                    city=data.get("preferred_locations", {}).get("cityId"),
                    districts=data.get("preferred_locations", {}).get("district"),
                    zones=data.get("preferred_locations", {}).get("mainDivision"),
                ),
            },
            "skus": ["RECOMMEND_PROPERTIES_BY_PREFERENCES"],
        }
        url = urljoin(self.api_url, "recommend_properties_by_preferences/")
        response, errors = self._make_request("POST", url, data=json.dumps(data))
        if errors:
            logger.debug(f"get_makan_offers: {errors}")
            raise BadRequest(reason={"errors": errors})
        data = response.json() if response else {}
        makan_offers = data.get("recommend_properties_by_preferences", [])
        return makan_offers if isinstance(makan_offers, list) else []

    @staticmethod
    def get_location_for_integration_request_input(
        level: str,
        city: int,
        zones: list,
        districts: list,
    ) -> dict:
        # Collect preferred zone IDs
        preferred_zones = (
            [zone["id"] for zone in zones if isinstance(zone, dict) and zone.get("id")]
            if zones
            else []
        )

        # Collect preferred district IDs
        preferred_districts = (
            [
                district["id"]
                for district in (districts or [])
                if isinstance(district, dict) and district.get("id")
            ]
            if districts
            else []
        )

        # Map city, zones, and districts to the corresponding level
        mapper = {
            "city": {
                "cities_ids_list": [city] if city else [],
                "zones_ids_list": preferred_zones,
            },
            "district": {
                "districts_ids_list": preferred_districts,
                "cities_ids_list": [city] if city else [],
                "zones_ids_list": preferred_zones,
            },
        }

        return mapper.get(level, {})


locations = Locations()
