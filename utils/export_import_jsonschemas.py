import json

from formschemas.models import FormSchema


def import_jsonschemas():
    with open("all_schemas.json", "r") as json_file:
        new_schemas = json.load(json_file)

    # updating existing form schemas
    existing_schemas = FormSchema.objects.all()
    updated = []
    for s in existing_schemas:
        new_form_schema = new_schemas.get(s.key, dict()).get("json_schema")
        if new_form_schema is None:
            print(f"could not find {s.key} in the new schemas file")
            continue
        s.json_schema = new_form_schema
        updated.append(s)
        new_schemas.pop(s.key)
    FormSchema.objects.bulk_update(updated, ("json_schema",))

    # create the new objects
    new_objects = []
    for key in new_schemas.keys():
        print(f"found and creating {key}")
        new_objects.append(
            FormSchema(
                key=new_schemas[key]["key"], json_schema=new_schemas[key]["json_schema"]
            )
        )
    FormSchema.objects.bulk_create(new_objects)


def export_jsonschemas():
    schemas = {s["key"]: s for s in FormSchema.objects.values("key", "json_schema")}
    print(f"schemas = {schemas}")
    with open("all_schemas.json", "w") as json_file:
        json.dump(schemas, json_file, indent=2)
