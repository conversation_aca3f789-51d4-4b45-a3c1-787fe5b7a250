import json
import logging

import requests
from django.conf import settings
from gabbro.graphene import BadR<PERSON>quest
from gabbro.makan_client import SUCCESS_STATUS_CODES

logger = logging.getLogger("utils")

offers_settings = getattr(settings, "OFFERS", {})


class OfferClient(object):
    def __init__(self, ssl_verify=True):
        self.verify = ssl_verify
        self.connection_error = None
        self.api_url = offers_settings.get("INTERNAL_BASE_URL", "")
        self.api_key = offers_settings.get("API_KEY")
        self._prep_session()

    def _prep_session(self):
        self.session = requests.session()
        self.session.headers.update(
            {
                "Authorization": f"API_KEY {self.api_key}",
                "content-type": "application/json;charset=UTF-8",
            }
        )

    def _close_session(self):
        self.session.close()
        self.session = None

    def _reset_session(self):
        self._close_session()
        self._prep_session()

    def _make_request(self, method: str, *args, **kwargs):
        req_method = {"POST": self.session.post, "GET": self.session.get}[method]
        res = None

        if self.connection_error is not None:
            self.connection_error = None
            self._reset_session()

        try:
            res, _ = req_method(*args, **kwargs), None
        except requests.exceptions.SSLError:
            if self.verify:
                self.verify = False
                return self._make_request(req_method, *args, **kwargs)
        except requests.exceptions.RequestException as err:
            self.connection_error = (
                f"{err.__class__.__name__}: Error connecting to Offers!"
            )
            return None, self.connection_error

        if res.status_code in SUCCESS_STATUS_CODES:
            return res, None
        return None, res.text

    def fetch_offer_details(
        self, makan_offers_ids=None, offer_ids=None, page_info=None, filters=None
    ):
        makan_offers_ids = makan_offers_ids or []
        page_info = page_info or {}
        filters = filters or []
        filters.append(
            {"field": "pk", "value": offer_ids, "clause": "values_in"}
        ) if offer_ids or isinstance(offer_ids, list) else None
        if page_info and "order_by" in page_info.keys():
            order_by = page_info.pop("order_by")
            page_info.append({"orderBy": order_by})
        query = """
        query MyQuery($filters: [DjangoFilterInput]!, $pageInfo: PageInfo!, $ids: [Int]!) {
            renRecords(pageInfo: $pageInfo, filters: $filters, ids: $ids) {
                boundaries
                count
                 data {
                  id
                  data
                  geometry
                  recommended
                }
            }
        }
        """
        variables = {"filters": filters, "pageInfo": page_info, "ids": makan_offers_ids}
        data = {"query": query, "variables": variables}
        res, err = self._make_request("POST", self.api_url, data=json.dumps(data))
        res = res.json() if res else None
        if err or res and res.get("errors"):
            logger.debug(f"fetch_offer_details: {err or res.get('errors')}")
            raise BadRequest(reason={"errors": err or res.get("errors")})
        data = res.get("data", {}).get("renRecords", {})
        return data

    def create_vd_offer(self, data: dict):
        form_data = json.dumps(data["data"])
        geometry = data["geometry"]
        mutation = """
        mutation MyMutation($formData: JSONString!, $geometry: String!) {
          renCreateRecord(
            recordInput: {formData: $formData, geometry: $geometry}
          ) {
            record {
              data
              geometry
              id
            }
          }
        }
        """
        variables = {
            "formData": form_data,
            "geometry": geometry,
        }
        data = {"query": mutation, "variables": variables}
        res, err = self._make_request("POST", self.api_url, data=json.dumps(data))
        res = res.json() if res else None
        if err or res and res.get("errors"):
            logger.debug(f"create_vd_offer: {err or res.get('errors')}")
            raise BadRequest(reason={"errors": err or res.get("errors")})
        data = res.get("data", {}).get("renCreateRecord", {}).get("record", {})
        return data

    def update_vd_offer(self, data: dict):
        form_data = json.dumps(data["data"])
        geometry = data["geometry"]
        map_data = json.dumps(data.get("map_data", {}))
        record_id = data.get("id")
        mutation = """
        mutation MyMutation($formData: JSONString, $geometry: String, $recordId: Int, $mapData: JSONString) {
          renUpdateRecord(
            recordInput: {formData: $formData, geometry: $geometry, recordId: $recordId, mapData: $mapData}
          ) {
            record {
              data
              geometry
              id
            }
          }
        }
        """
        variables = {
            "formData": form_data,
            "geometry": geometry,
            "recordId": record_id,
            "mapData": map_data,
        }
        data = {"query": mutation, "variables": variables}
        res, err = self._make_request("POST", self.api_url, data=json.dumps(data))
        res = res.json() if res else None
        if err or res and res.get("errors"):
            logger.debug(f"update_vd_offer: {err or res.get('errors')}")
            raise BadRequest(reason={"errors": err or res.get("errors")})
        data = res.get("data", {}).get("renUpdateRecord", {}).get("record", {})
        return data


offers_client = OfferClient()
