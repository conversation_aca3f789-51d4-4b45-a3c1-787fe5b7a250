name: <PERSON><PERSON> Backend CI

on: [push]

jobs:
  build:

    runs-on: ubuntu-latest

    services:
      postgres:
        image: kartoza/postgis
        env:
          POSTGRES_DB: ren_backend
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: passw0rd
        ports:
          - 5432:5432

    steps:
    - name: Checkout code
      uses: actions/checkout@v2

    - name: Test PostgreSQL Connection
      run: |
        sudo apt-get update && sudo apt-get install -y postgresql-client
        PGPASSWORD="passw0rd" psql -h localhost -U postgres -d ren_backend -c "\dt"

    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: '3.10.7'

    - name: Install system dependencies
      run: |
        sudo apt-get -q update
        sudo apt-get install -y -q gdal-bin libgdal-dev gettext apt-utils apt-transport-https ca-certificates gnupg --no-install-recommends
        sudo apt-get clean && sudo rm -rf /var/lib/apt/lists/*
        echo "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] http://packages.cloud.google.com/apt cloud-sdk main" | sudo tee -a /etc/apt/sources.list.d/google-cloud-sdk.list
        curl https://packages.cloud.google.com/apt/doc/apt-key.gpg | sudo apt-key --keyring /usr/share/keyrings/cloud.google.gpg  add -
        sudo apt-get update -y
        sudo apt-get install google-cloud-cli -y

    - name: Install Python dependencies
      run: |
        pip install --no-cache-dir --upgrade pip
        pip install --no-cache-dir keyrings.google-artifactregistry-auth
        mkdir -p ~/.pip
        echo "[global]" >> ~/.pip/pip.conf
        echo "extra-index-url = https://us-central1-python.pkg.dev/geotech-infrastructure/pypi-all/simple/" >> ~/.pip/pip.conf
        gcloud auth login --cred-file=gcloud-service-account-key.json --update-adc
        gcloud config set project geotech-infrastructure
        gcloud config set artifacts/repository pypi-all
        gcloud config set artifacts/location us-central1
        pip install --no-cache-dir -r requirements_dev.txt

    - name: Prepare environment
      run: |
        sed '/^DATABASE_HOST/d' .env.example >> .env
        echo DATABASE_HOST=localhost >> .env

    - name: Run Django migrations and checks
      run: |
        python manage.py migrate
        python manage.py check
        python manage.py makemigrations --check --dry-run common users deals orders resource_management formschemas auditing

    - name: Run Django Test
      run: |
        coverage erase
        coverage run manage.py test
        coverage report

    - name: Run linters
      run: |
        pip install --no-cache-dir click==8.0.4 # until they fix this issue https://github.com/psf/black/issues/2964
        black .
        flake8
