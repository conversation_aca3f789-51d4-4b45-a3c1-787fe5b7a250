from typing import Dict

from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from gabbro.graphene import NotFound, BadRequest

from common.validators import InputValidation
from deals.mixins import RequestMixin
from deals.models import Deal, <PERSON><PERSON>tatus, FavoriteOffer, Request, RequestStatus
from deals.strategies import FetchmakanOffersStrategy
from formschemas.mixins import FormSchemaMixin
from orders.mixins import TaskMixin
from orders.models import Task, TaskStatus

User = get_user_model()


class CreateDealValidation(InputValidation, TaskMixin):
    def get_object_if_exists(self, user: User, deal_input: dict) -> Dict[str, Task]:
        favorite_offer: FavoriteOffer = self.get_favorite_offer_if_exists(
            user=user, favorite_offer_id=deal_input.get("favorite_offer_id")
        )
        task = favorite_offer.task
        self.validate_task(task=task)
        self.validate_offer(favorite_offer=favorite_offer, user=user)
        return {"task": task, "favorite_offer": favorite_offer}

    def validate_task(self, task: Task):
        if task.status != TaskStatus.NEGOTIATE_WITH_OWNER:
            raise BadRequest(
                reason={
                    "task": _("please submit all task %(task_id)s forms first")
                    % {"task_id": task.id}
                }
            )
        self.validate_task_status_is_in_progress(task=task)
        self.validate_task_has_no_deal(task=task)
        self.validate_request(task=task)

        # validate task form_data
        self.validate_keys_exists(
            form_data=task.form_data, task_id=task.id, level="negotiator"
        )

    @staticmethod
    def validate_request(task: Task):
        request = task.requests.order_by("id").last()
        if not request:
            raise BadRequest(
                reason={
                    "task": _(
                        "task %(task_id)s doesn't have any request, can't create request"
                    )
                    % {"task_id": task.id}
                }
            )
        if request.status in [RequestStatus.PENDING, RequestStatus.SENT]:
            raise BadRequest(
                reason={
                    "task": _("task %(task_id)s already has request, can't create deal")
                    % {"task_id": task.id}
                }
            )

    @staticmethod
    def validate_task_has_no_deal(task: Task):
        deal = task.favorite_offers.filter(deal__isnull=False).first()
        if deal:
            raise BadRequest(
                reason={
                    "deal": _("Deal for task %(task_id)s already exists")
                    % {"task_id": task.id}
                }
            )

    @staticmethod
    def get_favorite_offer_if_exists(favorite_offer_id: int, user: User):
        favorite_offer = FavoriteOffer.objects.filter(
            id=favorite_offer_id,
            task__isnull=False,
        ).first()
        if not favorite_offer:
            raise NotFound(
                reason={
                    "favorite_offer_id": _(
                        "Favorite Offer %(favorite_offer_id)s not found"
                    )
                    % {"favorite_offer_id": favorite_offer_id}
                }
            )
        if not favorite_offer.reserved:
            raise BadRequest(
                reason={
                    "favorite_offer": _("offer is not reserved by beneficiary") % {}
                }
            )
        if (
            Deal.objects.filter(favorite_offer__offer_id=favorite_offer.offer_id)
            .exclude(status=DealStatus.DEAL_CANCELLED)
            .first()
        ):
            raise BadRequest(
                reason={
                    "favorite_offer": _(
                        "Favorite offer %(favorite_offer_id)s already have deal"
                    )
                    % {"favorite_offer_id": favorite_offer_id}
                }
            )

        return favorite_offer

    @staticmethod
    def validate_offer(favorite_offer: FavoriteOffer, user: User):
        offer: dict = FetchmakanOffersStrategy().fetch_offers(
            user=user,
            context={"task": favorite_offer.task},
            offer_data={"ids": [favorite_offer.offer_id]},
            categorise_offers=False,
        )
        offer = offer.get("data", [])[0] or {}
        if (
            offer.get("id") != favorite_offer.offer_id
            or offer.get("add_to_favorite") is False
        ):
            raise NotFound(
                reason={"offer": _("offer matching query doesn't exist") % {}}
            )


class UpdateDealValidation(InputValidation, FormSchemaMixin, TaskMixin):
    def get_object_if_exists(self, user: User, deal_input: dict) -> Dict[str, Deal]:
        deal = self.get_deal_if_exists(deal_id=deal_input.get("deal_id"))
        self.validate_deal_is_in_progress(deal=deal)
        favorite_offer = deal.favorite_offer
        task = deal.favorite_offer.task
        self.validate_task_status_is_in_progress(task)
        form_schema_key = deal_input.get("form_schema_key")
        form_schema = (
            self.validate_and_get_form_schema(form_schema_key=form_schema_key)
            if form_schema_key is not None
            else None
        )
        return {
            "deal": deal,
            "form_schema": form_schema,
            "favorite_offer": favorite_offer,
            "task": task,
        }

    @staticmethod
    def validate_deal_is_in_progress(deal):
        if deal.status in [DealStatus.DEAL_FINISHED, DealStatus.DEAL_CANCELLED]:
            error_messages = {
                DealStatus.DEAL_FINISHED.value: _("can't Finish Deal yet") % {},
                DealStatus.DEAL_CANCELLED.value: _("can't cancel deal yet") % {},
            }
            raise BadRequest(reason={"status": error_messages[deal.status]})

    @staticmethod
    def get_deal_if_exists(deal_id: int):
        deal: Deal = (
            Deal.objects.filter(id=deal_id, favorite_offer__isnull=False)
            .select_related("favorite_offer")
            .first()
        )
        if not deal:
            raise NotFound(
                reason={"deal": _("Deal %(deal_id)s not found") % {"deal_id": deal_id}}
            )
        return deal


class ChoseDealFavoriteOfferValidation(InputValidation, TaskMixin, RequestMixin):
    def get_object_if_exists(
        self, user: User, deal_input: dict
    ) -> Dict[str, FavoriteOffer]:
        favorite_offer = self.get_favorite_offer_if_exists(
            favorite_offer_id=deal_input.get("favorite_offer_id"),
            task_id=deal_input.get("task_id"),
            user=user,
        )
        task = favorite_offer.task
        request: Request = self.get_beneficiary_request(task=task)
        self.validate_task_status_is_in_progress(task)
        deal = self.get_deal_if_exists(task=task)
        return {
            "favorite_offer": favorite_offer,
            "task": task,
            "request": request,
            "deal": deal,
        }

    @staticmethod
    def get_deal_if_exists(task: Task) -> Deal:
        task_favorite_offers = task.favorite_offers.filter(deal__isnull=False).first()
        deal = task_favorite_offers.deal if task_favorite_offers else None
        return deal

    @staticmethod
    def get_favorite_offer_if_exists(favorite_offer_id: int, task_id: int, user: User):
        favorite_offer: FavoriteOffer = FavoriteOffer.objects.filter(
            id=favorite_offer_id,
            task_id=task_id,
            task__beneficiary__user=user,
            deal__isnull=True,
        ).first()
        if not favorite_offer:
            raise BadRequest(
                reason={
                    "favorite_offer": _(
                        "favorite offer %(favorite_offer_id)s not found for this task"
                    )
                    % {"favorite_offer_id": favorite_offer_id}
                }
            )

        if (
            Deal.objects.filter(favorite_offer__offer_id=favorite_offer.offer_id)
            .exclude(status=DealStatus.DEAL_CANCELLED)
            .first()
        ):
            raise BadRequest(
                reason={
                    "favorite_offer": _(
                        "favorite offer %(favorite_offer_id)s already have deal"
                    )
                    % {"favorite_offer_id": favorite_offer_id}
                }
            )
        return favorite_offer
