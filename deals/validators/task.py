from django.utils.translation import gettext_lazy as _
from gabbro.graphene import NotFound

from common.validators import QueryValidation
from orders.models import Task


class TaskValidator(QueryValidation):
    def get_object_if_exists(self, slug) -> Task:
        task = Task.objects.filter(id=slug).first()
        if not task:
            raise NotFound(
                reason={"task": _("Task %(task)s does not exist") % {"task": slug}}
            )
        return task
