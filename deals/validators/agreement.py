from typing import Dict

from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from gabbro.graphene import NotFound, BadRequest

from common.validators import InputValidation
from deals.models import Agreement, DealStatus
from formschemas.mixins import FormSchemaMixin
from orders.mixins import TaskMixin
from orders.models import Task

User = get_user_model()


class CreateAgreementValidation(InputValidation, TaskMixin, FormSchemaMixin):
    def get_object_if_exists(
        self, user: User, agreement_input: dict
    ) -> Dict[str, Task]:
        task = self.get_task_if_exists(
            filters={
                "id": agreement_input["task_id"],
                "negotiator__isnull": False,
                "favorite_offers__isnull": True,
                "favorite_offers__deal__isnull": True,
                "form_data__negotiator__negotiatorData__benificiaryServiceType__serviceType__exact": "تمويل عقاري",
            }
        )
        self.validate_task(task=task, agreement_input=agreement_input)
        return {"task": task}

    def validate_task(self, task: Task, agreement_input: dict):
        self.validate_task_status_is_in_progress(task=task)
        if getattr(task, "agreement", None) is not None:
            raise BadRequest(
                reason={
                    "task": _("The agreement is already being processed.") % {},
                }
            )
        self.validate_keys_exists(
            form_data=task.form_data,
            task_id=task.id,
            level="negotiator",
            required_keys=["negotiatorData"],
        )


class UpdateAgreementValidation(InputValidation, TaskMixin, FormSchemaMixin):
    def get_object_if_exists(
        self, user: User, agreement_input: dict
    ) -> Dict[str, Task]:
        agreement = self.get_agreement_if_exists(agreement_input=agreement_input)
        self.validate_task(task=agreement.task, agreement_input=agreement_input)
        form_schema_key = agreement_input.get("form_schema")
        form_schema = (
            self.validate_and_get_form_schema(form_schema_key=form_schema_key)
            if form_schema_key
            else None
        )
        return {
            "task": agreement.task,
            "agreement": agreement,
            "form_schema": form_schema,
        }

    @staticmethod
    def get_agreement_if_exists(agreement_input: dict):
        agreement = (
            Agreement.objects.filter(id=agreement_input["id"])
            .exclude(
                status__in=[DealStatus.DEAL_FINISHED, DealStatus.DEAL_CANCELLED],
            )
            .first()
        )
        if not agreement:
            raise NotFound(reason={"deal": _("Deal not found") % {}})
        return agreement

    def validate_task(self, task: Task, agreement_input: dict):
        self.validate_task_status_is_in_progress(task=task)
        # validate task form_data
        self.validate_keys_exists(
            form_data=task.form_data,
            task_id=task.id,
            level="negotiator",
            required_keys=["negotiatorData"],
        )
