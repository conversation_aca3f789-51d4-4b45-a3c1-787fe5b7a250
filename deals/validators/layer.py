from django.utils.translation import gettext_lazy as _
from gabbro.graphene import NotFound

from common.validators import QueryValidation
from deals.models import Layer


class LayerValidator(QueryValidation):
    def get_object_if_exists(self, slug) -> Layer:
        layer = Layer.objects.filter(key=slug).first()
        if not layer:
            raise NotFound(
                reason={
                    "layer": _("Layer matching query does not exist") % {},
                }
            )
        return layer
