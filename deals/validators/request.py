from typing import Dict

from django.contrib.auth import get_user_model
from django.contrib.contenttypes.models import ContentType
from django.utils.translation import gettext_lazy as _
from gabbro.graphene import BadRequest

from common.validators import InputValidation
from deals.models import RequestStatus, OfferRequestStatus
from orders.mixins import TaskMixin
from orders.models import Task

User = get_user_model()


class CreateRequestValidation(InputValidation, TaskMixin):
    def get_object_if_exists(self, user: User, request_input: dict) -> Dict[str, Task]:
        task: Task = self.get_task_if_exists(
            filters={"id": request_input["task_id"], "beneficiary__user__isnull": False}
        )
        self.validate_task_status_is_in_progress(task=task)
        request = self.validate_request(task=task)
        return {"task": task, "request": request}

    @staticmethod
    def validate_request(task: Task):
        request = task.requests.order_by("id").last()
        if not request:
            raise BadRequest(
                reason={
                    "task": _(
                        "task %(task_id)s doesn't have any request, can't create request"
                    )
                    % {"task_id": task.id}
                }
            )
        if request.status == RequestStatus.PENDING:
            raise BadRequest(
                reason={
                    "task": _(
                        "task %(task_id)s already has request, can't create new request"
                    )
                    % {"task_id": task.id}
                }
            )
        if request.status == RequestStatus.SENT:
            offers_count = request.offer_requests.filter(
                status=OfferRequestStatus.INCLUDED
            ).count()
            if offers_count >= 2:
                raise BadRequest(
                    reason={
                        "task": _("please remove offer first before request new offer")
                        % {}
                    }
                )
        if request.is_expired:
            raise BadRequest(
                reason={
                    "task": _(
                        "task %(task_id)s already has expired request, please reactivate the link"
                    )
                    % {"task_id": task.id}
                }
            )
        return request


class ActivateRequestValidation(InputValidation, TaskMixin):
    def get_object_if_exists(self, user: User, request_input: dict) -> Dict[str, Task]:
        task: Task = self.get_task_if_exists(
            filters={"id": request_input["task_id"], "beneficiary__user__isnull": False}
        )
        self.validate_task_status_is_in_progress(task=task)
        request = task.requests.order_by("id").last()
        if not request:
            raise BadRequest(
                reason={
                    "task": _(
                        "task %(task_id)s doesn't have any request, can't activate request"
                    )
                    % {"task_id": task.id}
                }
            )

        if request.is_expired is False:
            raise BadRequest(
                reason={
                    "task": _(
                        "task %(task_id)s already has active request, can't create new request"
                    )
                    % {"task_id": task.id}
                }
            )
        if user.is_beneficiary:
            notification = (
                task.negotiator.notifications.filter(
                    target_object_id=task.id,
                    target_content_type=ContentType.objects.get_for_model(Task),
                )
                .order_by("id")
                .last()
            )
            data = notification.data if notification and notification.data else {}
            request_expire = data.get("request_expire")
            if request_expire and request_expire == request.expired.isoformat():
                raise BadRequest(
                    reason={
                        "request": _(
                            "you already have Pending request, please wait for request approval"
                        )
                        % {}
                    }
                )
        return {"task": task, "request": request}
