from typing import Dict
from urllib.parse import urlparse

from django.contrib.auth import get_user_model
from django.core.files.storage import default_storage
from django.utils.translation import gettext_lazy as _
from gabbro.graphene import BadRequest
from phonenumber_field.phonenumber import PhoneNumber
from phonenumbers.phonenumberutil import NumberParseException

from common.validators import InputValidation
from deals.models import Beneficiary
from deals.scripts.upload_bulk_benefeciary import (
    UploadBulkBeneficiary as UploadBulkBeneficiaryScript,
)
from formschemas.mixins import FormSchemaMixin
from formschemas.models import FormSchema
from orders.mixins import TaskMixin

User = get_user_model()


class CreateBeneficiaryValidation(InputValidation, TaskMixin, FormSchemaMixin):
    def get_object_if_exists(
        self, user: User, beneficiary_input: dict
    ) -> Dict[str, FormSchema]:
        form_schema_key = beneficiary_input["form_schema"]
        form_schema = (
            self.validate_and_get_form_schema(form_schema_key=form_schema_key)
            if form_schema_key
            else None
        )
        self.validate_beneficiary(beneficiary_input=beneficiary_input)
        return {"form_schema": form_schema}

    @staticmethod
    def validate_beneficiary(beneficiary_input: dict) -> None:
        form_data = beneficiary_input["form_data"]
        mobile = form_data.get("personalData", {}).get("mobileNumber")
        try:
            PhoneNumber.from_string(mobile)
        except NumberParseException as error:
            raise BadRequest(
                reason={
                    "mobile": _("Invalid Beneficiary Phone Number, %(error)s")
                    % {"error": error}
                }
            )
        if PhoneNumber.from_string(mobile).is_valid() is False:
            raise BadRequest(
                reason={"mobile": _("Invalid Beneficiary Phone Number") % {}}
            )
        beneficiary = (
            Beneficiary.objects.filter(data__personalData__mobileNumber__exact=mobile)
            .values_list("data__personalData__mobileNumber", flat=True)
            .first()
        )
        if beneficiary:
            raise BadRequest(reason={"mobile": _("Beneficiary already exists") % {}})
        return None


class UploadBulkBeneficiaryValidation(InputValidation):
    def get_object_if_exists(self, user: User, user_input: dict) -> dict:
        file = user_input["file"]
        url = urlparse(file)
        file_path = default_storage.exists(url.path)
        if not file_path:
            raise BadRequest(
                reason={
                    "file": _("file doesn't exist") % {},
                }
            )
        file = default_storage.open(url.path, "rb")
        upload_beneficiary_script = UploadBulkBeneficiaryScript(metadata={"file": file})
        result, error = upload_beneficiary_script.validate_file()
        if not result:
            raise BadRequest(reason={"file": error})

        return {"file": file}
