from typing import Dict

from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from gabbro.graphene import NotFound, BadRequest

from common.validators import InputValidation
from deals.models import <PERSON><PERSON><PERSON>, Deal, Request, Request<PERSON>tatus, Offer, DealStatus
from deals.strategies import FetchmakanOffersStrategy
from orders.mixins import TaskMixin
from orders.models import Task

User = get_user_model()


class OfferValidation(InputValidation, TaskMixin):
    def get_object_if_exists(self, user: User, offer_input: dict) -> Dict:
        offer = (
            self.get_offer_if_exists(offer_input.get("offer_id"))
            if offer_input.get("offer_id") is not None
            else None
        )
        # validate key if exists
        self.validate_key_if_exists(offer_input=offer_input)
        context = {"offer": offer}
        return context

    @staticmethod
    def validate_key_if_exists(offer_input: dict):
        form_data = offer_input.get("form_data", {})
        drafts = offer_input.get("is_draft", False)
        if not drafts:
            offers_keys = ["completingOffer"]
            if not all(key in offers_keys for key in form_data.keys()):
                raise (BadRequest(reason={"form_data": _("invalid key") % {}}))
            if (
                not form_data.get("completingOffer", {})
                .get("location", {})
                .get("coordinate")
            ):
                raise (
                    BadRequest(reason={"form_data": _("invalid offer location") % {}})
                )
        return form_data

    @staticmethod
    def get_offer_if_exists(offer_id: int) -> Offer:
        offer = Offer.objects.filter(id=offer_id).first()
        if not offer:
            raise NotFound(
                reason={"offer": _("offer matching query doesn't exist") % {}}
            )
        if (
            Deal.objects.filter(favorite_offer__offer_id=offer.external_offer_id)
            .exclude(status=DealStatus.DEAL_CANCELLED)
            .first()
        ):
            raise BadRequest(
                reason={
                    "offer": _("Offer %(offer_id)s already have deal")
                    % {"offer_id": offer_id}
                }
            )

        return offer


class CreateFavoriteOfferValidation(InputValidation, TaskMixin):
    def get_object_if_exists(self, user: User, offer_input: dict) -> Dict:
        task = self.get_task_if_exists(
            filters={"id": offer_input["task_id"], "negotiator__isnull": False}
        )
        self.validate_offer_if_exists(offer_id=offer_input.get("offer_id"), task=task)
        context = {"offer_id": offer_input.get("offer_id"), "task": task}
        return context

    @staticmethod
    def validate_offer_if_exists(offer_id: int, task: Task) -> None:
        task_favorite_offers_count = task.favorite_offers.count()
        if task_favorite_offers_count >= 2:
            raise BadRequest(
                reason={
                    "offers": _(
                        "task already have %(task_favorite_offers_count)s offers"
                    )
                    % {"task_favorite_offers_count": task_favorite_offers_count}
                }
            )
        if task.favorite_offers.filter(offer_id=offer_id).only("offer_id").first():
            raise NotFound(reason={"offer": _("task already have this offer") % {}})
        if (
            Deal.objects.filter(favorite_offer__offer_id=offer_id)
            .exclude(status=DealStatus.DEAL_CANCELLED)
            .only("favorite_offer__offer_id", "status")
            .first()
        ):
            raise BadRequest(reason={"offer": _("offer already have deal") % {}})


class DeleteFavoriteOfferValidation(InputValidation, TaskMixin):
    def get_object_if_exists(self, user: User, favorite_offer_id: int) -> Dict:
        favorite_offer = self.get_favorite_offer_if_exists(
            favorite_offer_id=favorite_offer_id,
        )
        task = favorite_offer.task
        request = self.validate_request(task=task, user=user)
        context = {"favorite_offer": favorite_offer, "task": task, "request": request}
        return context

    @staticmethod
    def validate_request(task: Task, user: User) -> Request:
        request = task.requests.order_by("id").last()
        if request and request.is_expired and user.is_beneficiary:
            raise BadRequest(
                reason={
                    "task": _(
                        "task %(task_id)s already has expired request, please reactivate the link"
                    )
                    % {"task_id": task.id}
                }
            )
        return request

    @staticmethod
    def get_favorite_offer_if_exists(favorite_offer_id: int) -> FavoriteOffer:
        favorite_offer: FavoriteOffer = FavoriteOffer.objects.filter(
            id=favorite_offer_id
        ).first()
        if not favorite_offer:
            raise NotFound(
                reason={"favorite_offer": _("offer matching query doesn't exist") % {}}
            )

        if Deal.objects.filter(favorite_offer__task_id=favorite_offer.task_id).first():
            raise BadRequest(
                reason={
                    "favorite_offer": _(
                        "Task %(task_id)s already have deal, can't be deleted"
                    )
                    % {"task_id": favorite_offer.task_id}
                }
            )
        return favorite_offer


class FetchMakanOffersValidator(InputValidation, TaskMixin):
    def get_object_if_exists(self, user: User, offer_input: dict) -> Dict:
        task = self.get_task_if_exists(
            filters={
                "id": offer_input["task_id"],
                "negotiator__isnull": False,
                "form_data__negotiator__negotiatorData__isnull": False,
                "form_data__negotiator__placeOrder__isnull": False,
            }
        )
        context = {"task": task}
        return context


class ShareFavoriteOfferValidation(InputValidation, TaskMixin):
    def get_object_if_exists(self, user: User, offer_input: dict) -> Dict:
        task = self.get_task_if_exists(
            filters={"id": offer_input["task_id"], "negotiator__isnull": False}
        )
        if task.favorite_offers.count() == 0:
            raise BadRequest(
                reason={
                    "task": _("task has no favorite offer, please add some offers") % {}
                }
            )
        self.validate_task_status_is_in_progress(task=task)
        request = self.validate_if_task_has_pending_request(task=task)
        offers = self._validate_offers(task=task, user=user)
        return {"task": task, "request": request, "offers": offers}

    @staticmethod
    def _validate_offers(task: Task, user: User):
        offers_ids: list = list(task.favorite_offers.values_list("offer_id", flat=True))
        if Deal.objects.filter(favorite_offer__offer_id__in=offers_ids).first():
            raise BadRequest(
                reason={"task": _("can't share offers that already have deal") % {}}
            )
        offers = (
            FetchmakanOffersStrategy().fetch_offers(
                user,
                context={"task": task},
                offer_data={"ids": offers_ids},
                categorise_offers=False,
            )
            or {}
        )
        offers = offers.get("data", [])
        makan_offers_ids = {offer["id"]: offer for offer in offers}
        difference = set(offers_ids) - set(makan_offers_ids.keys())
        if difference or not all(
            [offer["add_to_favorite"] is True for offer in offers]
        ):
            raise BadRequest(
                reason={
                    "task": _("cant share these uncompleted offers %(difference)s")
                    % {"difference": difference}
                }
            )
        return offers

    @staticmethod
    def validate_if_task_has_pending_request(task: Task) -> Request:
        request = task.requests.order_by("id").last()
        if request and request.is_expired:
            raise BadRequest(
                reason={
                    "task": _(
                        "task %(task_id)s already has expired request, please reactivate the link"
                    )
                    % {"task_id": task.id}
                }
            )
        if request and request.status == RequestStatus.SENT:
            raise BadRequest(
                reason={
                    "request": _("beneficiary didn't response for your last request.")
                    % {}
                }
            )
        if request and task.favorite_offers.filter(deal__isnull=False).first():
            raise BadRequest(
                reason={
                    "task": _("task already have deal, can't share offers again") % {}
                }
            )
        return request
