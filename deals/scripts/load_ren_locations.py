import logging

from fiona.io import MemoryFile
from utils.general_utils import open_storage_file, remove_null_values
from utils.geometry import get_valid_geometry
from deals.models.record import Record
from django.contrib.gis.geos.error import GEOSException

logger = logging.getLogger("deals")


class LoadRenLocations(object):
    def __init__(self, metadata: dict) -> None:
        self.file = metadata.get("file")
        self.file_extension = ["gpkg"]
        self.layer = metadata.get("layer")
        self.batch_size = metadata.get("patch_size")

    def read_records_from_geopackage(
        self,
        file,
        parse_all=False,
        parsing_fields=None,
    ):
        """Generator function to convert GeoPackage layers to GeoJSON-like structures."""
        geo_pkg_bytes = file.read()
        with MemoryFile(geo_pkg_bytes) as memory_file:
            for layer_name in memory_file.listlayers():
                with memory_file.open(layer=layer_name) as layer:
                    for feature in layer:
                        properties = dict(feature["properties"])
                        yield {
                            "type": "Feature",
                            "geometry": dict(feature["geometry"]),
                            "properties": properties,
                        }

    def create_records_from_features(self, features, layer, DataModel):
        records = list()
        invalid = list()

        for i, f in enumerate(features):
            # extract source_properties
            source_properties = remove_null_values(f.get("properties", dict()))
            # create geo geometry object
            try:
                geometry = get_valid_geometry(f.get("geometry"))
            except GEOSException as e:
                logger.debug(f"Invalid geometry with error: {e}")
                invalid.append((i, f))
                continue
            if not geometry.valid:
                invalid.append((i, f))
                continue
            # create a record object
            r = DataModel(
                layer=layer,
                geometry=geometry,
                source_properties=source_properties,
            )
            records.append(r)
        if records:
            # Creating the records in batches
            DataModel.objects.bulk_create(
                records, batch_size=self.batch_size, ignore_conflicts=True
            )
        return records

    def execute(self):
        """

        - read Geopackage file and execute the script after validation
        - return Boolean and list of messages
        """
        storage_file_object = open_storage_file(file_path=self.file)
        if not storage_file_object:
            logger.debug(f"{self.file} does not exist")
            return
        features = self.read_records_from_geopackage(file=storage_file_object)
        recrods = self.create_records_from_features(
            features=features, layer=self.layer, DataModel=Record
        )
        return recrods
