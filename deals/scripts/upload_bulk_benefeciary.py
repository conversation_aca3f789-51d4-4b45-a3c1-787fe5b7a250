import json
import os
from io import BytesIO

import jsonschema
import pandas as pd
from django.conf import settings
from django.contrib.messages import DEFAULT_LEVELS
from django.core.files.storage import default_storage
from django.db.models import Q
from django.utils.translation import gettext_lazy as _
from gabbro.utils import generate_random_string, generate_random_simple_code
from phonenumber_field.phonenumber import PhoneNumber

from deals.models.beneficiary import Beneficiary, BeneficiaryDataSource
from formschemas.models import FormSchema

MEDIA_ROOT = getattr(settings, "MEDIA_ROOT", None)
REQUIRED_FILE_COLUMNS = {
    "code": {"name": "ID_CODE", "type": str},
    "name": {"name": "الاسم", "type": str},
    "external_id": {"name": "رقم التقنيات", "type": int},
    "mobileNumber": {
        "name": "رقم الجوال",
        "type": str,
    },
}


class UploadBulkBeneficiary(object):
    """
    - Upload File oF Beneficiaries
    - Create Bulk Beneficiary
    """

    def __init__(self, metadata: dict) -> None:
        self.file = metadata.get("file")
        self.file_extension = ["xlsx"]
        self.form_schema = {}
        self.beneficiary = {}
        self.beneficiary_batch = []
        self.beneficiary_data = {}
        self.total_errors = {
            "file_errors": {},
            "script_errors": {},
        }
        self.metadata_messages = {}
        self.batch_size = 100
        self.seen_records = set()
        self.seen_mobile_numbers = set()
        self.HEADERS: dict = REQUIRED_FILE_COLUMNS
        self.file_headers = ["الاخطاء"]
        self.errors_file_data = []
        self.created_records = 0
        self.error_count = 0
        self.schema = None
        self.mapper = dict
        self.type_mapping = {
            "int": int(),
            "str": str,
        }
        self.beneficiary_record = 1

    def read_mapper(self) -> json:
        """
        - Read Beneficiary Mapper Data
        """ ""
        with open(
            os.path.join(
                settings.BASE_DIR,
                "formschemas",
                "mappers",
                "beneficiary_mapper_v1.json",
            ),
            "r",
        ) as file:
            return json.load(file)

    def validate_file(self) -> (bool, str):
        """
        - validate file extension
        - validate all required columns is exists
        """
        if self.file is None:
            return False, "File Is Required"
        file_extension = self.file.name.split(".")[-1]
        if file_extension not in self.file_extension:
            return False, "Invalid File Extension"
        df = pd.read_excel(self.file)
        if not all(
            key in df.columns.tolist() for key in list(self.read_mapper().keys())
        ):
            return False, "Missing Columns"
        return True, ""

    def error_message(self, message, error_path: str, error_type: str) -> None:
        """
        - add an error message
        """
        errors_mapper = {
            "file_errors": self.total_errors["file_errors"].get(error_path, []),
            "script_errors": self.total_errors["script_errors"].get(error_path, []),
        }
        _errors: list = errors_mapper[error_type]
        if _errors:
            _errors.append(message)
            self.total_errors[error_type][error_path] = _errors
        else:
            self.total_errors[error_type][error_path] = [message]
        self.error_count += 1

    def metadata_message(self, message, message_path: str) -> None:
        """
        - add Metadata messages
        """
        self.metadata_messages[message_path] = message

    def validate_jsonschema_exists(self) -> bool:
        form_schema = (
            FormSchema.objects.filter(
                ~Q(json_schema__form=[]) & ~Q(json_schema__form={}),
                key="upload-bulk-beneficiaries",
                json_schema__form__isnull=False,
            )
            .distinct()
            .first()
        )
        if not form_schema:
            message = _("Form Schema Does Not Exist") % {}
            self.error_message(
                message=message, error_path="form_schema", error_type="script_errors"
            )
            return False
        self.form_schema = form_schema.json_schema.get("form", {})
        return True

    def validate_beneficiary(self, beneficiary_data) -> bool:
        """
        - validate if beneficiary exists with the same code and external_id
        - validate beneficiary data not in seen records to avoid duplicated beneficiary data before bulk create
        """
        external_id = beneficiary_data[self.HEADERS["external_id"]["name"]]
        code = beneficiary_data[self.HEADERS["code"]["name"]]
        mobile = beneficiary_data["personalData"]["mobileNumber"]
        try:
            if PhoneNumber.from_string(mobile).is_valid() is False:
                message = _("Invalid Beneficiary Phone Number") % {}
                self.error_message(
                    message=message,
                    error_path=f"{self.beneficiary_record}",
                    error_type="file_errors",
                )
                return False

        except Exception:
            message = _("Invalid Beneficiary Phone Number") % {}
            self.error_message(
                message=message,
                error_path=f"{self.beneficiary_record}",
                error_type="file_errors",
            )
            return False

        if (
            code,
            external_id,
        ) in self.seen_records or mobile in self.seen_mobile_numbers:
            message = _("Duplicated Beneficiary Data") % {}
            self.error_message(
                message=message,
                error_path=f"{self.beneficiary_record}",
                error_type="file_errors",
            )
            return False
        return True

    @staticmethod
    def convert_to_default_values(user_data, expected_type: str):
        """
        convert to default value if user_data is None or not valid
        """
        null_values = {"-", "_"}
        if pd.isna(user_data) or user_data in null_values:
            return True
        return False

    def reformat_beneficiary_data(self, user_data: dict, mapper: dict):
        mapper = mapper or self.mapper
        for key, value in mapper.items():
            column_name = value.get("column_name", None)
            default = value.get("default")
            column_type = value.get("type")
            user_data[column_name] = (
                default
                if self.convert_to_default_values(
                    user_data=user_data[column_name], expected_type=column_type
                )
                else user_data[column_name]
            )
        user_data = self.generate_mandatory_db_field(user_data=user_data)
        beneficiary_data = {
            "ID_CODE": user_data["ID_CODE"],
            "request": {"registrationDate": user_data["registrationDate"]},
            "location": {
                "city": user_data["city"],
                "region": user_data["region"],
            },
            "familyData": {
                "familyMembersCount": user_data["familyMembersCount"],
            },
            "destination": user_data["destination"],
            "personalData": {
                "name": user_data["name"],
                "ageHijri": user_data["ageHijri"],
                "mobileNumber": user_data["mobileNumber"],
            },
            "financialData": {
                "AIP1": user_data["AIP1"],
                "AIP2": user_data["AIP2"],
                "AIP3": user_data["AIP3"],
                "AIP4": user_data["AIP4"],
                "AIP5": user_data["AIP5"],
                "salary": user_data["salary"],
                "funder1": user_data["funder1"],
                "funder2": user_data["funder2"],
                "funder3": user_data["funder3"],
                "funder4": user_data["funder4"],
                "funder5": user_data["funder5"],
                "salaryBank": user_data["salaryBank"],
                "entitlement": user_data["entitlement"],
                "fundingAmount": user_data["fundingAmount"],
                "additionalIncome": user_data["additionalIncome"],
                "housingAllowance": user_data["housingAllowance"],
                "monthlyDeduction": user_data["monthlyDeduction"],
                "remainingDurationMonths": user_data["remainingDurationMonths"],
            },
            "supportPackages": {
                "product": user_data["product"],
            },
            "professionalData": {
                "job": user_data["job"],
            },
            "personalRealEstateData": {
                "hasLand": user_data["hasLand"],
                "startedBuilding": user_data["startedBuilding"],
                "hasBuildingLicense": user_data["hasBuildingLicense"],
            },
            "رقم التقنيات": user_data["رقم التقنيات"],
        }
        return beneficiary_data

    def validate_form_data_schema(self, form_data: dict) -> bool:
        validator = jsonschema.Draft7Validator(self.form_schema)
        errors = sorted(validator.iter_errors(form_data), key=lambda e: list(e.path))
        for error in errors:
            field_path = "/".join(map(str, error.path)) if error.path else "root"
            self.error_message(
                message=f"field_path: {field_path}, error: {error.message}",
                error_path=f"{self.beneficiary_record}",
                error_type="file_errors",
            )
        return False if errors else True

    def generate_mandatory_db_field(self, user_data: dict):
        destination = user_data["destination"]
        if destination != "صندوق التنمية":
            user_data["ID_CODE"] = f"EXBN_00_{generate_random_string(length=6)}"
            user_data[self.HEADERS["external_id"]["name"]] = int(
                generate_random_simple_code(length=6)
            )
        return user_data

    def generate_row_errors_data(self, data: dict):
        errors = self.total_errors.get("file_errors", {}).get(
            str(self.beneficiary_record), []
        )
        columns = {value["column_name"]: key for key, value in self.mapper.items()}
        renamed_data = {columns.get(key): value for key, value in data.items()}
        renamed_data["الاخطاء"] = errors
        self.errors_file_data.append(renamed_data)

    def bulk_create(self):
        """
        - create bulk beneficiary after validation
        """
        for user_data in self.beneficiary_data:
            self.beneficiary_record += 1
            self.beneficiary = user_data
            reformated_data = self.reformat_beneficiary_data(
                user_data=user_data, mapper=self.mapper
            )
            validation = (
                # validate beneficiary data
                self.validate_beneficiary(beneficiary_data=reformated_data),
                # validate beneficiary form data
                self.validate_form_data_schema(form_data=reformated_data),
            )
            if not all(validation):
                self.generate_row_errors_data(user_data)
                continue
            destination = user_data["destination"]
            code = reformated_data.get("ID_CODE")
            external_id = reformated_data[self.HEADERS["external_id"]["name"]]
            data = reformated_data
            name = reformated_data["personalData"]["name"]
            beneficiary = Beneficiary(
                code=code,
                name=name,
                external_id=external_id,
                data=data,
                data_source=BeneficiaryDataSource.INTERNAL
                if destination == "صندوق التنمية"
                else BeneficiaryDataSource.EXTERNAL,
            )
            self.beneficiary_batch.append(beneficiary)
            # # add record to seen records
            self.seen_records.add((beneficiary.code, beneficiary.external_id))
            self.seen_mobile_numbers.add(
                data.get("personalData", {}).get("mobileNumber", "")
            )
        if self.beneficiary_batch:
            beneficiaries = Beneficiary.objects.bulk_create(
                self.beneficiary_batch,
                batch_size=self.batch_size,
            )
            self.created_records += len(self.beneficiary_batch)
            for object in beneficiaries:
                data_source = object.data_source
                object.code = (
                    f"EXBN_00_{object.id}"
                    if data_source == BeneficiaryDataSource.EXTERNAL
                    else object.code
                )
                object.external_id = (
                    object.id
                    if data_source == BeneficiaryDataSource.EXTERNAL
                    else object.external_id
                )
                object.data["ID_CODE"] = str(object.code)
                object.data["رقم التقنيات"] = str(object.external_id)

            Beneficiary.objects.bulk_update(
                beneficiaries, fields=["external_id", "code", "data"]
            )

    def prepare_file_data(self) -> None:
        self.mapper: dict = self.read_mapper()
        dtypes = {
            key: self.type_mapping[value["type"]] for key, value in self.mapper.items()
        }
        df = pd.read_excel(self.file, dtype=dtypes)
        # drop rows where all elements are NaN
        df = df.dropna(how="all")
        # rename all column to the name of mapper keys
        self.file_headers.extend(df.columns.tolist())

        rename_columns = {
            key: value["column_name"] for key, value in self.mapper.items()
        }
        df.rename(columns=rename_columns, inplace=True)
        records = df.to_dict("records")

        self.beneficiary_data = [
            record
            for record in records
            if any(pd.notna(value) for value in record.values())
        ]

    @staticmethod
    def generate_errors_file(
        data: list[list], path: str, file_name, header: list[str] = None
    ):
        fs = default_storage
        file = BytesIO()
        pd.DataFrame(
            data,
            columns=header,
        ).to_excel(file, index=False)
        file_path = os.path.join(path, file_name)
        fs.save(name=file_path, content=file)
        return file_path

    def execute(self):
        """
        - check if JsonSchema Exists
        - read Excel file and execute the script after validation
        - return Boolean, list of messages and errors file path
        """
        exists_beneficiaries = Beneficiary.objects.all().values(
            "code", "external_id", "data__personalData__mobileNumber"
        )
        self.seen_records = {
            (beneficiary["code"], beneficiary["external_id"])
            for beneficiary in exists_beneficiaries
        }
        self.seen_mobile_numbers = {
            beneficiary["data__personalData__mobileNumber"]
            for beneficiary in exists_beneficiaries
        }
        self.prepare_file_data()
        validation = (self.validate_jsonschema_exists(),)
        if not all(validation):
            return (
                False,
                {"metadata": self.metadata_messages, "errors": self.total_errors},
                [
                    {
                        "message": _("%(error_count)s Error")
                        % {"error_count": self.error_count},
                        "level": DEFAULT_LEVELS["ERROR"],
                    },
                ],
            )

        try:
            self.bulk_create()
            execution_status = True
        except Exception as e:
            execution_status = False
            if self.beneficiary_batch:
                Beneficiary.objects.bulk_create(
                    self.beneficiary_batch, batch_size=self.batch_size
                )
                self.created_records += len(self.beneficiary_batch)
            message = str(e)
            error_path = f"{self.beneficiary_record}"
            self.error_message(
                message=message, error_path=error_path, error_type="script_errors"
            )

        # generate errors file
        file_name = self.file.name.split("/")[-1]
        errors_file_path = os.path.join(
            MEDIA_ROOT, "attachments", "beneficiaries_errors"
        )
        errors_file_path = self.generate_errors_file(
            data=self.errors_file_data,
            path=errors_file_path,
            file_name=file_name,
            header=self.file_headers,
        )

        # self.metadata_message(message_path="total_errors", message=self.total_errors)
        self.metadata_message(
            message_path="created_records", message=self.created_records
        )
        return (
            execution_status,
            {"metadata": self.metadata_messages, "errors": self.total_errors},
            [
                {
                    "message": _("%(created_records)s Beneficiary Created")
                    % {"created_records": self.created_records},
                    "level": DEFAULT_LEVELS["SUCCESS"],
                },
                {
                    "message": _("%(error_count)s Error")
                    % {"error_count": self.error_count},
                    "level": DEFAULT_LEVELS["ERROR"],
                },
            ],
            errors_file_path,
        )
