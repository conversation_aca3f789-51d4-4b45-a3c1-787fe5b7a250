import json
import os
from abc import ABC, abstractmethod
from datetime import <PERSON><PERSON><PERSON>

from django.contrib.postgres.aggregates import <PERSON><PERSON>yAgg
from django.db.models import F, Q
from django.template.loader import render_to_string
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from gabbro.graphene import BadRequest
from rest_framework.exceptions import ValidationError as DRFValidationError

from auditing.mixins import AuditMixin
from deals.models import (
    OfferType,
    OfferStatus,
    FavoriteOffer,
    Layer,
    Record,
    Offer,
    RequestStatus,
    Request,
    Deal,
    OFFER_DATA_MAPPER,
    DealStatus,
    OfferRequest,
    ExternalOffer,
    OfferRequestStatus,
)
from deals.serializers import (
    CreateFavoriteOfferSerializer,
    CreateRequestSerializer,
    CreateOfferSerializer,
)
from formschemas.mixins import FormSchemaMixin
from orders.models import Task
from users.models import User, UserRoleChoices, RenRole
from utils.accounts import accounts
from utils.general_utils import get_nested_value
from utils.geometry import calculate_boundaries_postgis, validate_geometry
from utils.locations import locations
from utils.offers import OfferClient


class OfferStrategy(ABC, FormSchemaMixin, AuditMixin):
    @abstractmethod
    def create_offer(self, user, context: dict, offer_data: dict):
        pass

    @abstractmethod
    def update_offer(self, user, context: dict, offer_data: dict):
        pass

    @abstractmethod
    def fetch_offers(self, user, context: dict, offer_data: dict):
        pass

    def _prepare_offer_data(self, offer_data: dict):
        form_schema = self.validate_and_get_form_schema(
            form_schema_key=offer_data["form_schema_key"]
        )
        form_data = self.get_valid_form_data(
            input_data=offer_data,
            form_schema=form_schema,
            origin_form_data={},
            multiple_forms=False,
        )
        data = {
            "offer_type": OfferType.INTERNAL,
            "status": OfferStatus.NOT_RESERVED,
            "license_number": offer_data.get("license_number"),
            "data": form_data,
        }
        return data

    def _update_offer_record(self, offer: Offer, user: User) -> Offer:
        layer, created = Layer.objects.get_or_create(key="ren-offers")
        coordinate: dict = offer.extract_data(field="coordinate")
        geometry = None
        if coordinate:
            try:
                geometry = validate_geometry(str(coordinate))
            except DRFValidationError as error:
                raise BadRequest(reason=error.args[0])
        if not offer.record:
            record = Record.objects.create(layer=layer, geometry=geometry)
            offer.record = record
            offer.save(update_fields=["record"])
            self.audit_for_object_creation(
                content_object=record,
                user=user,
                data={
                    "layer_id": layer.id,
                    "geometry": json.loads(record.geometry.geojson)
                    if record.geometry
                    else None,
                    "source_properties": record.source_properties,
                },
            )
        else:
            record = offer.record
            record.geometry = geometry
            record.save(update_fields=["geometry"])
            self.audit_for_object_updating(
                content_object=record,
                user=user,
                data={
                    "layer_id": layer.id,
                    "geometry": json.loads(record.geometry.geojson)
                    if record.geometry
                    else None,
                    "source_properties": record.source_properties,
                },
            )
        # update boundaries
        self._update_layer_boundaries(layer=layer)
        return offer

    @staticmethod
    def _update_layer_boundaries(layer: Layer):
        boundaries = calculate_boundaries_postgis(
            records=layer.record_set.all(), field="geometry"
        )
        layer.boundaries = boundaries
        layer.save(update_fields=["boundaries"])


class FavoriteOfferStrategy(ABC, FormSchemaMixin, AuditMixin):
    @abstractmethod
    def create_favorite_offer(self, user, context: dict, offer_data: dict):
        pass

    @abstractmethod
    def delete_favorite_offer(self, user, context: dict):
        pass

    @abstractmethod
    def share_favorite_offers(self, user, context: dict):
        pass


class CreateOfferStrategy(OfferStrategy):
    def create_offer(self, user, context, offer_data):
        data = self._prepare_offer_data(offer_data=offer_data)
        serializer = CreateOfferSerializer(data=data)
        if not serializer.is_valid():
            raise BadRequest(reason=serializer.errors)
        offer = serializer.save()
        # create record for this offer
        offer = self._update_offer_record(offer=offer, user=user)
        if offer.data and offer.geometry is not None and not offer.data.get("drafts"):
            data = {
                "geometry": json.loads(offer.geometry.geojson),
                "data": offer.data,
            }
            offer_data = OfferClient().create_vd_offer(data=data)
            offer.external_offer_id = offer_data.get("id")
            offer.data["completingOffer"]["offerData"]["id"] = str(offer_data.get("id"))
            offer.save(update_fields=["external_offer_id", "data"])
        self.audit_for_object_creation(
            content_object=offer,
            user=user,
            data={
                "form_data": offer.data,
                "status": {offer.status: offer.get_status_display()},
            },
        )
        return offer

    def update_offer(self, user, context: dict, offer_data: dict):
        raise NotImplementedError()

    def fetch_offers(self, user, context: dict, offer_data: dict):
        raise NotImplementedError()


class UpdateOfferStrategy(OfferStrategy):
    def update_offer(self, user, context: dict, offer_data: dict):
        offer = context.get("offer")
        data = self._prepare_offer_data(offer_data=offer_data)
        serializer = CreateOfferSerializer(data=data, partial=True, instance=offer)
        if not serializer.is_valid():
            raise BadRequest(reason=serializer.errors)
        offer = serializer.save()
        # update record of this offer
        offer = self._update_offer_record(offer=offer, user=user)
        if offer.data and not offer.data.get("drafts"):
            data = {
                "geometry": json.loads(offer.geometry.geojson),
                "data": offer.data,
            }
            if offer.external_offer_id:
                data["id"] = offer.external_offer_id
                offer_data = OfferClient().update_vd_offer(data=data)
            else:
                offer_data = OfferClient().create_vd_offer(data=data)

            offer.external_offer_id = offer_data.get("id")
            offer.data["completingOffer"]["offerData"]["id"] = str(offer_data.get("id"))
            offer.save(update_fields=["external_offer_id", "data"])
        self.audit_for_object_updating(
            content_object=offer,
            user=user,
            data={
                "form_data": offer.data,
                "status": {offer.status: offer.get_status_display()},
            },
        )
        return offer

    def create_offer(self, user, context, offer_data):
        raise NotImplementedError()

    def fetch_offers(self, user, context: dict, offer_data: dict):
        raise NotImplementedError()


class CreateFavoriteOfferStrategy(FavoriteOfferStrategy):
    def create_favorite_offer(self, user, context, offer_data):
        task = context["task"]
        offer_id = context["offer_id"]
        data = {"task": task.id, "offer_id": offer_id}
        serializer = CreateFavoriteOfferSerializer(data=data)
        if not serializer.is_valid():
            raise BadRequest(reason=serializer.errors)
        favorite_offer = serializer.save()
        self.audit_for_object_creation(
            content_object=favorite_offer,
            user=user,
            data={
                "offer_id": offer_id,
                "task_id": favorite_offer.task.id,
            },
        )
        return favorite_offer

    def delete_favorite_offer(self, user, context: dict):
        raise NotImplementedError()

    def share_favorite_offers(self, user, context: dict):
        raise NotImplementedError()


class DeleteFavoriteOfferStrategy(FavoriteOfferStrategy):
    def create_favorite_offer(self, user, context: dict, offer_data: dict):
        raise NotImplementedError()

    def share_favorite_offers(self, user, context: dict):
        raise NotImplementedError()

    def delete_favorite_offer(self, user, context):
        favorite_offer: FavoriteOffer = context["favorite_offer"]
        request = context["request"]
        self.audit_for_object_deleting(
            object_id=favorite_offer.id,
            user=user,
            data={
                "task": favorite_offer.task.id,
                "offer": favorite_offer.offer_id,
                "model": str(favorite_offer.__class__.__name__),
                "id": favorite_offer.id,
            },
        )
        task = favorite_offer.task
        offer_id = favorite_offer.offer_id
        favorite_offer.delete()
        offer = (
            request.offer_requests.filter(
                Q(internal_offer__external_offer_id=str(offer_id))
                | Q(external_offer__external_offer_id=str(offer_id)),
                status=OfferRequestStatus.INCLUDED,
            ).first()
            if request
            else None
        )
        if offer:
            offer.status = OfferRequestStatus.EXCLUDED
            offer.save(update_fields=["status"])
            self.audit_for_object_updating(
                content_object=offer,
                user=user,
                data={
                    "data": offer.data,
                    "task_id": task.id,
                    "status": {
                        str(OfferRequestStatus.EXCLUDED.value): str(
                            OfferRequestStatus.EXCLUDED.label
                        )
                    },
                },
            )


class FetchmakanOffersStrategy(OfferStrategy):
    def create_offer(self, user, context: dict, offer_data: dict):
        raise NotImplementedError()

    def update_offer(self, user, context: dict, offer_data: dict):
        raise NotImplementedError()

    def share_favorite_offers(self, user, context: dict):
        raise NotImplementedError()

    @staticmethod
    def categorise_offers(offers: list) -> list:
        all_offers = {
            "completed_offers": [],
            "uncompleted_offers": [],
            "external_offers": [],
        }
        for offer in offers:
            offer_type = offer["offer_type"]
            if offer_type == "external":
                all_offers["external_offers"].append(offer)
            elif offer_type == "completed":
                all_offers["completed_offers"].append(offer)
            else:
                all_offers["uncompleted_offers"].append(offer)
        return [all_offers]

    def fetch_offers(
        self, user, context: dict, offer_data: dict, categorise_offers: bool = True
    ) -> dict:
        offers_ids = offer_data.get("ids") or []
        offers_ids = [int(_id) for _id in offers_ids]
        offers = self.fetch_makan_offers(context=context)
        offers = self.inject_ren_drafts_offers(offers=offers)
        finished_deal_offers = Deal.objects.filter(
            status=DealStatus.DEAL_FINISHED
        ).values_list("favorite_offer__offer_id", flat=True)
        if finished_deal_offers:
            offers = [
                offer for offer in offers if offer.get("id") not in finished_deal_offers
            ]
        if offers_ids:
            _ids = {item["id"]: item for item in offers if item.get("id") is not None}
            offers = [
                _ids.get(offer_id) for offer_id in offers_ids if _ids.get(offer_id)
            ]
        data: dict = self.inject_to_offer_data(data=offers, context=context)
        data["data"] = (
            self.categorise_offers(offers=data["data"]) if categorise_offers else offers
        )
        return data

    @staticmethod
    def fetch_makan_offers(context: dict) -> list:
        task = context["task"]
        place_order = task.form_data.get("negotiator", {}).get("placeOrder", {})
        negotiator_data = task.form_data.get("negotiator", {}).get("negotiatorData", {})
        if not all([place_order, negotiator_data]):
            raise BadRequest(
                reason={
                    "form_data": _("uncompleted task form data") % {},
                }
            )
        prefer_price = place_order.get("financialPreferencesLevelTwo", {}).get(
            "preferredPurchaseAmount"
        )
        property_type = negotiator_data.get("realEstatePreferences", {}).get(
            "preferredPropertyType"
        )
        preferred_locations = place_order.get("locationPreferencesLevelTwo", {})
        data = {
            "prefer_price": prefer_price,
            "property_type": property_type,
            "preferred_locations": preferred_locations,
        }
        makan_offers = locations.get_makan_offers(data=data)
        return makan_offers

    def inject_to_offer_data(self, context: dict, data: list) -> dict:
        task: Task = context["task"]
        offers = data
        offers_ids = [external_offer.get("id") for external_offer in offers]
        self.inject_status_into_external_offers(offers, offers_ids)
        self.inject_negotiators_into_external_offers(offers, offers_ids)
        self.inject_favorite_offer_id_into_external_offers(offers, offers_ids, task.id)
        self.inject_ren_offer_id_into_external_offers(
            offers=offers, offer_ids=offers_ids
        )
        self.inject_offer_type(offers=offers)
        geometries = self.inject_geometries_into_offers(offers=offers)
        boundaries = self.calculate_boundaries_geojson(geometries=geometries)
        data = {
            "data": offers,
            "geometries": geometries,
            "boundaries": boundaries,
        }
        return data

    @staticmethod
    def inject_offer_type(offers: list):
        for offer in offers:
            offer_form = offer.get("offer_form", {})
            office_data = offer_form.get("data", {}).get("office", {})
            completing_offer = office_data.get("completingOffer")
            data_source = offer.get("data_source")
            offer_type = (
                "external"
                if data_source not in ["offers_project", "ren_project"]
                else "completed"
                if completing_offer
                else "uncompleted"
            )
            offer["offer_type"] = offer_type
            add_to_favorite = False if not completing_offer else True
            offer["add_to_favorite"] = add_to_favorite
        return offers

    @staticmethod
    def inject_ren_drafts_offers(offers: list):
        ren_drafts_offers = Offer.objects.filter(data__drafts__isnull=False).only(
            "record__geometry", "id", "data", "external_offer_id"
        )
        ren_drafts_offers = [
            {
                "geometry": json.loads(offer.record.geometry.geojson)
                if offer.record and offer.record.geometry
                else None,
                "id": offer.external_offer_id,
                "ren_offer_id": offer.id,
                "offer_form": {"data": {"office": offer.data if offer.data else {}}},
                "data_source": "ren_project",
            }
            for offer in ren_drafts_offers
        ]
        ren_drafts_offers.extend(offers)
        return ren_drafts_offers

    @staticmethod
    def calculate_boundaries_geojson(geometries: list):
        geometries = [obj["geometry"] for obj in geometries]
        boundaries = calculate_boundaries_postgis(
            records=[], geojson_data=geometries, field=""
        )
        return json.loads(boundaries.geojson) if boundaries else None

    @staticmethod
    def inject_geometries_into_offers(offers: list):
        geometries = [
            {
                "offer_id": obj.get("id"),
                "offer_price": get_nested_value(
                    data=obj.get("offer_form", {})
                    .get("data", {})
                    .get("office", {})
                    .get("completingOffer", {}),
                    path=OFFER_DATA_MAPPER["real_estate_price"],
                ),
                "geometry": obj.get("geometry"),
            }
            for obj in offers
            if obj.get("geometry")
        ]
        return geometries

    @staticmethod
    def inject_status_into_external_offers(offers: list, offer_ids: list):
        external_offer_deals = (
            Deal.objects.filter(favorite_offer__offer_id__in=offer_ids)
            .select_related("favorite_offer")
            .only("favorite_offer__offer_id")
            .distinct()
        )
        grouped_external_offer_deals = {
            external_offer_deal.favorite_offer.offer_id: external_offer_deal
            for external_offer_deal in external_offer_deals
        }
        for external_offer in offers:
            if grouped_external_offer_deals.get(external_offer.get("id")):
                external_offer["status"] = _("Reserved") % {}
            else:
                external_offer["status"] = _("Not Reserved") % {}

    @staticmethod
    def inject_negotiators_into_external_offers(offers: list, offers_ids: list):
        negotiators_qs = (
            FavoriteOffer.objects.filter(offer_id__in=offers_ids)
            .values("offer_id")  # Group by offer_id
            .annotate(
                first_names=ArrayAgg(F("task__negotiator__first_name"), distinct=True),
                last_names=ArrayAgg(F("task__negotiator__last_name"), distinct=True),
            )
            .distinct()
        )
        grouped_negotiators = {
            item["offer_id"]: [
                {"first_name": fn, "last_name": ln}
                for fn, ln in zip(item["first_names"], item["last_names"])
            ]
            for item in negotiators_qs
        }
        for external_offer in offers:
            external_offer_id = external_offer.get("id")
            offer_negotiator = grouped_negotiators.get(external_offer_id, [])
            external_offer["negotiators"] = {
                "data": offer_negotiator,
                "count": len(offer_negotiator),
            }

    @staticmethod
    def inject_favorite_offer_id_into_external_offers(
        offers: list, offer_ids: list, task_id: int
    ):
        favorite_offers = (
            FavoriteOffer.objects.filter(task_id=task_id, offer_id__in=offer_ids)
            .only("id", "offer_id")
            .distinct("id")
        )
        grouped_favorite_offers = {
            favorite_offer.offer_id: favorite_offer.id
            for favorite_offer in favorite_offers
        }
        for external_offer in offers:
            external_offer["favorite_offer_id"] = grouped_favorite_offers.get(
                external_offer.get("id")
            )

    @staticmethod
    def inject_ren_offer_id_into_external_offers(offers: list[dict], offer_ids: list):
        internal_offers = set(
            Offer.objects.filter(external_offer_id__in=offer_ids).only(
                "id", "external_offer_id"
            )
        )
        grouped_internal_offers = {
            internal_offer.external_offer_id: internal_offer.id
            for internal_offer in internal_offers
        }
        for offer in offers:
            if offer["data_source"] == "ren_project":
                offer["ren_offer_id"] = offer["ren_offer_id"]
            else:
                offer["ren_offer_id"] = grouped_internal_offers.get(offer.get("id"))


class ShareFavoriteOffersStrategy(FavoriteOfferStrategy):
    def share_favorite_offers(self, user, context: dict) -> Request:
        task = context["task"]
        self.check_user_if_exists(task=task)
        beneficiary_user: User = self.check_user_if_exists(task=task)
        self.share_offer_with_beneficiary(
            phone=beneficiary_user.phone.raw_input, task=task
        )
        offers = context.get("offers", [{}])
        request = self.create_new_request(task=task, offers=offers, user=user)
        # check request offers
        self.check_request_offers(request=request, offers=offers)
        return request

    @staticmethod
    def check_user_if_exists(task: Task) -> User:
        if user := task.beneficiary.user:
            return user

        phone = task.beneficiary.data.get("personalData", {}).get("mobileNumber")
        if not phone:
            raise BadRequest(
                reason={
                    "errors": [
                        {
                            "field": "personalData.mobileNumber",
                            "message": _("This field is required.") % {},
                        }
                    ]
                }
            )
        user_data, error = accounts.internal_retrieve_user(data={"phone": phone})
        if not user_data:
            user_data = accounts.internal_create_user(data={"phone": phone})
        user = User.objects.filter(external_key=user_data.get("pk")).first()
        if not user:
            user = User.objects.create_user(
                external_key=user_data.get("pk"),
                email=user_data.get("email"),
                phone=user_data.get("phone"),
                first_name=user_data.get("first_name"),
                last_name=user_data.get("last_name"),
                role=RenRole.objects.get(role=UserRoleChoices.BENEFICIARY),
            )

        task.beneficiary.user = user
        task.beneficiary.save(update_fields=["user"])
        return user

    @staticmethod
    def share_offer_with_beneficiary(phone: str, task: Task) -> None:
        data = {
            "to": phone,
            "message": render_to_string(
                template_name="beneficiary_message.txt",
                context={
                    "beneficiary_name": task.beneficiary.name,
                    "negotiator_name": f"{task.negotiator.first_name} {task.negotiator.last_name}",
                    "link": os.getenv("BASE_URL"),
                },
            ),
        }
        accounts.internal_send_sms(data=data)

    def create_new_request(self, task: Task, offers: list, user: User) -> Request:
        request = task.requests.order_by("id").last()
        data = {
            "task": task.id,
            "status": RequestStatus.SENT,
            "expired": timezone.now() + timedelta(hours=48),
        }
        serializer = CreateRequestSerializer(
            data=data,
            instance=request
            if request and request.status == RequestStatus.PENDING
            else None,
            partial=True,
        )
        if not serializer.is_valid():
            raise BadRequest(reason=serializer.errors)
        request = serializer.save()
        self.audit_for_object_creation(
            content_object=request,
            user=user,
            data={
                "status": {request.status: request.get_status_display()},
                "offers": {offer["id"]: offer for offer in offers},
            },
        )
        return request

    @staticmethod
    def check_request_offers(request: Request, offers: list) -> Request:
        makan_offers_ids: list[str] = list()
        offers_lookup: dict[str, dict] = dict()
        for makan_offer in offers:
            makan_offers_ids.append(makan_offer["id"])
            offers_lookup[str(makan_offer["id"])] = makan_offer

        internal_offers_map = {
            str(offer.external_offer_id): offer
            for offer in Offer.objects.filter(external_offer_id__in=makan_offers_ids)
        }
        external_offers_map = {
            str(offer.external_offer_id): offer
            for offer in ExternalOffer.objects.filter(
                external_offer_id__in=makan_offers_ids
            )
        }

        existed_offers_ids_in_db = set(internal_offers_map.keys()).union(
            set(external_offers_map.keys())
        )
        missed_offer_ids_from_db = set(offers_lookup.keys()).difference(
            existed_offers_ids_in_db
        )

        new_external_offers = [
            ExternalOffer(external_offer_id=offer_id, data=offers_lookup[offer_id])
            for offer_id in missed_offer_ids_from_db
        ]

        if new_external_offers:
            ExternalOffer.objects.bulk_create(new_external_offers)
            new_added_external_offers_map = {
                str(offer.external_offer_id): offer for offer in new_external_offers
            }
            # Update external_offers_map with newly created offers
            external_offers_map.update(new_added_external_offers_map)

        updated_offer_requests = []
        for offer_id_str, offer_data in offers_lookup.items():
            internal_offer = internal_offers_map.get(offer_id_str)
            external_offer = external_offers_map.get(offer_id_str)
            kwargs = {"request": request}
            if internal_offer:
                kwargs["internal_offer"] = internal_offer
            elif external_offer:
                kwargs["external_offer"] = external_offer
            else:
                continue
            offer_request, created = OfferRequest.objects.get_or_create(**kwargs)
            offer_request.status = OfferRequestStatus.INCLUDED
            offer_request.data = offer_data
            updated_offer_requests.append(offer_request)

        if updated_offer_requests:
            OfferRequest.objects.bulk_update(updated_offer_requests, ["status", "data"])

        return request

    def create_favorite_offer(self, user, context: dict, offer_data: dict):
        raise NotImplementedError()

    def delete_favorite_offer(self, user, context: dict):
        raise NotImplementedError()
