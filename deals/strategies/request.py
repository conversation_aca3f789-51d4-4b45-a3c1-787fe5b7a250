from abc import ABC, abstractmethod
from datetime import timed<PERSON><PERSON>

from django.contrib.auth import get_user_model
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from gabbro.graphene import BadRequest

from auditing.mixins import AuditMixin
from deals.models import Request, RequestStatus, OfferRequestStatus, OfferRequest
from deals.serializers import CreateRequestSerializer, UpdateRequestSerializer
from formschemas.mixins import FormSchemaMixin
from utils.general_utils import create_notification

User = get_user_model()


class RequestStrategy(ABC, FormSchemaMixin, AuditMixin):
    @abstractmethod
    def create_request(self, user, context: dict, request_input: dict):
        pass

    @abstractmethod
    def activate_request(self, user, context: dict, request_input: dict):
        pass


class CreateRequestStrategy(RequestStrategy, FormSchemaMixin, AuditMixin):
    def create_request(self, user, context: dict, request_input: dict) -> Request:
        request = context["request"]
        data = {
            "task": context["task"].id,
            "data": request.data if request else {},
            "status": RequestStatus.PENDING,
        }
        serializer = CreateRequestSerializer(data=data)
        if not serializer.is_valid():
            raise BadRequest(reason=serializer.errors)
        if request and request.status == RequestStatus.SENT:
            request.status = RequestStatus.REJECTED
            self.update_request_offers(request=request, user=user)
            request.save(update_fields=["status"])
        request: Request = serializer.save()
        notifications = [
            {
                "target": request.task,
                "level": "warning",
                "recipient": context["task"].negotiator,
                "actor": user,
                "title": _("Beneficiary Rejected Offer/s") % {},
                "description": _(
                    "المستفيد %(beneficiary_name)s طلب عروض جديدة للمهمة رقم %(task_id)s."
                )
                % {
                    "beneficiary_name": request.task.beneficiary.name,
                    "task_id": request.task.id,
                },
            }
        ]
        create_notification(notifications=notifications)
        self.audit_for_object_creation(
            content_object=request,
            user=user,
            data={
                "task": request.task.id,
                "offers": {},
                "status": {request.status: request.get_status_display()},
            },
        )
        return request

    def update_request_offers(self, request: Request, user: User):
        request_offers: [OfferRequest] = request.offer_requests.all()
        for request_offer in request_offers:
            request_offer.status = OfferRequestStatus.EXCLUDED
            self.audit_for_object_updating(
                content_object=request_offer,
                user=user,
                data={
                    "data": request_offer.data,
                    "status": {
                        str(OfferRequestStatus.EXCLUDED.value): str(
                            OfferRequestStatus.EXCLUDED.label
                        )
                    },
                },
            )
        OfferRequest.objects.bulk_update(request_offers, ["status"])

    def activate_request(self, user, context: dict, request_input: dict):
        raise NotImplementedError()


class ActivateRequestStrategy(RequestStrategy, FormSchemaMixin, AuditMixin):
    def create_request(self, user, context: dict, request_input: dict):
        raise NotImplementedError()

    def activate_request(self, user, context: dict, request_input: dict) -> Request:
        request = context["request"]
        data = {
            "task": context["task"].id,
            "status": RequestStatus.SENT,
            "expired": timezone.now() + timedelta(hours=48),
        }
        serializer = UpdateRequestSerializer(data=data, instance=request, partial=True)
        if not serializer.is_valid():
            raise BadRequest(reason=serializer.errors)
        request: Request = serializer.save()
        self.audit_for_object_updating(
            content_object=request,
            user=user,
            data={
                "task_id": request.task.id,
                "data": request.data,
                "status": request.status,
            },
        )
        return request
