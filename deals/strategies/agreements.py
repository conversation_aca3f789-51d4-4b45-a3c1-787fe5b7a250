from abc import ABC, abstractmethod

from django.core.exceptions import ValidationError
from django.utils.translation import ugettext_lazy as _
from gabbro.graphene import BadRequest

from auditing.mixins import AuditMixin
from deals.models import DealStatus, Agreement
from deals.serializers import (
    FinishAgreementSerializer,
    UpdateAgreementSerializer,
)
from formschemas.mixins import FormSchemaMixin
from orders.models import Task, TaskStatus, TaskAssignedStatus
from users.models import User
from utils.general_utils import form_schema_validate


class AgreementStrategy(ABC):
    @abstractmethod
    def update_agreement(
        self, context: dict, agreement_input: dict, user: User
    ) -> Agreement:
        pass

    @abstractmethod
    def create_agreement(
        self, context: dict, agreement_input: dict, user: User
    ) -> Agreement:
        pass


class CreateAgreementStrategy(
    AgreementStrategy,
    AuditMixin,
):
    def update_agreement(
        self, context: dict, agreement_input: dict, user: User
    ) -> Agreement:
        raise NotImplementedError()

    def create_agreement(
        self, context: dict, agreement_input: dict, user: User
    ) -> Agreement:
        task = context["task"]
        serializer_data = {
            "task": task.id,
            "status": DealStatus.IN_PROGRESS,
        }
        agreement_serializer = UpdateAgreementSerializer(
            data=serializer_data,
        )
        if not agreement_serializer.is_valid():
            raise BadRequest(reason=agreement_serializer.errors)
        agreement = agreement_serializer.save()
        task.status = TaskStatus.CREATE_DEAL
        # delete drafts from task form_data if exists
        task.form_data.pop("drafts", None)
        task.save(update_fields=["form_data", "status"])
        self.audit_for_object_creation(
            content_object=agreement,
            user=user,
            data={
                "form_data": agreement.form_data,
                "status": {agreement.status: agreement.get_status_display()},
            },
        )
        return agreement


class UpdateAgreementStrategy(AgreementStrategy, AuditMixin, FormSchemaMixin):
    def create_agreement(
        self, context: dict, agreement_input: dict, user: User
    ) -> Agreement:
        raise NotImplementedError()

    def update_agreement(
        self, context: dict, agreement_input: dict, user: User
    ) -> Agreement:
        task = context["task"]
        agreement = context.get("agreement")
        form_schema = context["form_schema"]
        form_data = self.get_valid_form_data(
            form_schema=form_schema,
            input_data=agreement_input,
            origin_form_data=agreement.form_data
            if agreement and agreement.form_data
            else dict(),
        )
        serializer_data = {
            "id": agreement.id,
            "task": task.id,
            "status": DealStatus.IN_PROGRESS,
            "form_data": form_data,
        }
        agreement_serializer = UpdateAgreementSerializer(
            partial=True,
            instance=agreement,
            data=serializer_data,
        )
        if not agreement_serializer.is_valid():
            raise BadRequest(reason=agreement_serializer.errors)
        agreement = agreement_serializer.save()
        self.audit_for_object_updating(
            content_object=agreement,
            user=user,
            data={
                "form_data": agreement.form_data,
                "status": {agreement.status: agreement.get_status_display()},
            },
        )
        return agreement


class FinishAgreementStrategy(AgreementStrategy, AuditMixin):
    def create_agreement(
        self, context: dict, agreement_input: dict, user: User
    ) -> Agreement:
        raise NotImplementedError()

    def update_agreement(
        self, context: dict, agreement_input: dict, user: User
    ) -> Agreement:
        form_schema = context["form_schema"]
        agreement = context["agreement"]
        task = context["task"]
        self._validate_form_data(form_schema=form_schema, form_data=agreement.form_data)
        deal = self._update_agreement_process(agreement=agreement, user=user, task=task)
        return deal

    def _update_agreement_process(self, agreement: Agreement, user: User, task: Task):
        agreement = self._update_agreement_status(agreement=agreement)
        self._update_task_status(task=task, user=user)
        self.audit_for_object_updating(
            content_object=agreement,
            user=user,
            data={
                "form_data": agreement.form_data,
                "status": {agreement.status: agreement.get_status_display()},
            },
        )
        return agreement

    @staticmethod
    def _validate_form_data(form_data, form_schema):
        if not form_data:
            raise BadRequest(
                reason={"form_data": _("Fulfill Form Data of Deal first") % {}}
            )
        if form_data.pop("drafts", None):
            raise BadRequest(
                {"form_data": _("remove drafts first before finish deal") % {}}
            )
        try:
            form_schema_validate(
                form_schema=form_schema.json_schema.get("form", dict()),
                form_data=form_data,
            )
        except ValidationError as e:
            raise BadRequest(reason=e.args[0])
        return form_data

    @staticmethod
    def _update_agreement_status(agreement: Agreement) -> Agreement:
        serializer = FinishAgreementSerializer(
            instance=agreement,
            data={"status": DealStatus.DEAL_FINISHED},
            partial=True,
        )
        if not serializer.is_valid():
            raise BadRequest(reason=serializer.errors)
        agreement = serializer.save()
        return agreement

    def _update_task_status(self, task: Task, user: User):
        task.status = TaskStatus.FINISHED
        task.assigned_status = TaskAssignedStatus.NOT_ASSIGNED
        task.save(update_fields=["status", "assigned_status"])
        self.audit_for_object_updating(
            content_object=task,
            user=user,
            data={
                "form_data": task.form_data,
                "status": {task.status: task.get_status_display()},
                "customer_service_id": task.customer_service.id,
                "negotiator_id": task.negotiator.id,
            },
        )
