import os
from abc import ABC, abstractmethod
from datetime import datetime

from django.conf import settings
from django.contrib.auth import get_user_model
from gabbro.graphene import BadRequest
from gabbro.uploads.models import Upload

from auditing.mixins import AuditMixin
from deals.models import Beneficiary, BeneficiaryDataSource
from deals.scripts.upload_bulk_benefeciary import (
    UploadBulkBeneficiary as UploadBulkBeneficiaryScript,
)
from deals.serializers import CreateBeneficiarySerializer
from formschemas.mixins import FormSchemaMixin
from resource_management.models import (
    ResourceManagementStatus,
    ResourceManagementActionChoices,
    BENEFICIARY_FILE_PATH,
)
from resource_management.serializers.resource_management import (
    CreateResourceManagementSerializer,
)

MEDIA_ROOT = getattr(settings, "MEDIA_ROOT", None)

User = get_user_model()


class BeneficiariesStrategy(ABC, FormSchemaMixin, AuditMixin):
    @abstractmethod
    def create_beneficiary(self, user, context: dict, beneficiaries_input: dict):
        pass

    @abstractmethod
    def create_bulk_beneficiary(self, user, context: dict, beneficiaries_input: dict):
        pass


class CreateBeneficiariesStrategy(BeneficiariesStrategy):
    def create_beneficiary(
        self, user, context: dict, beneficiaries_input: dict
    ) -> Beneficiary:
        form_schema = context["form_schema"]
        form_data = self.get_valid_form_data(
            form_schema=form_schema,
            input_data=beneficiaries_input,
            origin_form_data=dict(),
        )
        form_data = self.reformat_beneficiary_data(form_data=form_data)
        destination = form_data["destination"]
        serializer_data = {
            "external_id": None,
            "code": None,
            "data": form_data,
            "name": form_data.get("personalData", {}).get("name"),
            "data_source": BeneficiaryDataSource.INTERNAL
            if destination == "صندوق التنمية"
            else BeneficiaryDataSource.EXTERNAL,
        }
        serializer = CreateBeneficiarySerializer(data=serializer_data)
        if not serializer.is_valid():
            raise BadRequest(reason=serializer.errors)
        beneficiary = serializer.save()

        beneficiary.code = (
            f"EXBN_00_{beneficiary.id}"
            if beneficiary.data_source == BeneficiaryDataSource.EXTERNAL
            else beneficiary.code
        )
        beneficiary.external_id = (
            beneficiary.id
            if beneficiary.data_source == BeneficiaryDataSource.EXTERNAL
            else beneficiary.external_id
        )
        beneficiary.save()
        return beneficiary

    @staticmethod
    def reformat_beneficiary_data(form_data: dict) -> dict:
        form_data["ID_CODE"] = None
        form_data["رقم التقنيات"] = None
        form_data["request"] = {"registrationDate": datetime.now().strftime("%Y-%m-%d")}
        return form_data

    def create_bulk_beneficiary(self, user, context: dict, user_input: dict):
        raise NotImplementedError()


class UploadBulkBeneficiariesStrategy(BeneficiariesStrategy):
    def create_bulk_beneficiary(
        self, user, context: dict, user_input: dict
    ) -> (bool, dict, str):
        file = context["file"]
        title = user_input["name"]
        resource_management = CreateResourceManagementSerializer(
            data={
                "name": title,
                "created_by": user.id,
                "choices": ResourceManagementActionChoices.CREATE_BENEFICIARY,
                "data": dict(),
                "status": ResourceManagementStatus.PENDING.value,
                "action": ResourceManagementActionChoices.CREATE_BENEFICIARY.value,
            }
        )
        if not resource_management.is_valid():
            raise BadRequest(reason=resource_management.errors)
        resource_management = resource_management.save()
        save_to = os.path.join(MEDIA_ROOT, BENEFICIARY_FILE_PATH, file.name)
        Upload.objects.create(dataset=save_to, content_object=resource_management)

        upload_beneficiary_script = UploadBulkBeneficiaryScript(metadata={"file": file})

        (
            result,
            file_messages,
            user_message,
            errors_file,
        ) = upload_beneficiary_script.execute()
        if result is True:
            resource_management_status = ResourceManagementStatus.EXECUTED.value
        else:
            resource_management_status = ResourceManagementStatus.FAILED.value

        resource_management.errors_file = os.path.join(errors_file)
        resource_management.data = file_messages
        resource_management.status = resource_management_status
        resource_management.save()

        self.audit_for_object_creation(
            content_object=resource_management,
            user=user,
            data={
                "status": {
                    "key": resource_management.status,
                    "dispaly": resource_management.get_status_display(),
                },
                "data": resource_management.data,
            },
        )
        return resource_management

    def create_beneficiary(self, user, context: dict, beneficiaries_input: dict):
        raise NotImplementedError()
