from abc import ABC, abstractmethod

from django.core.exceptions import ValidationError
from django.utils.translation import ugettext_lazy as _
from gabbro.graphene import BadRequest

from auditing.mixins import AuditMixin
from deals.models import (
    <PERSON>,
    Offer,
    FavoriteOffer,
    DealStatus,
    Request<PERSON>tatus,
    Request,
    OfferStatus,
    ExternalOffer,
)
from deals.serializers import (
    CreateDealSerializer,
    UpdateDealSerializer,
    FinishDealSerializer,
    RejectDealSerializer,
    UpdateRequestSerializer,
    UpdateFavoriteOfferSerializer,
    CreateOfferSerializer,
    UpdateExternalOfferSerializer,
)
from formschemas.mixins import FormSchemaMixin
from orders.mixins import TaskMixin
from orders.models import Task, TaskStatus, TaskAssignedStatus
from users.models import User
from utils.general_utils import form_schema_validate, create_notification
from utils.offers import OfferClient


class DealStrategy(ABC):
    @abstractmethod
    def create_deal(self, context: dict, deal_input: dict, user: User) -> Deal:
        """Method that all deal strategies must implement"""
        pass

    @abstractmethod
    def update_deal(self, context: dict, deal_input: dict, user: User) -> Deal:
        pass


class CreateNewDealStrategy(DealStrategy, AuditMixin, TaskMixin):
    def create_deal(self, context: dict, deal_input: dict, user: User) -> Deal:
        task = context["task"]
        favorite_offer = context["favorite_offer"]
        deal = self.create_deal_from_favorite_offer(
            favorite_offer=favorite_offer, task=task
        )
        task.status = TaskStatus.CREATE_DEAL
        task.save(update_fields=["status"])
        self.audit_for_object_creation(
            content_object=deal,
            user=user,
            data={
                "form_data": deal.form_data,
                "status": {deal.status: deal.get_status_display()},
            },
        )
        return deal

    def create_deal_from_favorite_offer(
        self, favorite_offer: FavoriteOffer, task: Task
    ):
        deal_serializer = CreateDealSerializer(
            data={"favorite_offer": favorite_offer.id}
        )
        if not deal_serializer.is_valid():
            raise BadRequest(reason=deal_serializer.errors)
        deal = deal_serializer.save()
        # delete drafts from task form_data if exists
        task.form_data.pop("drafts", None)
        # update task call logs
        self.update_call_logs(
            task=task,
            user=task.negotiator,
            level="negotiator",
            current_form_data=task.form_data.get("negotiator", {}).get(
                "negotiatorData", {}
            ),
            old_form_data={},
            check_time=False,
        )
        task.save(update_fields=["form_data", "meta_data"])
        self._update_offer_if_exists(deal=deal)
        return deal

    @staticmethod
    def _update_offer_if_exists(deal: Deal):
        deal_offer_id = deal.favorite_offer.offer_id
        internal_offer = Offer.objects.filter(external_offer_id=deal_offer_id).first()
        external_offer = (
            ExternalOffer.objects.filter(external_offer_id=deal_offer_id)
            .only("status", "external_offer_id")
            .first()
        )

        if internal_offer:
            offer_data = {"status": OfferStatus.RESERVED}
            serializer = CreateOfferSerializer(
                data=offer_data, instance=internal_offer, partial=True
            )
            if not serializer.is_valid():
                raise BadRequest(reason=serializer.errors)
            serializer.save()

        elif external_offer:
            offer_data = {"status": OfferStatus.RESERVED}
            serializer = UpdateExternalOfferSerializer(
                data=offer_data, instance=external_offer, partial=True
            )
            if not serializer.is_valid():
                raise BadRequest(reason=serializer.errors)
            serializer.save()

    def update_deal(self, context: dict, deal_input: dict, user: User) -> Deal:
        raise NotImplementedError()


class UpdateDealStrategy(DealStrategy, FormSchemaMixin, AuditMixin):
    def update_deal(self, context: dict, deal_input: dict, user: User) -> Deal:
        deal = context["deal"]
        form_schema = context["form_schema"]
        form_data = self.get_valid_form_data(
            form_schema=form_schema,
            input_data=deal_input,
            origin_form_data=deal.form_data or dict(),
        )
        # Update the deal's status and form data
        deal = self._update_deal_status_and_data(
            deal=deal, task_status=deal_input.get("task_status"), form_data=form_data
        )
        self.audit_for_object_updating(
            content_object=deal,
            user=user,
            data={
                "form_data": deal.form_data,
                "status": {deal.status: deal.get_status_display()},
            },
        )
        return deal

    @staticmethod
    def _update_deal_status_and_data(
        deal: Deal,
        form_data: dict,
        task_status: int = None,
    ) -> Deal:
        """
        Updates the status and form data of the deal using a serializer.

        Args:
            deal (Deal): The deal being updated.
            form_data (dict): The validated form data.

        Returns:
            Deal: The updated deal object.
        """
        data = {
            "status": DealStatus.IN_PROGRESS,
            "form_data": form_data,
            "task_status": task_status,
        }
        serializer = UpdateDealSerializer(instance=deal, partial=True, data=data)
        if not serializer.is_valid():
            raise BadRequest(reason=serializer.errors)
        deal = serializer.save()
        if serializer.validated_data.get("task_status") is not None:
            task = deal.favorite_offer.task
            task.status = task_status
            task.save(update_fields=["status"])
        return deal

    def create_deal(self, context: dict, deal_input: dict, user: User):
        raise NotImplementedError()


class ChoseDealFavoriteOfferStrategy(DealStrategy, AuditMixin):
    def update_deal(self, context: dict, deal_input: dict, user: User) -> FavoriteOffer:
        deal = context.get("deal")
        task = context["task"]
        request = context.get("request")
        favorite_offer = context.get("favorite_offer")
        if deal:
            self.update_current_deal(context, user=user)
        else:
            self.update_favorite_offer(task=task, favorite_offer=favorite_offer)
        self.update_request_status(
            request=request, user=user, favorite_offer=favorite_offer
        )
        notifications = [
            {
                "target": task,
                "level": "success",
                "recipient": context["task"].negotiator,
                "actor": user,
                "title": _("Beneficiary Selected an Offer") % {},
                "description": _(
                    "المستفيد %(beneficiary_name)s اختار العرض %(favorite_offer_id)s للمهمة رقم %(task_id)s."
                )
                % {
                    "beneficiary_name": task.beneficiary.name,
                    "favorite_offer_id": favorite_offer.id,
                    "task_id": task.id,
                },
            }
        ]
        create_notification(notifications=notifications)
        return favorite_offer

    def update_request_status(
        self, request: Request, user: User, favorite_offer: FavoriteOffer
    ) -> Request:
        data = {"status": RequestStatus.ACCEPTED}
        serializer = UpdateRequestSerializer(instance=request, data=data, partial=True)
        if not serializer.is_valid():
            raise BadRequest(reason=serializer.errors)
        request = serializer.save()
        self.audit_for_object_updating(
            content_object=request,
            data={
                "offers": {f"{favorite_offer.offer_id}": {}},
                "status": {request.status: request.get_status_display()},
            },
            user=user,
        )
        return request

    @staticmethod
    def update_favorite_offer(
        task: Task, favorite_offer: FavoriteOffer
    ) -> FavoriteOffer:
        data = {"reserved": True}
        serializer = UpdateFavoriteOfferSerializer(
            instance=favorite_offer, data=data, partial=True
        )
        if not serializer.is_valid():
            raise BadRequest(reason=serializer.errors)
        favorite_offers = task.favorite_offers.all()
        for offer in favorite_offers:
            offer.reserved = False
        task.favorite_offers.bulk_update(favorite_offers, fields=["reserved"])
        favorite_offer = serializer.save()
        return favorite_offer

    def update_current_deal(self, context: dict, user: User):
        deal = context["deal"]
        favorite_offer = context["favorite_offer"]
        data = {
            "favorite_offer": favorite_offer.id,
        }
        serializer = UpdateDealSerializer(instance=deal, data=data, partial=True)
        if not serializer.is_valid():
            raise BadRequest(reason=serializer.errors)
        old_favorite_offer = deal.favorite_offer
        deal = serializer.save()
        old_favorite_offer.reserved = False
        old_favorite_offer.save(update_fields=["reserved"])
        deal.favorite_offer.reserved = True
        deal.favorite_offer.save(update_fields=["reserved"])
        self.audit_for_object_updating(
            content_object=deal,
            user=user,
            data={
                "form_data": deal.form_data,
                "status": {deal.status: deal.get_status_display()},
                "favorite_offer_id": deal.favorite_offer.id,
            },
        )
        return deal

    def create_deal(self, context: dict, deal_input: dict, user: User) -> Deal:
        raise NotImplementedError()


class FinishDealStrategy(DealStrategy, AuditMixin):
    def update_deal(self, context: dict, deal_input: dict, user: User) -> Deal:
        form_schema = context["form_schema"]
        deal = context["deal"]
        self._validate_form_data(form_schema=form_schema, form_data=deal.form_data)
        deal = self._update_deal_process(deal=deal, user=user)
        self._update_offer_if_exists(deal)
        return deal

    def _update_deal_process(self, deal: Deal, user: User):
        deal = self._update_deal_status(deal=deal)
        task = deal.favorite_offer.task
        self._update_task_status(task=deal.favorite_offer.task)
        self.audit_for_object_updating(
            content_object=task,
            user=user,
            data={
                "form_data": task.form_data,
                "status": {task.status: task.get_status_display()},
                "customer_service_id": task.customer_service.id,
                "negotiator_id": task.negotiator.id,
            },
        )
        self.audit_for_object_updating(
            content_object=deal,
            user=user,
            data={
                "form_data": deal.form_data,
                "status": {deal.status: deal.get_status_display()},
            },
        )
        return deal

    @staticmethod
    def _validate_form_data(form_data, form_schema):
        if not form_data:
            raise BadRequest(
                reason={"form_data": _("Fulfill Form Data of Deal first") % {}}
            )
        if form_data.pop("drafts", None):
            raise BadRequest(
                {"form_data": _("remove drafts first before finish deal") % {}}
            )
        try:
            form_schema_validate(
                form_schema=form_schema.json_schema.get("form", dict()),
                form_data=form_data,
            )
        except ValidationError as e:
            raise BadRequest(reason=e.args[0])
        return form_data

    @staticmethod
    def _update_deal_status(deal: Deal) -> Deal:
        serializer = FinishDealSerializer(
            instance=deal,
            data={"status": DealStatus.DEAL_FINISHED, "form_data": deal.form_data},
            partial=True,
        )
        if not serializer.is_valid():
            raise BadRequest(reason=serializer.errors)
        deal = serializer.save()
        return deal

    @staticmethod
    def _update_task_status(task: Task):
        task.status = TaskStatus.FINISHED
        task.assigned_status = TaskAssignedStatus.NOT_ASSIGNED
        task.save(update_fields=["status", "assigned_status"])

    @staticmethod
    def _update_offer_if_exists(deal: Deal):
        deal_offer_id = deal.favorite_offer.offer_id
        internal_offer = Offer.objects.filter(external_offer_id=deal_offer_id).first()
        if internal_offer:
            data = {
                "geometry": internal_offer.geometry.geojson,
                "id": internal_offer.external_offer_id,
                "map_data": {"is_sold": True},
                "data": internal_offer.data,
            }
            OfferClient().update_vd_offer(data=data)

    def create_deal(self, context: dict, deal_input: dict, user: User):
        raise NotImplementedError()


class RejectDealStrategy(FinishDealStrategy, AuditMixin):
    def update_deal(self, context: dict, deal_input: dict, user: User) -> Deal:
        deal = context["deal"]
        deal = self._update_deal_process(deal=deal, user=user)
        return deal

    @staticmethod
    def _update_deal_status(deal: Deal):
        serializer = RejectDealSerializer(
            instance=deal, data={"status": DealStatus.DEAL_CANCELLED}, partial=True
        )
        if not serializer.is_valid():
            raise BadRequest(reson=serializer.errors)
        return serializer.save()

    @staticmethod
    def _update_task_status(task: Task):
        task.status = TaskStatus.REJECTED
        task.assigned_status = TaskAssignedStatus.NOT_ASSIGNED
        task.save(update_fields=["status", "assigned_status"])
