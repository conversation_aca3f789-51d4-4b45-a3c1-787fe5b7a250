from common.permissions import PermissionsInterface
from common.validators import InputValidation
from deals.strategies import OfferStrategy, FavoriteOfferStrategy
from users.models import User


class OfferService:
    def __init__(
        self,
        strategy: OfferStrategy,
        perms: PermissionsInterface,
        validations: InputValidation,
    ):
        self.strategy = strategy
        self.perms = perms
        self.validations = validations

    def create_offer(self, offer_data: dict, user: User):
        context = self.validations.get_object_if_exists(user, offer_data)
        self.perms.check_permissions(user=user, context=context)
        return self.strategy.create_offer(
            context=context, user=user, offer_data=offer_data
        )

    def update_offer(self, offer_data: dict, user: User):
        context = self.validations.get_object_if_exists(user, offer_data)
        self.perms.check_permissions(user=user, context=context)
        return self.strategy.update_offer(
            context=context, user=user, offer_data=offer_data
        )

    def fetch_offers(self, offer_data: dict, user: User):
        context = self.validations.get_object_if_exists(user, offer_data)
        self.perms.check_permissions(user=user, context=context)
        return self.strategy.fetch_offers(
            context=context, user=user, offer_data=offer_data
        )


class FavoriteOfferService:
    def __init__(
        self,
        strategy: FavoriteOfferStrategy,
        perms: PermissionsInterface,
        validations: InputValidation,
    ):
        self.strategy = strategy
        self.perms = perms
        self.validations = validations

    def create_favorite_offer(self, offer_data: dict, user: User):
        context = self.validations.get_object_if_exists(user, offer_data)
        self.perms.check_permissions(user=user, context=context)
        return self.strategy.create_favorite_offer(
            context=context, user=user, offer_data=offer_data
        )

    def delete_favorite_offer(self, favorite_offer_id: int, user: User):
        context = self.validations.get_object_if_exists(user, favorite_offer_id)
        self.perms.check_permissions(user=user, context=context)
        return self.strategy.delete_favorite_offer(
            context=context,
            user=user,
        )

    def share_favorite_offers(self, user: User, offer_input: dict):
        context = self.validations.get_object_if_exists(user, offer_input)
        self.perms.check_permissions(user=user, context=context)
        return self.strategy.share_favorite_offers(
            context=context,
            user=user,
        )
