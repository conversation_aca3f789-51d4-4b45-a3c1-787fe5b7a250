from common.permissions import PermissionsInterface
from common.validators import InputValidation
from deals.strategies.beneficiaries import CreateBeneficiariesStrategy
from formschemas.mixins import FormSchemaMixin


class BeneficiaryService(FormSchemaMixin):
    def __init__(
        self,
        strategy: CreateBeneficiariesStrategy,
        perms: PermissionsInterface,
        validations: InputValidation,
    ):
        self.strategy = strategy
        self.validations = validations
        self.perms = perms

    def create_beneficiary(self, beneficiary_input: dict, user):
        context = self.validations.get_object_if_exists(
            beneficiary_input=beneficiary_input, user=user
        )
        self.perms.check_permissions(user=user, context=context)
        return self.strategy.create_beneficiary(
            context=context, beneficiaries_input=beneficiary_input, user=user
        )

    def create_bulk_beneficiary(self, user_input: dict, user):
        context = self.validations.get_object_if_exists(
            user_input=user_input, user=user
        )
        self.perms.check_permissions(user=user, context=context)
        return self.strategy.create_bulk_beneficiary(
            context=context, user_input=user_input, user=user
        )
