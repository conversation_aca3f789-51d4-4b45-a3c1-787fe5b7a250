from common.permissions import PermissionsInterface
from common.validators import InputValidation
from deals.strategies.deals import DealStrategy
from formschemas.mixins import FormSchemaMixin


class DealService(FormSchemaMixin):
    def __init__(
        self,
        strategy: DealStrategy,
        perms: PermissionsInterface,
        validations: InputValidation,
    ):
        self.strategy = strategy
        self.validations = validations
        self.perms = perms

    def create_deal(self, deal_input: dict, user):
        context = self.validations.get_object_if_exists(
            deal_input=deal_input, user=user
        )
        self.perms.check_permissions(user=user, context=context)
        return self.strategy.create_deal(
            context=context, deal_input=deal_input, user=user
        )

    def update_deal(self, user, deal_input):
        context = self.validations.get_object_if_exists(
            deal_input=deal_input, user=user
        )
        self.perms.check_permissions(user=user, context=context)
        return self.strategy.update_deal(
            context=context, deal_input=deal_input, user=user
        )
