from common.permissions import PermissionsInterface
from common.validators import InputValidation
from deals.strategies.agreements import AgreementStrategy
from formschemas.mixins import FormSchemaMixin


class AgreementService(FormSchemaMixin):
    def __init__(
        self,
        strategy: AgreementStrategy,
        perms: PermissionsInterface,
        validations: InputValidation,
    ):
        self.strategy = strategy
        self.validations = validations
        self.perms = perms

    def update_agreement(self, agreement_input: dict, user):
        context = self.validations.get_object_if_exists(
            agreement_input=agreement_input, user=user
        )
        self.perms.check_permissions(user=user, context=context)
        return self.strategy.update_agreement(
            context=context, agreement_input=agreement_input, user=user
        )

    def create_agreement(self, agreement_input: dict, user):
        context = self.validations.get_object_if_exists(
            agreement_input=agreement_input, user=user
        )
        self.perms.check_permissions(user=user, context=context)
        return self.strategy.create_agreement(
            context=context, agreement_input=agreement_input, user=user
        )
