from common.permissions import PermissionsInterface
from common.validators import InputValidation
from deals.strategies import RequestStrategy
from formschemas.mixins import FormSchemaMixin


class RequestService(FormSchemaMixin):
    def __init__(
        self,
        strategy: RequestStrategy,
        perms: PermissionsInterface,
        validations: InputValidation,
    ):
        self.strategy = strategy
        self.validations = validations
        self.perms = perms

    def create_request(self, request_input: dict, user):
        context = self.validations.get_object_if_exists(user, request_input)
        self.perms.check_permissions(user=user, context=context)
        return self.strategy.create_request(
            context=context, request_input=request_input, user=user
        )

    def activate_request(self, request_input: dict, user):
        context = self.validations.get_object_if_exists(user, request_input)
        self.perms.check_permissions(user=user, context=context)
        return self.strategy.activate_request(
            context=context, request_input=request_input, user=user
        )
