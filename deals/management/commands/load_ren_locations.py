import logging
import time

from django.core.management.base import BaseCommand

from deals.models.layer import Layer
from deals.scripts.load_ren_locations import LoadRenLocations

logger = logging.getLogger("commands")

LAYERS_KEYS = {
    "amana": "amana",
    "city": "city",
    "districts": "districts",
    "region": "region",
    "zone": "zone",
}


class Command(BaseCommand):
    help = "load REN layers and records"
    layers = list(LAYERS_KEYS.values())

    def add_arguments(self, parser):
        parser.add_argument("--file_path", type=str, required=True)
        parser.add_argument("--patch_size", type=int, required=False)
        parser.add_argument(
            "--layer_key",
            type=str,
            choices=self.layers,
            help="Choose Layer from the available options",
        )

    def handle(self, *args, **options):
        layer_key = options["layer_key"]
        file_path = options.get("file_path")
        patch_size = options.get("patch_size", 1000)
        logger.debug(f"file_path: {file_path}")
        start = time.time()
        layer, created = Layer.objects.get_or_create(key=layer_key)
        logger.debug(f"Start loading {layer}")
        logger.debug("-" * 70)
        LoadRenLocations(
            metadata={"layer": layer, "file": file_path, "patch_size": patch_size}
        ).execute()
        end = time.time()
        logger.debug(f"All Layers Loaded in {end - start} seconds")
