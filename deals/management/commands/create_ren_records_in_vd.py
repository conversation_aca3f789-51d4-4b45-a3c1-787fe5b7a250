import json
import logging

from django.core.management.base import BaseCommand
from django.db.models import Q

from deals.models import Offer
from utils.offers import OfferClient

logger = logging.getLogger("commands")


class Command(BaseCommand):
    help = "Create Ren Records in VD"

    def handle(self, *args, **options):
        offers = Offer.objects.filter(
            ~Q(data__completingOffer=[]) & ~Q(data__completingOffer={}),
            external_offer_id__isnull=True,
            data__isnull=False,
            record__geometry__isnull=False,
            data__completingOffer__isnull=False,
        ).distinct()
        logger.debug(
            f"create_records_in_vd: ren_offers_count: {offers.count()}, ids: {list(offers.values_list('id', flat=True))}"
        )
        for offer in offers:
            data = {
                "geometry": json.loads(offer.record.geometry.geojson),
                "data": offer.data,
            }
            offer_data = OfferClient().create_vd_offer(data=data)
            offer.external_offer_id = offer_data.get("id")
            offer.data["completingOffer"]["offerData"]["id"] = str(offer_data.get("id"))
            offer.save(update_fields=["external_offer_id", "data"])
            logger.debug(
                f"create_records_in_vd: offer_id: {offer.id} external_offer_id: {offer.external_offer_id}"
            )
