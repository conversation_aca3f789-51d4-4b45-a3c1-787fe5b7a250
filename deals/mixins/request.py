from django.utils.translation import gettext_lazy as _
from gabbro.graphene import BadR<PERSON>quest

from deals.models import Request, RequestStatus
from orders.models import Task


class RequestMixin:
    @staticmethod
    def get_beneficiary_request(task: Task) -> Request:
        request = task.requests.all().order_by("id").last()
        if not request or request and request.status != RequestStatus.SENT:
            raise BadRequest(reason={"task": _("you dont have pending request") % {}})
        if request.is_expired:
            raise BadRequest(
                reason={
                    "task": _(
                        "task %(task_id)s already has expired request, please reactivate the link"
                    )
                    % {"task_id": task.id}
                }
            )
        return request
