from django.contrib import admin
from django.utils.translation import ugettext_lazy as _

from deals.models import Offer, FavoriteOffer, ExternalOffer


class OfferAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "external_offer_id",
        "status",
    )
    search_fields = ("id", "license_number")
    list_filter = ("status",)

    def has_delete_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request, obj=None):
        return False

    def has_change_permission(self, request, obj=None):
        return False


class FavoriteOfferAdmin(admin.ModelAdmin):

    list_display = (
        "id",
        "offer_id",
        "task",
        "reservation_remaining_hours",
    )
    search_fields = ("id", "offer_id")

    def has_delete_permission(self, request, obj=None):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request, obj=None):
        return False

    @admin.display(description=_("Reservation Remaining Hours"))
    def reservation_remaining_hours(self, favorite_offer: FavoriteOffer):
        return favorite_offer.reservation_remaining_hours


class ExternalOfferAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "external_offer_id",
    )
    search_fields = ("id", "external_offer_id")

    def has_delete_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request, obj=None):
        return False

    def has_change_permission(self, request, obj=None):
        return False


admin.site.register(Offer, OfferAdmin)
admin.site.register(FavoriteOffer, FavoriteOfferAdmin)
admin.site.register(ExternalOffer, ExternalOfferAdmin)
