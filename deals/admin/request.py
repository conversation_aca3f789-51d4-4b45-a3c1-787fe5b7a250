from django.contrib import admin

from deals.models import Request, OfferRequest


class RequestAdmin(admin.ModelAdmin):
    list_display_links = ["id", "task"]
    list_display = ("id", "expired", "task", "status", "created", "modified")

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def has_change_permission(self, request, obj=None):
        return False


class OfferRequestAdmin(admin.ModelAdmin):
    list_display_links = ["id", "internal_offer"]
    list_display = (
        "id",
        "internal_offer",
        "external_offer",
        "request",
        "status",
        "created",
        "modified",
    )

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def has_change_permission(self, request, obj=None):
        return False


admin.site.register(Request, RequestAdmin)
admin.site.register(OfferRequest, OfferRequestAdmin)
