from django.contrib import admin

from deals.models import Beneficiary


class BeneficiaryAdmin(admin.ModelAdmin):
    list_display = ["id", "name"]
    search_fields = ["name", "code"]
    list_display_links = ["id", "name"]
    list_filter = ["tasks__status", "tasks__assigned_status"]

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def has_change_permission(self, request, obj=None):
        return False


admin.site.register(Beneficiary, BeneficiaryAdmin)
