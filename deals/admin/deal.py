from django.contrib import admin
from deals.models import Deal


class DealAdmin(admin.ModelAdmin):
    list_display = ("id", "favorite_offer", "status", "created", "modified")
    search_fields = ("id", "favorite_offer__license_number")
    list_filter = ("status",)

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def has_change_permission(self, request, obj=None):
        return False


admin.site.register(Deal, DealAdmin)
