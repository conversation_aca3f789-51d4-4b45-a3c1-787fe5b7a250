from django.contrib import admin
from deals.models import Agreement


class AgreementAdmin(admin.ModelAdmin):
    list_display = ("id", "task", "status", "created", "modified")
    search_fields = ("id", "task_id")
    list_filter = ("status",)
    list_display_links = ["id", "task"]

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def has_change_permission(self, request, obj=None):
        return False


admin.site.register(Agreement, AgreementAdmin)
