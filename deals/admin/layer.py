from django.contrib import admin

from deals.models import Layer


class LayerAdmin(admin.ModelAdmin):
    list_display = ("__str__", "id", "key")
    search_fields = ("id", "key", "title")

    def get_readonly_fields(self, request, obj=None):
        fields = super().get_readonly_fields(request, obj)
        if obj:
            return fields + ("key",)
        return fields


admin.site.register(Layer, LayerAdmin)
