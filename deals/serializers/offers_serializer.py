from rest_framework import serializers

from deals.models import Offer, FavoriteOffer, ExternalOffer


class CreateOfferSerializer(serializers.ModelSerializer):
    class Meta:
        model = Offer
        fields = ["status", "offer_type", "data"]


class CreateFavoriteOfferSerializer(serializers.ModelSerializer):
    class Meta:
        model = FavoriteOffer
        fields = ["task", "offer_id"]


class UpdateFavoriteOfferSerializer(serializers.ModelSerializer):
    class Meta:
        model = FavoriteOffer
        fields = [
            "reserved",
        ]


class UpdateExternalOfferSerializer(serializers.ModelSerializer):
    class Meta:
        model = ExternalOffer
        fields = ["status", "data"]
