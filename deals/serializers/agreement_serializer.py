from django.utils.translation import gettext_lazy as _
from rest_framework import serializers

from deals.models import DealStatus, Agreement


class UpdateAgreementSerializer(serializers.ModelSerializer):
    class Meta:
        model = Agreement
        fields = ["id", "form_data", "status", "task"]


class FinishAgreementSerializer(serializers.ModelSerializer):
    class Meta:
        model = Agreement
        fields = ["status"]

    def validate_status(self, value):
        if self.instance and self.instance.status in [
            DealStatus.DEAL_CANCELLED,
            DealStatus.DEAL_FINISHED,
        ]:
            raise serializers.ValidationError(
                _("Deal is %(status)s, can't be Finished")
                % {"status": self.instance.get_status_display()}
            )
        return value


class RejectAgreementSerializer(serializers.ModelSerializer):
    class Meta:
        model = Agreement
        fields = ["status"]

    def validate_status(self, value):
        if self.instance and self.instance.status in [
            DealStatus.DEAL_CANCELLED,
            DealStatus.DEAL_FINISHED,
        ]:
            raise serializers.ValidationError(
                _("Deal is %(status)s, can't be Canceled")
                % {"status": self.instance.get_status_display()}
            )
        return value
