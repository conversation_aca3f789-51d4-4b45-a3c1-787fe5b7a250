from django.utils.translation import gettext_lazy as _
from rest_framework import serializers

from deals.models import Deal, DealStatus
from orders.models import TaskStatus


class CreateDealSerializer(serializers.ModelSerializer):
    class Meta:
        model = Deal
        fields = ["id", "favorite_offer", "status", "form_data"]


class UpdateDealSerializer(serializers.ModelSerializer):
    task_status = serializers.ChoiceField(
        allow_null=True,
        choices=[
            (key, value)
            for key, value in TaskStatus.choices
            if key
            not in [
                TaskStatus.REJECTED.value,
                TaskStatus.FINISHED.value,
                TaskStatus.IN_PROGRESS.value,
                TaskStatus.PENDING_REVIEW.value,
                TaskStatus.CREATE_DEAL,
            ]
        ],
    )

    class Meta:
        model = Deal
        fields = ["id", "form_data", "status", "task_status", "favorite_offer"]


class FinishDealSerializer(serializers.ModelSerializer):
    class Meta:
        model = Deal
        fields = ["status", "form_data"]


class RejectDealSerializer(serializers.ModelSerializer):
    class Meta:
        model = Deal
        fields = ["status"]

    def validate_status(self, value):
        if self.instance and self.instance.status in [
            DealStatus.DEAL_CANCELLED,
            DealStatus.DEAL_FINISHED,
        ]:
            raise serializers.ValidationError(
                _("Deal is %(status)s, can't be Canceled")
                % {"status": self.instance.get_status_display()}
            )
        return value
