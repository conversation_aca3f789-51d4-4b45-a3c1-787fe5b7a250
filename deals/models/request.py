from functools import cached_property

from django.contrib.contenttypes.fields import GenericRelation
from django.contrib.gis.db import models
from django.utils import timezone
from django.utils.translation import ugettext_lazy as _
from django_extensions.db.models import TimeStampedModel
from jsoneditor.fields.django3_jsonfield import J<PERSON>NField


class RequestStatus(models.IntegerChoices):
    PENDING = 1, _("Pending")
    SENT = 2, _("Sent")
    ACCEPTED = 3, _("Accepted")
    REJECTED = 4, _("Rejected")


class OfferRequestStatus(models.TextChoices):
    INCLUDED = "INCLUDED", _("included in request")
    EXCLUDED = "EXCLUDED", _("excluded from request")


class Request(TimeStampedModel):
    task = models.ForeignKey(
        "orders.Task",
        on_delete=models.PROTECT,
        verbose_name=_("Task"),
        related_name="requests",
    )
    status = models.PositiveSmallIntegerField(
        choices=RequestStatus.choices,
        default=RequestStatus.PENDING,
        db_index=True,
        verbose_name=_("Record Status"),
    )
    expired = models.DateTimeField(
        verbose_name="Expired", auto_now_add=False, auto_now=False, null=True
    )
    data = JSONField(
        blank=True,
        null=True,
        verbose_name=_("Request Data"),
    )
    audits = GenericRelation("auditing.Audit", verbose_name=_("Audits"))

    class Meta:
        verbose_name = _("Request ")
        verbose_name_plural = _("Requests")

    def __str__(self):
        return f"pk:{self.id}, Task: {self.task}"

    @property
    def is_expired(self):
        return self.expired < timezone.now() if self.expired else False


class OfferRequest(TimeStampedModel):
    internal_offer = models.ForeignKey(
        "deals.Offer", on_delete=models.PROTECT, related_name="requests", null=True
    )
    external_offer = models.ForeignKey(
        "deals.ExternalOffer",
        on_delete=models.PROTECT,
        related_name="requests",
        null=True,
    )
    request = models.ForeignKey(
        "deals.Request", on_delete=models.CASCADE, related_name="offer_requests"
    )
    status = models.CharField(
        choices=OfferRequestStatus.choices,
        default=OfferRequestStatus.INCLUDED,
        db_index=True,
        verbose_name=_("Offer Status"),
        max_length=20,
    )
    data = JSONField(
        blank=True,
        null=True,
        verbose_name=_("Offer Request Data"),
    )

    class Meta:
        verbose_name = _("Offer Request")
        verbose_name_plural = _("Offer Requests")
        unique_together = [("internal_offer", "request"), ("external_offer", "request")]

    @cached_property
    def offer_id(self):
        if not any([self.internal_offer, self.external_offer]):
            return None
        return (
            str(self.external_offer.external_offer_id)
            if self.external_offer
            else str(self.internal_offer.external_offer_id)
        )
