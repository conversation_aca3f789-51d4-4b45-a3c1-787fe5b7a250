from datetime import timedelta

from django.conf import settings
from django.contrib.contenttypes.fields import GenericRelation
from django.contrib.gis.db import models
from django.utils import timezone
from django.utils.functional import cached_property
from django.utils.translation import ugettext_lazy as _
from django_extensions.db.models import TimeStampedModel
from jsoneditor.fields.django3_jsonfield import J<PERSON><PERSON>ield

from utils.general_utils import get_nested_value

OFFER_DATA_MAPPER = {
    "establishment_type": "realEstateDetails.establishmentType",
    "address": "location.nationalAddress",
    "offer_type": "offerData.offerType",
    "real_estate_price": "financialData.realEstatePrice",
    "coordinate": "location.coordinate",
    "land": {
        "area": "realEstateDetails.land.landArea",
        "images": "realEstateDetails.land.imageAttachment",
    },
    "buildingFacts": {
        "bedroom": "realEstateDetails.buildingFacts.BedroomCount",
        "bathroom": "realEstateDetails.buildingFacts.BathroomCount",
        "area": "realEstateDetails.buildingFacts.landFacts.landArea",
        "images": "realEstateDetails.buildingFacts.imageAttachment",
    },
    "shop": {
        "area": "realEstateDetails.shop.theArea",
        "images": "realEstateDetails.shop.imageAttachment",
        "bedroom": "realEstateDetails.shop.BedroomCount",
        "bathroom": "realEstateDetails.shop.BathroomCount",
    },
    "Department": {
        "area": "realEstateDetails.Department.theArea",
        "bedroom": "realEstateDetails.Department.BedroomCount",
        "bathroom": "realEstateDetails.Department.BathroomCount",
        "images": "realEstateDetails.Department.images",
    },
}
OFFER_TYPE_MAPPER = {
    "أرض": "land",
    "فيلا": "buildingFacts",
    "برج": "buildingFacts",
    "مزرعة": "buildingFacts",
    "عمارة": "buildingFacts",
    "استراحة": "buildingFacts",
    "محل": "shop",
    "معرض": "shop",
    "مستودع": "shop",
    "ورشة": "shop",
    "شقة": "Department",
    "دور": "Department",
    "ملحق": "Department",
    "غرفة": "Department",
    "فيلا دوبلكس": "Department",
    "مكتب": "Department",
}


class OfferType(models.IntegerChoices):
    INTERNAL = 1, _("Internal")
    EXTERNAL = 2, _("External")


class OfferStatus(models.IntegerChoices):
    RESERVED = 1, _("Reserved")
    NOT_RESERVED = 2, _("Not Reserved")
    RESERVED_FOR_TIME = 3, _("Reserved For Time")


class Offer(TimeStampedModel):
    external_offer_id = models.IntegerField(
        verbose_name=_("External Offer ID"),
        db_index=True,
        null=True,
        blank=True,
    )
    license_number = models.CharField(
        max_length=255, null=True, blank=True, verbose_name=_("License Number")
    )
    offer_type = models.PositiveSmallIntegerField(
        choices=OfferType.choices,
        default=OfferType.INTERNAL,
        db_index=True,
        verbose_name=_("Offer Type"),
    )
    status = models.PositiveSmallIntegerField(
        choices=OfferStatus.choices,
        default=OfferStatus.NOT_RESERVED,
        db_index=True,
        verbose_name=_("Status"),
    )
    record = models.OneToOneField(
        "deals.Record",
        on_delete=models.CASCADE,
        verbose_name="Record",
        related_name="offer",
        blank=True,
        null=True,
    )
    data = JSONField(blank=True, null=True, verbose_name=_("Offer Data"))
    audits = GenericRelation("auditing.Audit", verbose_name=_("Audits"))

    class Meta:
        verbose_name = _("REN Offer")
        verbose_name_plural = _("REN Offers")
        indexes = [
            models.Index(
                models.F("data__completingOffer__offerData__id"), name="offer_id"
            ),
        ]

    def __str__(self):
        return f"external_offer_id: {self.external_offer_id}"

    @cached_property
    def negotiators(self):
        negotiators = (
            FavoriteOffer.objects.filter(offer=self)
            .select_related("task__negotiator")
            .values(
                "task__negotiator__first_name",
                "task__negotiator__last_name",
            )
        ).distinct()
        return {"data": negotiators, "count": negotiators.count()}

    def extract_data(self, field):
        field: str = OFFER_DATA_MAPPER.get(field)
        if not field:
            return None
        data = self.data.get("drafts") or self.data
        field = get_nested_value(data=data.get("completingOffer", {}), path=field)
        return field

    @cached_property
    def geometry(self):
        return self.record.geometry if self.record else None


class ExternalOffer(TimeStampedModel):
    external_offer_id = models.PositiveBigIntegerField(
        verbose_name=_("External Offer ID"),
        db_index=True,
        unique=True,
    )
    status = models.PositiveSmallIntegerField(
        choices=OfferStatus.choices,
        default=OfferStatus.NOT_RESERVED,
        db_index=True,
        verbose_name=_("Status"),
    )
    data = JSONField(blank=True, null=True, verbose_name=_("Offer Data"))

    class Meta:
        verbose_name = _("External Offer")
        verbose_name_plural = _("External Offers")

    def __str__(self):
        return f"{self.external_offer_id}"


class FavoriteOffer(TimeStampedModel):
    task = models.ForeignKey(
        "orders.Task",
        on_delete=models.CASCADE,
        related_name="favorite_offers",
        verbose_name=_("Task"),
    )
    offer_id = models.PositiveBigIntegerField(
        verbose_name=_("Offer ID"),
        db_index=True,
    )
    reserved = models.BooleanField(default=False, verbose_name=_("Reserved"))

    class Meta:
        verbose_name = _("Favorite Offer")
        verbose_name_plural = _("Favorite Offers")

    def __str__(self):
        return f"{self.id} - {self.task}"

    @cached_property
    def reservation_remaining_hours(self):
        deal = getattr(self, "deal", None)
        if not deal:
            return
        limit_days = timedelta(days=settings.DEAL_RESERVATION_LIMIT_DAYS)
        time_difference = timezone.now() - deal.created
        remaining_time = limit_days - time_difference
        remaining_hours = max(remaining_time.total_seconds() / 3600, 0)
        return int(remaining_hours)
