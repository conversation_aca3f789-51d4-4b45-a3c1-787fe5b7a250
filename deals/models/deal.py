from django.contrib.contenttypes.fields import GenericRelation
from django.contrib.gis.db import models
from django.utils.translation import ugettext_lazy as _
from django_extensions.db.models import TimeStampedModel
from jsoneditor.fields.django3_jsonfield import J<PERSON><PERSON>ield


class DealStatus(models.IntegerChoices):
    CHOOSING_OFFER = 1, _("Choosing Offer")
    IN_PROGRESS = 2, _("In Progress")
    DEAL_FINISHED = 3, _("Deal Finished")
    DEAL_CANCELLED = 4, _("Deal Cancelled")


class Deal(TimeStampedModel):
    favorite_offer = models.OneToOneField(
        "deals.FavoriteOffer",
        on_delete=models.PROTECT,
        related_name="deal",
        verbose_name=_("Offer"),
    )
    status = models.PositiveSmallIntegerField(
        choices=DealStatus.choices,
        default=DealStatus.CHOOSING_OFFER,
        db_index=True,
        verbose_name=_("Status"),
    )
    form_data = JSONField(
        blank=True,
        null=True,
        verbose_name=_("Form Data"),
    )
    audits = GenericRelation("auditing.Audit", verbose_name=_("Audits"))

    class Meta:
        verbose_name = _("Deal")
        verbose_name_plural = _("Deals")

    def __str__(self):
        return f"{self.favorite_offer} - {self.id}"
