from django.contrib.gis.db import models
from django.utils.translation import ugettext_lazy as _
from django_extensions.db.models import TimeStampedModel
from jsoneditor.fields.django3_jsonfield import J<PERSON><PERSON><PERSON>


class Layer(TimeStampedModel):
    key = models.SlugField(
        verbose_name=_("Unique Form Key"),
        unique=True,
        max_length=200,
    )

    title = models.CharField(
        max_length=200, verbose_name=_("Layer Title"), null=True, blank=True
    )
    description = models.TextField(
        verbose_name=_("Layer Description"), null=True, blank=True
    )
    metadata = JSONField(
        verbose_name=_("Metadata"),
        blank=True,
        default=dict,
    )
    boundaries = models.PolygonField(
        blank=True, null=True, verbose_name=_("Boundaries")
    )

    class Meta:
        verbose_name = _("Layer")
        verbose_name_plural = _("Layers")

    def __str__(self):
        return self.title or self.key
