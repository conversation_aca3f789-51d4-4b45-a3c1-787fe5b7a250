from django.contrib.contenttypes.fields import GenericRelation
from django.contrib.gis.db import models
from django.utils.translation import ugettext_lazy as _
from django_extensions.db.models import TimeStampedModel
from jsoneditor.fields.django3_jsonfield import <PERSON><PERSON><PERSON><PERSON>

from deals.models.deal import DealStatus


class Agreement(TimeStampedModel):
    task = models.OneToOneField(
        "orders.task",
        on_delete=models.PROTECT,
        verbose_name="Task",
        related_name="agreement",
    )
    status = models.PositiveSmallIntegerField(
        choices=DealStatus.choices,
        default=DealStatus.IN_PROGRESS,
        db_index=True,
        verbose_name=_("Status"),
    )
    form_data = JSONField(
        blank=True,
        null=True,
        verbose_name=_("Form Data"),
    )
    audits = GenericRelation("auditing.Audit", verbose_name=_("Audits"))

    class Meta:
        verbose_name = _("Agreement")
        verbose_name_plural = _("Agreements")

    def __str__(self):
        return f"{self.id} - {self.task}"
