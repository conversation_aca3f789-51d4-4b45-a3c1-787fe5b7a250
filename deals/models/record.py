from django.contrib.gis.db import models
from django.utils.translation import ugettext_lazy as _
from django_extensions.db.models import TimeStampedModel
from jsoneditor.fields.django3_jsonfield import <PERSON><PERSON><PERSON><PERSON>

from deals.models.layer import Layer


class Record(TimeStampedModel):
    layer = models.ForeignKey(
        Layer,
        on_delete=models.CASCADE,
        null=True,
        verbose_name=_("Related Layer"),
    )
    geometry = models.GeometryField(
        null=True,
        blank=True,
        verbose_name=_("Geometry Collection Record"),
    )
    source_properties = JSONField(
        blank=True,
        null=True,
        verbose_name=_("Source Properties"),
    )

    class Meta:
        verbose_name = _("Geometry Record")
        verbose_name_plural = _("Geometry Records")
        indexes = [
            models.Index(models.F("source_properties__id"), name="record_id"),
            models.Index(models.F("source_properties__city_id"), name="record_city_id"),
            models.Index(models.F("source_properties__zone_id"), name="record_zone_id"),
            models.Index(
                models.F("source_properties__region_id"), name="record_region_id"
            ),
        ]

    def __str__(self):
        return f"LAYER: {self.layer}, RECORD: {self.pk}"
