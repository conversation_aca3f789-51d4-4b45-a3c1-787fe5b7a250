from django.db import models
from django.utils.functional import cached_property
from django.utils.translation import gettext_lazy as _
from jsoneditor.fields.django3_jsonfield import J<PERSON><PERSON><PERSON>

from utils.general_utils import get_nested_value

BENEFICIARY_DATA_MAPPER = {
    "family_member": "familyData.familyMembersCount",
    "job": "professionalData.job",
    "age": "personalData.ageHijri",
    "region": "location.region",
    "city": "location.city",
    "product": "supportPackages.product",
    "salary": "financialData.salary",
    "salary_bank": "financialData.salaryBank",
    "avg_fund": "financialData.fundingAmount",
    "mobile": "personalData.mobileNumber",
    "destination": "destination",
}


class BeneficiaryDataSource(models.TextChoices):
    INTERNAL = "internal_form", _("Internal")
    EXTERNAL = "external_source", _("External")


class Beneficiary(models.Model):
    user = models.OneToOneField(
        "users.User",
        on_delete=models.PROTECT,
        verbose_name=_("User"),
        null=True,
        blank=True,
        related_name="beneficiary",
    )
    data_source = models.CharField(
        max_length=50,
        choices=BeneficiaryDataSource.choices,
        verbose_name=_("Data Source"),
        default=BeneficiaryDataSource.EXTERNAL,
    )
    code = models.CharField(
        max_length=255, db_index=True, verbose_name=_("Code"), null=True
    )
    external_id = models.IntegerField(
        db_index=True, verbose_name=_("External ID"), null=True
    )
    name = models.CharField(
        max_length=255, blank=True, null=True, verbose_name=_("Name")
    )
    data = JSONField(blank=True, null=True, default=dict, verbose_name=_("Data"))

    class Meta:
        unique_together = (("code", "external_id"),)
        verbose_name = _("Beneficiary")
        verbose_name_plural = _("Beneficiaries")
        indexes = [
            models.Index(models.F("data__ID_CODE"), name="beneficiary_id_code"),
            models.Index(
                models.F("data__personalData__mobileNumber"), name="beneficiary_phone"
            ),
            models.Index(models.F("data__location__region"), name="beneficiary_region"),
            models.Index(models.F("data__location__city"), name="beneficiary_city"),
            models.Index(
                models.F("data__location__product"), name="beneficiary_product"
            ),
            models.Index(
                models.F("data__professionalData__job"), name="beneficiary_job"
            ),
            models.Index(
                models.F("data__financialData__salaryBank"),
                name="beneficiary_salary_bank",
            ),
            models.Index(models.F("data__personalData__age"), name="beneficiary_age"),
            models.Index(
                models.F("data__familyData__familyMembersCount"),
                name="beneficiary_family_member",
            ),
            models.Index(
                models.F("data__financialData__fundingAmount"),
                name="beneficiary_funding_amount",
            ),
        ]

    def __str__(self):
        return self.name or self.code

    def extract_data(self, field):
        field: str = BENEFICIARY_DATA_MAPPER.get(field)
        if not field:
            return None
        field = get_nested_value(data=self.data, path=field)
        return field

    @cached_property
    def task_set(self):
        return self.tasks.order_by("id")
