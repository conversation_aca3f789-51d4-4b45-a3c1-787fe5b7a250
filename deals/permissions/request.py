from django.utils.translation import gettext_lazy as _
from gabbro.graphene import Unauthorized

from common.permissions import PermissionsInterface


class RequestPerms(PermissionsInterface):
    def check_permissions(self, user, context: dict = None):
        task = context.get("task")
        permissions = (
            user.is_beneficiary and task.beneficiary.user == user,
            user.is_negotiator and task.negotiator == user,
            user.is_superuser,
        )
        if not any(permissions):
            raise Unauthorized(reason={"user": _("Permission Denied") % {}})
