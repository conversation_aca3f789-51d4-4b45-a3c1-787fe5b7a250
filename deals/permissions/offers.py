from django.utils.translation import gettext_lazy as _
from gabbro.graphene import Forbidden

from common.permissions import PermissionsInterface


class OfferPerms(PermissionsInterface):
    def check_permissions(self, user, context: dict = None):
        permissions = (user.is_superuser, user.is_negotiator)
        if not any(permissions):
            raise Forbidden(reason={"user": _("Permission Denied") % {}})


class UpdateOfferPerms(PermissionsInterface):
    def check_permissions(self, user, context: dict = None):
        task = context.get("task")
        permissions = (
            user.is_superuser,
            user.is_negotiator and task.negotiator == user,
        )
        if not any(permissions):
            raise Forbidden(reason={"user": _("Permission Denied") % {}})


class DeleteOffersPerms(PermissionsInterface):
    def check_permissions(self, user, context: dict = None):
        task = context.get("task")
        permissions = (
            user.is_superuser,
            user.is_negotiator and task.negotiator == user,
            user.is_beneficiary and task.beneficiary.user == user,
        )
        if not any(permissions):
            raise Forbidden(reason={"user": _("Permission Denied") % {}})


class BeneficiaryOffersPerms(PermissionsInterface):
    def check_permissions(self, user, context: dict = None):
        task = context.get("task")
        permissions = (
            user.is_superuser,
            user.is_negotiator and task.negotiator == user,
            user.is_beneficiary and task.beneficiary.user == user,
        )
        if not any(permissions):
            raise Forbidden(reason={"user": _("Permission Denied") % {}})
