from django.utils.translation import gettext_lazy as _
from gabbro.graphene import Forbidden

from common.permissions import PermissionsInterface


class DealsPerms(PermissionsInterface):
    def check_permissions(self, user, context: dict = None):
        permissions = (user.is_project_manager, user.is_superuser)
        if not any(permissions):
            raise Forbidden(reason={"user": _("Permission Denied") % {}})


class CreateDealsPerms(PermissionsInterface):
    def check_permissions(self, user, context: dict = None):
        task = context["task"]
        permissions = (
            user.is_negotiator and task.negotiator == user,
            user.is_superuser,
        )
        if not any(permissions):
            raise Forbidden(reason={"user": _("Permission Denied") % {}})


class UpdateDealsPerms(PermissionsInterface):
    def check_permissions(self, user, context: dict = None):
        task = context["task"]
        permissions = (
            user.is_negotiator and task.negotiator == user,
            user.is_superuser,
        )
        if not any(permissions):
            raise Forbidden(reason={"user": _("Permission Denied") % {}})
