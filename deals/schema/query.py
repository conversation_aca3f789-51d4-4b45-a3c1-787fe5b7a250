import logging

import graphene
from django.contrib.auth import get_user_model
from django.contrib.contenttypes.models import ContentType
from django.db.models import (
    Q,
    <PERSON>query,
    OuterRef,
    Char<PERSON>ield,
    F,
    Value,
    IntegerField,
    Count,
)
from django.db.models.functions import Concat
from django.utils.translation import gettext_lazy as _
from gabbro.graphene.exceptions import BadRequest

from auditing.models import Audit
from deals.management.commands.load_ren_locations import LAYERS_KEYS
from deals.models import (
    Deal,
    Layer,
    Request,
    Beneficiary,
    RequestStatus,
    Offer,
    Agreement,
    OfferRequest,
    FavoriteOffer,
    OfferRequestStatus,
)
from deals.permissions import (
    DealsPerms,
    CreateDealsPerms,
    OfferPerms,
    UpdateOfferPerms,
    BeneficiaryOffersPerms,
)
from deals.schema.object_types import (
    BeneficiaryListType,
    BeneficiaryFilterOptionsListType,
    LayerListType,
    RecordListType,
    DealListType,
    OfferListType,
    OfferLocationType,
    FavoriteOfferListType,
    BeneficiaryOffersList,
    InternalOffersListType,
    FavoriteOffersListAuditsType,
    AgreementListType,
    ExternalOffersLogsListType,
    InternalOffersLogsListType,
)
from deals.services import OfferService
from deals.strategies import FetchmakanOffersStrategy
from deals.validators import (
    FetchMakanOffersValidator,
    LayerValidator,
    TaskValidator,
)
from orders.models import Task
from users.models import UserRoleChoices
from users.schema.object_types import CustomerServiceListType, NegotiatorListType
from utils.graphene.decorators import authentication_required
from utils.graphene.query import (
    PageInfo,
    DjangoFilterInput,
    filter_qs_paginate_with_count,
    build_q,
    reshape_locations_filter,
)
from utils.locations import locations

User = get_user_model()
logger = logging.getLogger("deals")


class Query(graphene.ObjectType):
    beneficiary_filter_options = graphene.Field(
        BeneficiaryFilterOptionsListType,
    )
    beneficiaries = graphene.Field(
        BeneficiaryListType,
        pk=graphene.Int(),
        page_info=PageInfo(),
        filters=graphene.List(DjangoFilterInput),
    )
    customer_services = graphene.Field(
        CustomerServiceListType,
        pk=graphene.Int(),
        page_info=PageInfo(),
        filters=graphene.List(DjangoFilterInput),
    )
    negotiators = graphene.Field(
        NegotiatorListType,
        pk=graphene.Int(),
        page_info=PageInfo(),
        filters=graphene.List(DjangoFilterInput),
    )
    layers = graphene.Field(
        LayerListType,
        pk=graphene.Int(),
        page_info=PageInfo(),
        filters=graphene.List(DjangoFilterInput),
    )
    regions = graphene.Field(
        RecordListType,
        id=graphene.Int(),
        page_info=PageInfo(),
        filters=graphene.List(DjangoFilterInput),
    )
    cities = graphene.Field(
        RecordListType,
        pk=graphene.Int(),
        region_id=graphene.Int(),
        page_info=PageInfo(),
        filters=graphene.List(DjangoFilterInput),
    )
    zones = graphene.Field(
        RecordListType,
        pk=graphene.Int(),
        city_id=graphene.Int(),
        page_info=PageInfo(),
        filters=graphene.List(DjangoFilterInput),
    )
    districts = graphene.Field(
        RecordListType,
        pk=graphene.Int(),
        zones_id=graphene.List(graphene.Int),
        city_id=graphene.Int(),
        page_info=PageInfo(),
        filters=graphene.List(DjangoFilterInput),
    )
    favorite_offers = graphene.Field(
        FavoriteOfferListType,
        task_id=graphene.Int(required=True),
        page_info=PageInfo(),
        filters=graphene.List(DjangoFilterInput),
    )
    offers = graphene.Field(
        OfferListType,
        pk=graphene.ID(required=False),
        task_id=graphene.Int(required=True),
    )
    internal_offers = graphene.Field(
        InternalOffersListType,
        pk=graphene.Int(required=False),
        page_info=PageInfo(),
        filters=graphene.List(DjangoFilterInput),
    )
    offer_location_data = graphene.Field(
        OfferLocationType,
        latitude=graphene.Float(required=True),
        longitude=graphene.Float(required=True),
    )
    beneficiary_offers = graphene.Field(
        BeneficiaryOffersList,
    )
    deals = graphene.Field(
        DealListType,
        task_id=graphene.Int(required=True),
        page_info=PageInfo(),
        filters=graphene.List(DjangoFilterInput),
    )
    agreements = graphene.Field(
        AgreementListType,
        task_id=graphene.Int(required=True),
        pk=graphene.Int(),
        page_info=PageInfo(),
        filters=graphene.List(DjangoFilterInput),
    )
    request_audits = graphene.Field(
        FavoriteOffersListAuditsType,
        page_info=PageInfo(),
        pk=graphene.Int(required=False),
        task_id=graphene.Int(required=True),
        offer_id=graphene.String(required=False),
    )
    external_offers_logs = graphene.Field(
        ExternalOffersLogsListType,
        page_info=PageInfo(),
        filters=graphene.List(DjangoFilterInput),
        offer_id=graphene.String(required=False),
    )

    internal_offers_logs = graphene.Field(
        InternalOffersLogsListType,
        page_info=PageInfo(),
        filters=graphene.List(DjangoFilterInput),
        offer_id=graphene.String(required=False),
    )

    @staticmethod
    @authentication_required
    def resolve_beneficiary_filter_options(root, info):
        return BeneficiaryFilterOptionsListType()

    @staticmethod
    @authentication_required
    def resolve_beneficiaries(root, info, pk: int = None, page_info=None, filters=None):
        """
        retrieve all not Assigned Beneficiary & Beneficiaries that has previous Cancelled Deal
        """
        user = info.context.user
        DealsPerms().check_permissions(user)
        queryset = Beneficiary.objects.filter(tasks__isnull=True).distinct()
        return BeneficiaryListType(
            *filter_qs_paginate_with_count(queryset, build_q(pk, filters), page_info)
        )

    @staticmethod
    @authentication_required
    def resolve_customer_services(
        root, info, pk: int = None, page_info=None, filters=None
    ):
        user = info.context.user
        DealsPerms().check_permissions(user)
        queryset = User.objects.filter(
            roles__role=UserRoleChoices.CUSTOMER_SERVICES
        ).distinct()
        return CustomerServiceListType(
            *filter_qs_paginate_with_count(queryset, build_q(pk, filters), page_info)
        )

    @staticmethod
    @authentication_required
    def resolve_negotiators(root, info, pk: int = None, page_info=None, filters=None):
        user = info.context.user
        DealsPerms().check_permissions(user)
        queryset = User.objects.filter(
            roles__role=UserRoleChoices.NEGOTIATORS
        ).distinct()
        return NegotiatorListType(
            *filter_qs_paginate_with_count(queryset, build_q(pk, filters), page_info)
        )

    @staticmethod
    @authentication_required
    def resolve_layers(root, info, pk: int = None, page_info=None, filters=None):
        queryset = Layer.objects.all()
        return LayerListType(
            *filter_qs_paginate_with_count(queryset, build_q(pk, filters), page_info)
        )

    @staticmethod
    @authentication_required
    def resolve_regions(root, info, id: int = None, page_info=None, filters=None):
        layer = LayerValidator().get_object_if_exists(slug=LAYERS_KEYS.get("region"))
        filters = reshape_locations_filter(
            fields={"id": {"value": id}}, filters=filters
        )
        queryset = layer.record_set.distinct("source_properties__region_name")
        return RecordListType(
            *filter_qs_paginate_with_count(
                queryset, build_q(filters=filters), page_info
            )
        )

    @staticmethod
    @authentication_required
    def resolve_cities(
        root, info, pk: int = None, region_id: int = None, page_info=None, filters=None
    ):
        layer = LayerValidator().get_object_if_exists(slug=LAYERS_KEYS.get("city"))
        queryset = layer.record_set.distinct("source_properties__city_name")
        filters = reshape_locations_filter(
            fields={"region_id": {"value": region_id}}, filters=filters
        )
        return RecordListType(
            *filter_qs_paginate_with_count(queryset, build_q(pk, filters), page_info)
        )

    @staticmethod
    @authentication_required
    def resolve_zones(
        root, info, pk: int = None, city_id: int = None, page_info=None, filters=None
    ):
        layer = LayerValidator().get_object_if_exists(slug=LAYERS_KEYS.get("zone"))
        queryset = layer.record_set.distinct("source_properties__zone_name")
        filters = reshape_locations_filter(
            fields={"city_id": {"value": city_id}},
            filters=filters,
        )
        return RecordListType(
            *filter_qs_paginate_with_count(queryset, build_q(pk, filters), page_info)
        )

    @staticmethod
    @authentication_required
    def resolve_districts(
        root,
        info,
        zones_id: list = None,
        city_id: int = None,
        pk: int = None,
        page_info=None,
        filters=None,
    ):
        layer = LayerValidator().get_object_if_exists(slug=LAYERS_KEYS.get("districts"))
        queryset = layer.record_set.all().distinct("source_properties__district_name")
        zones_id = zones_id or True if city_id else zones_id or None
        filters = reshape_locations_filter(
            fields={
                "zone_id": {
                    "value": zones_id,
                    "clause": "isnull" if type(zones_id) is bool else "in",
                },
                "city_id": {"value": city_id},
            },
            filters=filters,
        )
        return RecordListType(
            *filter_qs_paginate_with_count(queryset, build_q(pk, filters), page_info)
        )

    @staticmethod
    @authentication_required
    def resolve_favorite_offers(
        root, info, task_id, pk: int = None, page_info=None, filters=None
    ):
        user = info.context.user
        task = TaskValidator().get_object_if_exists(task_id)
        UpdateOfferPerms().check_permissions(user, context={"task": task})
        favorite_offers = task.favorite_offers.all()
        offers_ids = list(favorite_offers.values_list("offer_id", flat=True))
        offers = FetchmakanOffersStrategy().fetch_offers(
            user=user,
            context={"task": task},
            offer_data={"ids": offers_ids},
            categorise_offers=False,
        )
        setattr(info.context, "offers", offers.get("data", []))
        return FavoriteOfferListType(
            data=favorite_offers, count=favorite_offers.count(), task=task
        )

    @staticmethod
    @authentication_required
    def resolve_offers(
        root,
        info,
        task_id: int,
        pk: int = None,
    ):
        user = info.context.user
        if pk:
            setattr(info.context, "offer_id", True)
        offers_service = OfferService(
            validations=FetchMakanOffersValidator(),
            perms=UpdateOfferPerms(),
            strategy=FetchmakanOffersStrategy(),
        )
        offers = offers_service.fetch_offers(
            offer_data={"task_id": task_id, "ids": [pk] if pk else None},
            user=user,
        )
        return OfferListType(
            data=offers["data"],
            geometries=offers["geometries"],
            boundaries=offers["boundaries"],
        )

    @staticmethod
    @authentication_required
    def resolve_internal_offers(
        root, info, pk: int = None, page_info=None, filters=None
    ):
        offers: [Offer] = Offer.objects.filter(
            Q(data__isnull=True) | Q(data__drafts__isnull=False)
        ).distinct()
        data, count = filter_qs_paginate_with_count(
            offers, build_q(pk, filters), page_info
        )

        return InternalOffersListType(
            data=data,
            count=count,
        )

    @staticmethod
    @authentication_required
    def resolve_beneficiary_offers(root, info):
        user = info.context.user
        task = (
            Task.objects.filter(
                beneficiary__user=user,
                beneficiary__user__roles__role=UserRoleChoices.BENEFICIARY,
            )
            .order_by("id")
            .last()
        )
        if not task:
            raise BadRequest(reason={"task": _("you don't have any task") % {}})
        BeneficiaryOffersPerms().check_permissions(
            user=user, context={"user": user, "task": task}
        )
        request = task.requests.order_by("id").last()
        if not request or (request and request.status != RequestStatus.SENT):
            return BeneficiaryOffersList(offers={}, task=task, request=request, count=0)

        favorite_offer_subquery = (
            FavoriteOffer.objects.filter(
                Q(task=task)
                & (
                    Q(offer_id=OuterRef("internal_offer__external_offer_id"))
                    | Q(offer_id=OuterRef("external_offer__external_offer_id"))
                ),
            )
            .distinct("offer_id")
            .values("id")[:1]
        )

        request_offers = (
            request.offer_requests.filter(
                status=OfferRequestStatus.INCLUDED,
            )
            .annotate(favorite_offer_id=Subquery(favorite_offer_subquery))
            .distinct(
                "external_offer__external_offer_id", "internal_offer__external_offer_id"
            )
        )
        request_offers, count = filter_qs_paginate_with_count(request_offers, build_q())
        return BeneficiaryOffersList(
            offers=request_offers, task=task, request=request, count=count
        )

    @staticmethod
    @authentication_required
    def resolve_deals(
        root, info, task_id, pk: int = None, page_info=None, filters=None
    ):
        user = info.context.user
        task = TaskValidator().get_object_if_exists(task_id)
        CreateDealsPerms().check_permissions(user, context={"task": task})
        queryset = Deal.objects.filter(favorite_offer__task=task).select_related(
            "favorite_offer"
        )
        return DealListType(
            *filter_qs_paginate_with_count(queryset, build_q(pk, filters), page_info)
        )

    @staticmethod
    @authentication_required
    def resolve_offer_location_data(root, info, latitude: int, longitude: int):
        user = info.context.user
        OfferPerms().check_permissions(user=user)
        offer_data, error = locations.get_offer_location_data(
            latitude=latitude, longitude=longitude
        )
        if error:
            raise BadRequest(reason={"error": error})
        return OfferLocationType(data=offer_data)

    @staticmethod
    @authentication_required
    def resolve_request_audits(
        root, info, task_id, pk: int = None, offer_id: int = None, page_info=None
    ):
        user = info.context.user
        task = TaskValidator().get_object_if_exists(task_id)
        OfferPerms().check_permissions(user)
        if offer_id and not pk:
            raise BadRequest(reason={"pk": _("you must provide offer_id with pk") % {}})
        requests_ids = task.requests.values_list("id", flat=True)
        queryset = Audit.objects.filter(
            content_type=ContentType.objects.get_for_model(Request),
            object_id__in=requests_ids,
        ).only("id", "data", "created", "modified", "created_by")
        queryset, count = filter_qs_paginate_with_count(
            queryset, build_q(pk), page_info=page_info
        )
        setattr(info.context, "offer_id", offer_id)
        return FavoriteOffersListAuditsType(data=queryset, count=count)

    @staticmethod
    @authentication_required
    def resolve_agreements(
        root, info, task_id, pk: int = None, page_info=None, filters=None
    ):
        user = info.context.user
        task = TaskValidator().get_object_if_exists(task_id)
        CreateDealsPerms().check_permissions(user, context={"task": task})
        queryset = Agreement.objects.filter(task=task).select_related("task")
        return AgreementListType(
            *filter_qs_paginate_with_count(queryset, build_q(pk, filters), page_info)
        )

    @staticmethod
    @authentication_required
    def resolve_external_offers_logs(
        root, info, offer_id: int = None, page_info=None, filters=None
    ):
        filters = filters or []

        user = info.context.user
        OfferPerms().check_permissions(user)

        offer_beneficiary_counts = (
            OfferRequest.objects.filter(external_offer_id__isnull=False)
            .values("external_offer_id")
            .annotate(count=Count("request__task__beneficiary", distinct=True))
        )
        beneficiaries_count = Subquery(
            offer_beneficiary_counts.filter(
                external_offer_id=OuterRef("external_offer_id")
            ).values("count")[:1],
            output_field=IntegerField(),
        )

        negotiators_count = Subquery(
            FavoriteOffer.objects.filter(
                offer_id=OuterRef("external_offer__external_offer_id")
            )
            .values("offer_id")  # optional
            .values("task__negotiator")
            .annotate(count=Count("task__negotiator", distinct=True))
            .values("count")[:1],
            output_field=IntegerField(),
        )

        queryset = (
            OfferRequest.objects.filter(external_offer__isnull=False)
            .select_related("external_offer")
            .annotate(
                negotiators_count=negotiators_count,
                beneficiaries_count=beneficiaries_count,
            )
            .distinct("external_offer__external_offer_id")
        ).order_by("external_offer__external_offer_id", "-created")
        if offer_id:
            filters.append(
                {"field": "external_offer__external_offer_id", "value": offer_id}
            )
            setattr(info.context, "offer_id", offer_id)

        return ExternalOffersLogsListType(
            *filter_qs_paginate_with_count(
                queryset, build_q(filters=filters), page_info
            )
        )

    @staticmethod
    @authentication_required
    def resolve_internal_offers_logs(
        root, info, offer_id: int = None, filters=None, page_info=None
    ):
        filters = filters or []
        user = info.context.user
        OfferPerms().check_permissions(user)
        offer_beneficiary_counts = (
            OfferRequest.objects.filter(internal_offer_id__isnull=False)
            .values("internal_offer_id")
            .annotate(count=Count("request__task__beneficiary", distinct=True))
        )
        beneficiaries_count = Subquery(
            offer_beneficiary_counts.filter(
                internal_offer_id=OuterRef("internal_offer_id")
            ).values("count")[:1],
            output_field=IntegerField(),
        )

        negotiators_count = Subquery(
            FavoriteOffer.objects.filter(
                offer_id=OuterRef("internal_offer__external_offer_id")
            )
            .values("offer_id")  # optional
            .values("task__negotiator")
            .annotate(count=Count("task__negotiator", distinct=True))
            .values("count")[:1],
            output_field=IntegerField(),
        )

        # Final Queryset
        queryset = (
            (
                OfferRequest.objects.filter(internal_offer__isnull=False).annotate(
                    first_created_by=get_full_name_of_create_offers(["created"]),
                    last_modified_by=get_full_name_of_create_offers(["-created"]),
                    beneficiaries_count=beneficiaries_count,
                    negotiators_count=negotiators_count,
                )
            )
            .order_by("internal_offer__external_offer_id", "-created")
            .distinct("internal_offer__external_offer_id")
        )
        if offer_id:
            filters.append(
                {"field": "internal_offer__external_offer_id", "value": offer_id}
            )
            setattr(info.context, "offer_id", offer_id)

        return InternalOffersLogsListType(
            *filter_qs_paginate_with_count(
                queryset, build_q(filters=filters), page_info
            )
        )


def get_offers_from_request(request: Request, offer_id: int) -> list:
    data = request.data or {}
    offers = data.get("offers", [])
    offers_ids = [int(offer) for offer in offers]
    if offer_id and offer_id in offers_ids:
        return [offer_id]
    return offers_ids


def get_full_name_of_create_offers(order_by_fields: list[str]):
    """
    Generate a subquery to get the `full_name` of the creator of audits
    linked to internal offers.

    Args:
        order_by_fields (List[str]): Fields to order the audits by.

    Returns:
        Subquery: A subquery for the `full_name` of the creator.
    """
    # Extracted to a constant for reusability and clarity
    offer_content_type = ContentType.objects.get_for_model(Offer)

    return Subquery(
        Audit.objects.filter(
            content_type=offer_content_type, object_id=OuterRef("internal_offer_id")
        )
        .order_by(*order_by_fields)
        .annotate(
            full_name=Concat(
                F("created_by__first_name"),
                Value(" "),
                F("created_by__last_name"),
                output_field=CharField(),
            )
        )
        .values("full_name")[:1],
        output_field=CharField(),
    )
