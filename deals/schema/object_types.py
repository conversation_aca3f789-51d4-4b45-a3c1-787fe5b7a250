import json

import graphene
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from graphene import ObjectType
from graphene.types.generic import GenericScalar
from graphene_django import DjangoObjectType
from graphene_gis.scalars import J<PERSON><PERSON><PERSON>ar, GISScalar

from auditing.models import Audit
from deals.management.commands.load_ren_locations import LAYERS_KEYS
from deals.models import (
    Agreement,
    Request,
    Deal,
    Layer,
    Record,
    FavoriteOffer,
    OFFER_DATA_MAPPER,
    OFFER_TYPE_MAPPER,
    RequestStatus,
    Offer,
    OfferRequest,
)
from deals.models.beneficiary import (
    Beneficiary,
    BENEFICIARY_DATA_MAPPER,
)
from orders.models import TaskStatus, Task
from orders.schema import TaskType
from resource_management.models import ResourceManagement
from users.schema.object_types import UserType
from utils.general_utils import get_nested_value

User = get_user_model()


class BeneficiaryDataType(ObjectType):
    """
    Add beneficiary.data.{field_name} in following fields in order to filter and order with these values
    - age
    - region
    - job
    - city
    - product
    - family_member
    - salary
    - salary_bank
    - avg_fund
    - destination
    - mobile
    """

    age = graphene.Int()
    region = graphene.String()
    job = graphene.String()
    city = graphene.String()
    product = graphene.String()
    family_member = graphene.Int()
    salary = graphene.Float()
    salary_bank = graphene.String()
    avg_fund = graphene.Float()
    destination = graphene.String()
    mobile = graphene.String()

    def resolve_job(self: Beneficiary, info):
        return self.extract_data("job")

    def resolve_family_member(self: Beneficiary, info):
        return self.extract_data("family_member")

    def resolve_age(self: Beneficiary, info):
        return self.extract_data("age")

    def resolve_region(self: Beneficiary, info):
        return self.extract_data("region")

    def resolve_city(self: Beneficiary, info):
        return self.extract_data("city")

    def resolve_product(self: Beneficiary, info):
        return self.extract_data("product")

    def resolve_salary(self: Beneficiary, info):
        return self.extract_data("salary")

    def resolve_salary_bank(self: Beneficiary, info):
        return self.extract_data("salary_bank")

    def resolve_avg_fund(self: Beneficiary, info):
        return self.extract_data("avg_fund")

    def resolve_destination(self: Beneficiary, info):
        return self.extract_data("destination")

    def resolve_mobile(self: Beneficiary, info):
        return self.extract_data("mobile")


class BeneficiaryType(DjangoObjectType):
    id = graphene.Int()
    external_id = graphene.Int()
    task_status = graphene.Field(JSONScalar)
    assigned_task_status = graphene.Field(JSONScalar)
    beneficiary_data = graphene.Field(BeneficiaryDataType)
    task_id = graphene.Int()

    class Meta:
        model = Beneficiary
        fields = [
            "id",
            "code",
            "name",
        ]

    def resolve_task_id(self: Beneficiary, info):
        """
        resolve Last task id for this Beneficiary
        """
        task = self.task_set.last()
        return task.id if task else None

    def resolve_task_status(self: Beneficiary, info):
        """
        resolve status from the last task related to this Beneficiary
        """
        task = self.task_set.last()
        return (
            {"key": task.status, "display": task.get_status_display()} if task else None
        )

    def resolve_assigned_task_status(self: Beneficiary, info):
        """
        resolve which role assigned to this Beneficiary
        """
        task = self.task_set.last()
        return (
            {"key": task.assigned_status, "display": task.get_assigned_status_display()}
            if task
            else None
        )

    def resolve_beneficiary_data(self: Beneficiary, info):
        return self

    def resolve_external_id(self: Beneficiary, info):
        value = self.external_id
        return value


class BeneficiaryListType(graphene.ObjectType):
    data = graphene.List(BeneficiaryType)
    count = graphene.Int()


class BeneficiaryFilterOptionsListType(graphene.ObjectType):
    job = graphene.List(graphene.String)
    region = graphene.List(graphene.String)
    city = graphene.List(graphene.String)
    product = graphene.List(graphene.String)
    salary_bank = graphene.List(graphene.String)
    task_status = GenericScalar()
    service_type = graphene.List(graphene.String)

    def resolve_job(self, info):
        field = BENEFICIARY_DATA_MAPPER.get("job", "")
        field = "__".join(field.split("."))
        filters = {f"data__{field}"}
        values: Beneficiary = Beneficiary.objects.distinct(*filters).values_list(
            *filters, flat=True
        )
        return values

    def resolve_region(self, info):
        field = BENEFICIARY_DATA_MAPPER.get("region", "")
        field = "__".join(field.split("."))
        filters = {f"data__{field}"}
        values: Beneficiary = Beneficiary.objects.distinct(*filters).values_list(
            *filters, flat=True
        )
        region_layer = Layer.objects.filter(key=LAYERS_KEYS.get("region")).first()
        region_records = Record.objects.filter(layer=region_layer).values_list(
            "source_properties__region_name", flat=True
        )
        values = region_records.union(values)
        return values

    def resolve_city(self, info):
        field = BENEFICIARY_DATA_MAPPER.get("city", "")
        field = "__".join(field.split("."))
        filters = {f"data__{field}"}
        values: Beneficiary = Beneficiary.objects.distinct(*filters).values_list(
            *filters, flat=True
        )
        city_layer = Layer.objects.filter(key=LAYERS_KEYS.get("city")).first()
        city_records = Record.objects.filter(layer=city_layer).values_list(
            "source_properties__city_name", flat=True
        )
        values = city_records.union(values)
        return values

    def resolve_product(self, info):
        field = BENEFICIARY_DATA_MAPPER.get("product", "")
        field = "__".join(field.split("."))
        filters = {f"data__{field}"}
        values: Beneficiary = Beneficiary.objects.distinct(*filters).values_list(
            *filters, flat=True
        )
        return values

    def resolve_salary_bank(self, info):
        field = BENEFICIARY_DATA_MAPPER.get("salary_bank", "")
        field = "__".join(field.split("."))
        filters = {f"data__{field}"}
        values: Beneficiary = Beneficiary.objects.distinct(*filters).values_list(
            *filters, flat=True
        )
        return values

    def resolve_task_status(self, info):
        return [
            {"label": _("%(label)s") % {"label": status.label}, "value": status.value}
            for status in TaskStatus
        ]

    def resolve_service_type(self, info):
        return ["بحث عن عقار", "تمويل عقاري"]


class LayerType(DjangoObjectType):
    class Meta:
        model = Layer
        fields = [
            "id",
            "key",
            "title",
            "description",
            "metadata",
            "created",
            "modified",
        ]


class LayerListType(graphene.ObjectType):
    data = graphene.List(LayerType)
    count = graphene.Int()


class RecordType(DjangoObjectType):
    geometry = graphene.Field(GISScalar)
    source_properties = graphene.Field(JSONScalar)

    class Meta:
        model = Record
        fields = ["layer", "source_properties", "created", "modified"]


class RecordListType(graphene.ObjectType):
    data = graphene.List(RecordType)
    count = graphene.Int()


class OffersNegotiatorsType(graphene.ObjectType):
    first_name = graphene.String()
    last_name = graphene.String()


class OffersNegotiatorsListType(graphene.ObjectType):
    count = graphene.Int()
    data = graphene.List(OffersNegotiatorsType)


class OfferData(graphene.ObjectType):
    establishment_type = graphene.String()
    address = graphene.Field(JSONScalar)
    offer_type = graphene.List(graphene.String)
    real_estate_price = graphene.String()
    offer_room = graphene.String()
    offer_bathroom = graphene.String()
    offer_area = graphene.String()
    images = graphene.List(graphene.String)

    def resolve_images(self: dict, info):
        path = self.get("offer_type")
        path = OFFER_DATA_MAPPER.get(path, {}).get("images", "")
        value = get_nested_value(data=self, path=path)
        return value

    def resolve_offer_room(self: dict, info):
        path = self.get("offer_type")
        path = OFFER_DATA_MAPPER.get(path, {}).get("bedroom", "")
        value = get_nested_value(data=self, path=path)
        return value

    def resolve_offer_bathroom(self: dict, info):
        path = self.get("offer_type")
        path = OFFER_DATA_MAPPER.get(path, {}).get("bathroom", "")
        value = get_nested_value(data=self, path=path)
        return value

    def resolve_offer_area(self: dict, info):
        path = self.get("offer_type")
        path = OFFER_DATA_MAPPER.get(path, {}).get("area", "")
        value = get_nested_value(data=self, path=path)
        return value

    def resolve_establishment_type(self: dict, info):
        field = get_nested_value(
            data=self, path=OFFER_DATA_MAPPER["establishment_type"]
        )
        return field

    def resolve_address(self: dict, info):
        field = get_nested_value(data=self, path=OFFER_DATA_MAPPER["address"])
        field = field if isinstance(field, dict) else {}
        return {
            "city": field.get("city"),
            "street": field.get("street"),
            "district": field.get("district"),
        }

    def resolve_offer_type(self: dict, info):
        field = get_nested_value(data=self, path=OFFER_DATA_MAPPER["offer_type"])
        return field

    def resolve_real_estate_price(self: dict, info):
        field = get_nested_value(data=self, path=OFFER_DATA_MAPPER["real_estate_price"])
        return field


class OffersType(graphene.ObjectType):
    offer_type = graphene.String()
    id = graphene.String()
    ren_offer_id = graphene.Int()
    status = graphene.String()
    data = graphene.Field(JSONScalar)
    offer_data = graphene.Field(OfferData)
    negotiators = graphene.Field(OffersNegotiatorsListType)
    favorite_offer_id = graphene.Int()
    geometry = GenericScalar()
    add_to_favorite = graphene.Boolean()

    def resolve_id(self: dict, info):
        return self.get("id")

    def resolve_geometry(self: dict, info):
        return self.get("geometry")

    def resolve_data(self: dict, info):
        offer_data = self.get("offer_form", {}).get("data", {}).get("office", {})
        drafts = offer_data.get("drafts", {})
        data = (
            {"completingOffer": drafts.get("completingOffer", {})}
            if drafts
            else {"completingOffer": offer_data.get("completingOffer", {})}
        )
        return data if getattr(info.context, "offer_id", None) else {}

    def resolve_offer_data(self: dict, info):
        offer_data = self.get("offer_form", {}).get("data", {}).get("office", {})
        drafts = offer_data.get("drafts", {}).get("completingOffer")
        data = drafts if drafts else offer_data.get("completingOffer", {})
        data["offer_type"] = OFFER_TYPE_MAPPER.get(
            data.get("realEstateDetails", {}).get("establishmentType", "")
        )
        return data


class CategorisedOffersType(graphene.ObjectType):
    completed_offers = graphene.List(OffersType)
    external_offers = graphene.List(OffersType)
    uncompleted_offers = graphene.List(OffersType)


class BeneficiaryOffersType(graphene.ObjectType):
    id = graphene.String()
    status = graphene.String()
    data = graphene.Field(JSONScalar)
    offer_data = graphene.Field(OfferData)
    negotiators = graphene.Field(OffersNegotiatorsListType)
    favorite_offer_id = graphene.Int()
    geometry = GenericScalar()

    def resolve_data(self: OfferRequest, info):
        offer_data = self.data or {}
        offer_data = offer_data.get("offer_form", {}).get("data", {}).get("office", {})
        return offer_data

    def resolve_offer_data(self: OfferRequest, info):
        offer_data = self.data or {}
        offer_data = (
            offer_data.get("offer_form", {})
            .get("data", {})
            .get("office", {})
            .get("completingOffer", {})
        )
        offer_data["offer_type"] = OFFER_TYPE_MAPPER.get(
            offer_data.get("realEstateDetails", {}).get("establishmentType", "")
        )
        return offer_data

    def resolve_geometry(self: OfferRequest, info):
        offer_data = self.data or {}
        offer_data = offer_data.get("geometry", {})
        return offer_data

    def resolve_id(self: dict, info):
        offer_data = self.data or {}
        offer_data = offer_data.get("id", {})
        return offer_data


class BeneficiaryOffersList(graphene.ObjectType):
    request = graphene.Field("deals.schema.object_types.RequestType")
    offers = graphene.List(BeneficiaryOffersType)
    beneficiary_data = graphene.Field(JSONScalar)
    count = graphene.Int()
    is_link_expired = graphene.Boolean()
    time_elapsed = graphene.String()
    task = graphene.Field(TaskType)

    def resolve_time_elapsed(self, info):
        request: Request = self.request
        return request.expired - timezone.now() if request and request.expired else None

    def resolve_is_link_expired(self, info):
        request: Request = self.request
        return request.is_expired if request and request.expired else None

    def resolve_beneficiary_data(self: dict, info):
        task: Task = self.task
        data = (
            task.form_data.get("negotiator", {}).get("negotiatorData") if task else {}
        )
        return data


class FavoriteOffersType(DjangoObjectType):
    has_deal = graphene.Boolean()
    task_id = graphene.Int()
    offer = graphene.Field(OffersType)
    offer_id = graphene.String()

    class Meta:
        model = FavoriteOffer
        fields = ["id", "offer_id", "reserved"]

    def resolve_has_deal(self: FavoriteOffer, info):
        deal = getattr(self, "deal", None)
        return bool(deal)

    def resolve_offer(self: FavoriteOffer, info):
        offers = getattr(info.context, "offers", [])
        offer = [
            offer
            for offer in offers
            if offer.get("id") and self.offer_id == offer["id"]
        ] or [{}]
        return offer[0]


class OfferListType(graphene.ObjectType):
    data = graphene.List(CategorisedOffersType)
    boundaries = GenericScalar()
    geometries = GenericScalar()


class InternalOffersType(DjangoObjectType):
    id = graphene.Int()
    offer_data = graphene.Field(OfferData)
    geometry = graphene.Field(GISScalar)
    data = graphene.Field(JSONScalar)

    class Meta:
        model = Offer
        fields = ["id", "data"]

    def resolve_geometry(self: Offer, info):
        return self.geometry

    def resolve_offer_data(self: Offer, info):
        data = self.data or {}
        data = data.get("drafts", {}).get("completingOffer", {}) or data.get(
            "completingOffer", {}
        )
        data["offer_type"] = OFFER_TYPE_MAPPER.get(
            data.get("realEstateDetails", {}).get("establishmentType", "")
        )
        return data


class InternalOffersListType(graphene.ObjectType):
    data = graphene.List(InternalOffersType)
    count = graphene.Int()
    boundaries = graphene.Field(GISScalar)
    geometries = GenericScalar()

    def resolve_geometries(self: Offer, info):
        data = self.data or {}
        geometries = [
            {
                "offer_id": obj.id,
                "offer_price": get_nested_value(
                    data=obj.data,
                    path=OFFER_DATA_MAPPER["real_estate_price"],
                ),
                "geometry": json.loads(obj.geometry.geojson),
            }
            for obj in data
            if obj.geometry
        ] or None
        return geometries


class FavoriteOfferListType(graphene.ObjectType):
    data = graphene.List(FavoriteOffersType)
    is_link_expired = graphene.Boolean()
    count = graphene.Int()
    share_offers = graphene.Boolean()
    task = graphene.Field(TaskType)

    def resolve_is_link_expired(self: FavoriteOffer, info):
        request: Request = self.task.requests.order_by("id").last()
        return request.is_expired if request and request.expired else False

    def resolve_share_offers(self: FavoriteOffer, info):
        request: Request = self.task.requests.order_by("id").last()
        if self.task.favorite_offers.filter(deal__isnull=False).first() or (
            request and request.is_expired
        ):
            return False
        if not request or (request and request.status) in [
            RequestStatus.PENDING,
            RequestStatus.ACCEPTED,
        ]:
            return True
        return False


class DealType(DjangoObjectType):
    status = graphene.Field(JSONScalar)
    form_data = graphene.Field(JSONScalar)
    task = graphene.Field(TaskType)

    class Meta:
        model = Deal
        fields = ["id", "form_data", "status"]

    def resolve_status(self: Deal, info):
        return {"key": self.status, "display": self.get_status_display()}

    def resolve_task(self: Deal, info):
        return self.favorite_offer.task


class DealListType(graphene.ObjectType):
    data = graphene.List(DealType)
    count = graphene.Int()


class OfferLocationType(graphene.ObjectType):
    data = GenericScalar()


class RequestType(DjangoObjectType):
    data = graphene.Field(JSONScalar)
    status = graphene.Field(JSONScalar)

    def resolve_status(self: Request, info):
        return {"key": self.status, "display": self.get_status_display()}

    class Meta:
        model = Request


class RequestListType(graphene.ObjectType):
    data = graphene.List(RequestType)
    count = graphene.Int()


class FavoriteOffersAuditsType(DjangoObjectType):
    class Meta:
        model = Audit
        fields = ["id", "data", "created", "modified", "created_by"]

    data = graphene.Field(JSONScalar)
    offers = graphene.List(graphene.String)
    status = graphene.Field(JSONScalar)
    created_by = graphene.Field(UserType)
    has_data = graphene.Boolean()

    def resolve_has_data(self: Audit, info):
        data = self.data or {}
        offers = data.get("offers", {})
        if not isinstance(offers, dict):
            return False
        return any(value for key, value in offers.items())

    def resolve_status(self: Audit, info):
        data = self.data or {}
        status = data.get("status", {})
        return status

    def resolve_data(self: Audit, info):
        if not getattr(info.context, "offer_id", None):
            return {}
        offer_id = getattr(info.context, "offer_id", None)
        data = self.data or {}
        data = data.get("offers", {}).get(str(offer_id), {})
        form_data = (
            data.get("offer_form", {})
            .get("data", {})
            .get("office", {})
            .get("completingOffer", {})
        )
        return {"completingOffer": form_data}

    def resolve_offers(self: Audit, info):
        data = self.data or {}
        offers = data.get("offers", {}).keys()
        return list(offers)


class FavoriteOffersListAuditsType(graphene.ObjectType):
    data = graphene.List(FavoriteOffersAuditsType)
    count = graphene.Int()


class AgreementType(DjangoObjectType):
    status = graphene.Field(JSONScalar)
    form_data = graphene.Field(JSONScalar)

    class Meta:
        model = Agreement
        fields = ["id", "task", "status", "form_data"]

    def resolve_status(self: Agreement, info):
        return {"key": self.status, "display": self.get_status_display()}


class AgreementListType(graphene.ObjectType):
    data = graphene.List(AgreementType)
    count = graphene.Int()


class ExternalOfferLogsType(DjangoObjectType):
    data = graphene.Field(JSONScalar)
    offer_data = graphene.Field(OfferData)
    offer_status = graphene.Field(JSONScalar)
    beneficiaries_count = graphene.Int()
    negotiators_count = graphene.Int()
    offer_id = graphene.String()

    class Meta:
        model = OfferRequest
        fields = [
            "data",
            "offer_data",
            "beneficiaries_count",
            "negotiators_count",
            "offer_id",
        ]

    def resolve_offer_status(self: OfferRequest, info):
        offer = self.external_offer
        return {"key": offer.status, "display": offer.get_status_display()}

    def resolve_offer_id(self: OfferRequest, info):
        return self.external_offer.external_offer_id

    def resolve_offer_data(self: OfferRequest, info):
        data = self.data or {}
        data = (
            data.get("offer_form", {})
            .get("data", {})
            .get("office", {})
            .get("completingOffer", {})
        )
        data["offer_type"] = OFFER_TYPE_MAPPER.get(
            data.get("realEstateDetails", {}).get("establishmentType", "")
        )
        return data

    def resolve_data(self: OfferRequest, info):
        data = self.data or {}
        data = data.get("offer_form", {}).get("data", {}).get("office", {})
        return data if getattr(info.context, "offer_id", None) else {}

    def resolve_negotiators_count(self: OfferRequest, info):
        return self.negotiators_count or 0


class InternalOfferLogsType(DjangoObjectType):
    data = graphene.Field(JSONScalar)
    offer_data = graphene.Field(OfferData)
    beneficiaries_count = graphene.Int()
    negotiators_count = graphene.Int()
    offer_id = graphene.String()
    first_created_by = graphene.String()
    last_modified_by = graphene.String()
    modified = graphene.String()
    offer_status = graphene.Field(JSONScalar)

    class Meta:
        model = OfferRequest
        fields = [
            "data",
            "offer_data",
            "beneficiaries_count",
            "negotiators_count",
            "first_created_by",
            "last_modified_by",
            "modified",
        ]

    def resolve_offer_status(self: OfferRequest, info):
        offer = self.internal_offer
        return {"key": offer.status, "display": offer.get_status_display()}

    def resolve_offer_id(self: OfferRequest, info):
        return self.internal_offer.external_offer_id

    def resolve_negotiators_count(self: OfferRequest, info):
        return self.negotiators_count or 0

    def resolve_offer_data(self: OfferRequest, info):
        data = self.data or {}
        data = (
            data.get("offer_form", {})
            .get("data", {})
            .get("office", {})
            .get("completingOffer", {})
        )
        data["offer_type"] = OFFER_TYPE_MAPPER.get(
            data.get("realEstateDetails", {}).get("establishmentType", "")
        )
        return data

    def resolve_data(self: OfferRequest, info):
        data = self.data or {}
        data = data.get("offer_form", {}).get("data", {}).get("office", {})
        return data if getattr(info.context, "offer_id", None) else {}


class InternalOffersLogsListType(graphene.ObjectType):
    data = graphene.List(InternalOfferLogsType)
    count = graphene.Int()


class ExternalOffersLogsListType(graphene.ObjectType):
    data = graphene.List(ExternalOfferLogsType)
    count = graphene.Int()


class BulkBeneficiaryObjectType(DjangoObjectType):
    created_beneficiaries = graphene.Int()
    errors_file = graphene.String()
    errors = graphene.Field(JSONScalar)

    class Meta:
        model = ResourceManagement
        fields = ["errors_file"]

    def resolve_created_beneficiaries(self: ResourceManagement, info):
        data = self.data or {}
        return data.get("metadata", {}).get("created_records")

    def resolve_errors_file(self: ResourceManagement, info):
        return self.errors_file.url if self.errors_file else None

    def resolve_errors(self: ResourceManagement, info):
        data = self.data or {}
        return data.get("errors", [])


__all__ = [
    "BeneficiaryType",
    "BeneficiaryListType",
    "BeneficiaryFilterOptionsListType",
    "LayerListType",
    "RecordListType",
    "OffersType",
    "OfferListType",
    "FavoriteOfferListType",
    "FavoriteOffersType",
    "BeneficiaryOffersList",
    "OfferLocationType",
    "InternalOffersType",
    "InternalOffersListType",
    "DealListType",
    "DealType",
    "RequestListType",
    "RequestType",
    "FavoriteOffersListAuditsType",
    "AgreementType",
    "AgreementListType",
    "InternalOffersLogsListType",
    "ExternalOffersLogsListType",
    "BulkBeneficiaryObjectType",
]
