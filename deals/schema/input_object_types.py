import graphene

from deals.models import DealStatus
from orders.schema.input_object_types import TaskStatusEnum

DealStatusEnum = graphene.Enum.from_enum(DealStatus)


class OrderInputYpe(graphene.InputObjectType):
    beneficiaries = graphene.List(graphene.Int, required=True)
    customer_services = graphene.List(graphene.Int, required=True)


class CreateOfferInputType(graphene.InputObjectType):
    form_data = graphene.JSONString(required=True)
    form_schema_key = graphene.String(required=True)
    is_draft = graphene.Boolean(required=False)


class UpdateOfferInputType(graphene.InputObjectType):
    offer_id = graphene.Int(required=True)
    form_data = graphene.JSONString(required=True)
    form_schema_key = graphene.String(required=True)
    is_draft = graphene.Boolean(required=False)


class CreateFavoriteOfferInputType(graphene.InputObjectType):
    offer_id = graphene.ID(required=True)
    task_id = graphene.Int(required=True)


class CreateDealInputType(graphene.InputObjectType):
    favorite_offer_id = graphene.Int(required=True)


class DeleteDealInputType(graphene.InputObjectType):
    favorite_offer_id = graphene.Int(required=True)
    task_id = graphene.Int(required=True)


class UpdateDealInputType(graphene.InputObjectType):
    deal_id = graphene.Int(required=True)
    form_data = graphene.JSONString(required=True)
    task_status = graphene.Argument(TaskStatusEnum, required=False)
    form_schema_key = graphene.String(required=True)
    is_draft = graphene.Boolean(required=True)


class ReplaceDealFavoriteOfferInputType(graphene.InputObjectType):
    favorite_offer_id = graphene.Int(required=True)
    task_id = graphene.Int(required=True)


class FinishDealInputType(graphene.InputObjectType):
    deal_id = graphene.Int(required=True)
    form_schema_key = graphene.String(required=True)


class RejectDealInputType(graphene.InputObjectType):
    deal_id = graphene.Int(required=True)


class RequestInputType(graphene.InputObjectType):
    task_id = graphene.Int(required=True)


class CreateAgreementInputTpe(graphene.InputObjectType):
    task_id = graphene.Int(required=True)


class UpdateAgreementInputTpe(graphene.InputObjectType):
    id = graphene.Int(required=True)
    form_schema = graphene.String(required=True)
    form_data = graphene.JSONString(required=True)
    is_draft = graphene.Boolean(required=False)


class FinishAgreementInputTpe(graphene.InputObjectType):
    id = graphene.Int(required=True)
    form_schema = graphene.String(required=True)


class CreateBeneficiaryInputType(graphene.InputObjectType):
    form_schema = graphene.String(required=True)
    form_data = graphene.JSONString(required=True)


__all__ = [
    "OrderInputYpe",
    "CreateDealInputType",
    "UpdateDealInputType",
    "FinishDealInputType",
    "ReplaceDealFavoriteOfferInputType",
    "DeleteDealInputType",
    "CreateFavoriteOfferInputType",
    "UpdateOfferInputType",
    "CreateOfferInputType",
    "RejectDealInputType",
    "RequestInputType",
    "CreateAgreementInputTpe",
    "UpdateAgreementInputTpe",
    "FinishAgreementInputTpe",
    "CreateBeneficiaryInputType",
]
