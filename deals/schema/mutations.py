import logging

import graphene
from django.conf import settings
from django.contrib.auth import get_user_model
from django.db import transaction
from django.utils.translation import gettext_lazy as _

from deals.models import Request
from deals.permissions import (
    CreateDealsPerms,
    UpdateDealsPerms,
    OfferPerms,
    UpdateOfferPerms,
    RequestPerms,
    DeleteOffersPerms,
    DealsPerms,
)
from deals.schema.input_object_types import (
    CreateFavoriteOfferInputType,
    CreateOfferInputType,
    CreateDealInputType,
    RequestInputType,
    CreateAgreementInputTpe,
    UpdateAgreementInputTpe,
    FinishAgreementInputTpe,
    CreateBeneficiaryInputType,
)
from deals.schema.input_object_types import (
    FinishDealInputType,
    UpdateDealInputType,
    UpdateOfferInputType,
    RejectDealInputType,
    ReplaceDealFavoriteOfferInputType,
)
from deals.schema.object_types import (
    RequestType,
    FavoriteOffersType,
    DealType,
    InternalOffersType,
    AgreementType,
    BeneficiaryType,
    BulkBeneficiaryObjectType,
)
from deals.services import (
    RequestService,
    OfferService,
    DealService,
    FavoriteOfferService,
    AgreementService,
    BeneficiaryService,
)
from deals.strategies import (
    CreateOfferStrategy,
    UpdateOfferStrategy,
    CreateNewDealStrategy,
    UpdateDealStrategy,
    FinishDealStrategy,
    RejectDealStrategy,
    CreateFavoriteOfferStrategy,
    DeleteFavoriteOfferStrategy,
    ChoseDealFavoriteOfferStrategy,
    CreateRequestStrategy,
    ShareFavoriteOffersStrategy,
    ActivateRequestStrategy,
    UpdateAgreementStrategy,
    FinishAgreementStrategy,
    CreateAgreementStrategy,
    CreateBeneficiariesStrategy,
    UploadBulkBeneficiariesStrategy,
)
from deals.validators import CreateDealValidation, UpdateDealValidation
from deals.validators import (
    OfferValidation,
    CreateFavoriteOfferValidation,
    DeleteFavoriteOfferValidation,
    ChoseDealFavoriteOfferValidation,
    CreateRequestValidation,
    ShareFavoriteOfferValidation,
    ActivateRequestValidation,
    UpdateAgreementValidation,
    CreateAgreementValidation,
    CreateBeneficiaryValidation,
    UploadBulkBeneficiaryValidation,
)
from notification_manager.schema.object_types import NotificationType
from utils.general_utils import create_notification
from utils.graphene.decorators import authentication_required

MEDIA_URL = getattr(settings, "MEDIA_URL")

logger = logging.getLogger("orders")
User = get_user_model()


class CreateOfferMutation(graphene.Mutation):
    offer = graphene.Field(InternalOffersType)

    class Input:
        offer_input = CreateOfferInputType(required=True)

    @staticmethod
    @authentication_required
    @transaction.atomic
    def mutate(root, info, offer_input):
        user = info.context.user
        offer_service = OfferService(
            strategy=CreateOfferStrategy(),
            perms=OfferPerms(),
            validations=OfferValidation(),
        )
        offer = offer_service.create_offer(offer_data=offer_input, user=user)
        return CreateOfferMutation(offer=offer)


class UpdateOfferMutation(graphene.Mutation):
    offer = graphene.Field(InternalOffersType)

    class Input:
        offer_input = UpdateOfferInputType(required=True)

    @staticmethod
    @authentication_required
    @transaction.atomic
    def mutate(root, info, offer_input):
        user = info.context.user
        offer_service = OfferService(
            strategy=UpdateOfferStrategy(),
            perms=OfferPerms(),
            validations=OfferValidation(),
        )
        offer = offer_service.update_offer(offer_data=offer_input, user=user)
        return UpdateOfferMutation(offer=offer)


class ChoseDealFavoriteOfferMutation(graphene.Mutation):
    class Input:
        favorite_offer_input = ReplaceDealFavoriteOfferInputType(required=True)

    favorite_offer = graphene.Field(FavoriteOffersType)

    @staticmethod
    @authentication_required
    @transaction.atomic
    def mutate(root, info, favorite_offer_input):
        user = info.context.user
        deal_service = DealService(
            strategy=ChoseDealFavoriteOfferStrategy(),
            perms=RequestPerms(),
            validations=ChoseDealFavoriteOfferValidation(),
        )
        favorite_offer = deal_service.update_deal(
            deal_input=favorite_offer_input, user=user
        )
        return ChoseDealFavoriteOfferMutation(favorite_offer=favorite_offer)


class CreateFavoriteOfferMutation(graphene.Mutation):
    favorite_offer = graphene.Field(FavoriteOffersType)

    class Input:
        offer_input = CreateFavoriteOfferInputType(required=True)

    @staticmethod
    @authentication_required
    @transaction.atomic
    def mutate(root, info, offer_input):
        user = info.context.user
        favorite_offer_service = FavoriteOfferService(
            strategy=CreateFavoriteOfferStrategy(),
            perms=UpdateOfferPerms(),
            validations=CreateFavoriteOfferValidation(),
        )
        favorite_offer = favorite_offer_service.create_favorite_offer(
            offer_data=offer_input, user=user
        )
        return CreateFavoriteOfferMutation(favorite_offer=favorite_offer)


class DeleteFavoriteOfferMutation(graphene.Mutation):
    status = graphene.String(required=True)

    class Input:
        favorite_offer_id = graphene.Int(required=True)

    @staticmethod
    @authentication_required
    def mutate(root, info, favorite_offer_id):
        user = info.context.user
        favorite_offer_service = FavoriteOfferService(
            strategy=DeleteFavoriteOfferStrategy(),
            perms=DeleteOffersPerms(),
            validations=DeleteFavoriteOfferValidation(),
        )
        favorite_offer_service.delete_favorite_offer(
            user=user, favorite_offer_id=favorite_offer_id
        )
        return DeleteFavoriteOfferMutation(
            status=_("Favorite Offer Removed Successfully") % {}
        )


class CreateDealMutation(graphene.Mutation):
    deal = graphene.Field(DealType)

    class Input:
        deal_input = CreateDealInputType(required=True)

    @staticmethod
    @authentication_required
    @transaction.atomic
    def mutate(root, info, deal_input):
        user = info.context.user
        deal_service = DealService(
            strategy=CreateNewDealStrategy(),
            perms=CreateDealsPerms(),
            validations=CreateDealValidation(),
        )
        deal = deal_service.create_deal(
            deal_input=deal_input,
            user=user,
        )
        return CreateDealMutation(deal=deal)


class UpdateDealMutation(graphene.Mutation):
    class Input:
        deal_input = UpdateDealInputType(required=True)

    deal = graphene.Field(DealType)

    @staticmethod
    @authentication_required
    @transaction.atomic
    def mutate(root, info, deal_input):
        user = info.context.user
        deal_service = DealService(
            strategy=UpdateDealStrategy(),
            perms=UpdateDealsPerms(),
            validations=UpdateDealValidation(),
        )
        deal = deal_service.update_deal(deal_input=deal_input, user=user)
        return UpdateDealMutation(deal=deal)


class FinishDealMutation(graphene.Mutation):
    class Arguments:
        deal_input = FinishDealInputType()

    deal = graphene.Field(DealType)

    @staticmethod
    @authentication_required
    @transaction.atomic
    def mutate(root, info, deal_input: dict):
        user = info.context.user
        deal_service = DealService(
            strategy=FinishDealStrategy(),
            perms=UpdateDealsPerms(),
            validations=UpdateDealValidation(),
        )
        deal = deal_service.update_deal(deal_input=deal_input, user=user)
        return FinishDealMutation(deal=deal)


class RejectDealMutation(graphene.Mutation):
    class Arguments:
        deal_input = RejectDealInputType()

    deal = graphene.Field(DealType)

    @staticmethod
    @authentication_required
    @transaction.atomic
    def mutate(root, info, deal_input: dict):
        user = info.context.user
        deal_service = DealService(
            strategy=RejectDealStrategy(),
            perms=UpdateDealsPerms(),
            validations=UpdateDealValidation(),
        )
        deal = deal_service.update_deal(deal_input=deal_input, user=user)
        return RejectDealMutation(deal=deal)


class CreateRequestMutation(graphene.Mutation):
    class Arguments:
        request_input = RequestInputType()

    request = graphene.Field(RequestType)

    @staticmethod
    @authentication_required
    def mutate(root, info, request_input: dict):
        user = info.context.user
        request_service = RequestService(
            strategy=CreateRequestStrategy(),
            perms=RequestPerms(),
            validations=CreateRequestValidation(),
        )
        request = request_service.create_request(request_input=request_input, user=user)
        return CreateRequestMutation(request=request)


class ShareOffersWithBeneficiaryMutation(graphene.Mutation):
    class Arguments:
        task_id = graphene.Int(required=True)

    request = graphene.Field(RequestType)

    @staticmethod
    @authentication_required
    @transaction.atomic
    def mutate(root, info, task_id: int):
        user = info.context.user
        offer_service = FavoriteOfferService(
            strategy=ShareFavoriteOffersStrategy(),
            validations=ShareFavoriteOfferValidation(),
            perms=UpdateOfferPerms(),
        )
        request = offer_service.share_favorite_offers(
            user=user, offer_input={"task_id": task_id}
        )
        return ShareOffersWithBeneficiaryMutation(request=request)


class ActivateRequestMutation(graphene.Mutation):
    class Arguments:
        task_id = graphene.Int(required=True)

    request = graphene.Field(RequestType)

    @staticmethod
    @authentication_required
    def mutate(root, info, task_id: int):
        user = info.context.user
        request_service = RequestService(
            strategy=ActivateRequestStrategy(),
            validations=ActivateRequestValidation(),
            perms=RequestPerms(),
        )
        request_service = request_service.activate_request(
            user=user, request_input={"task_id": task_id}
        )
        return ActivateRequestMutation(request=request_service)


class CreateActivationNotificationMutation(graphene.Mutation):
    class Arguments:
        task_id = graphene.Int(required=True)

    notification = graphene.Field(NotificationType)

    @staticmethod
    @authentication_required
    def mutate(root, info, task_id: int):
        user = info.context.user
        context = ActivateRequestValidation().get_object_if_exists(
            user=user, request_input={"task_id": task_id}
        )
        RequestPerms().check_permissions(user=user, context=context)
        request: Request = context.get("request")
        notifications = [
            {
                "target": context["task"],
                "level": "info",
                "recipient": context["task"].negotiator,
                "actor": user,
                "data": {"request_expire": request.expired.isoformat()},
                "title": _("Activate Task Offers") % {},
                "description": _(
                    "المستفيد %(beneficiary_name)s طلب اعاده تفعيل عروض المهمة رقم %(task_id)s لمده 48 ساعه اخري"
                )
                % {
                    "beneficiary_name": context["task"].beneficiary.name,
                    "task_id": context["task"].id,
                },
            }
        ]
        notification = create_notification(notifications=notifications)
        return CreateActivationNotificationMutation(notification=notification)


class CreateAgreementMutation(graphene.Mutation):
    agreement = graphene.Field(AgreementType)

    class Input:
        agreement_input = CreateAgreementInputTpe(required=True)

    @staticmethod
    @authentication_required
    def mutate(root, info, agreement_input: dict):
        user = info.context.user
        agreement_service = AgreementService(
            strategy=CreateAgreementStrategy(),
            perms=CreateDealsPerms(),
            validations=CreateAgreementValidation(),
        )
        agreement = agreement_service.create_agreement(
            agreement_input=agreement_input,
            user=user,
        )
        return CreateAgreementMutation(agreement=agreement)


class UpdateAgreementMutation(graphene.Mutation):
    agreement = graphene.Field(AgreementType)

    class Input:
        agreement_input = UpdateAgreementInputTpe(required=True)

    @staticmethod
    @authentication_required
    def mutate(root, info, agreement_input: dict):
        user = info.context.user
        agreement_service = AgreementService(
            strategy=UpdateAgreementStrategy(),
            perms=CreateDealsPerms(),
            validations=UpdateAgreementValidation(),
        )
        agreement = agreement_service.update_agreement(
            agreement_input=agreement_input,
            user=user,
        )
        return UpdateAgreementMutation(agreement=agreement)


class FinishAgreementMutation(graphene.Mutation):
    agreement = graphene.Field(AgreementType)

    class Input:
        agreement_input = FinishAgreementInputTpe(required=True)

    @staticmethod
    @authentication_required
    def mutate(root, info, agreement_input: dict):
        user = info.context.user
        agreement_service = AgreementService(
            strategy=FinishAgreementStrategy(),
            perms=CreateDealsPerms(),
            validations=UpdateAgreementValidation(),
        )
        agreement = agreement_service.update_agreement(
            agreement_input=agreement_input,
            user=user,
        )
        return FinishAgreementMutation(agreement=agreement)


class CreateBeneficiaryMutation(graphene.Mutation):
    class Arguments:
        beneficiary_input = CreateBeneficiaryInputType()

    beneficiary = graphene.Field(BeneficiaryType)

    @staticmethod
    @authentication_required
    @transaction.atomic
    def mutate(root, info, beneficiary_input: dict):
        user = info.context.user
        beneficiary = BeneficiaryService(
            strategy=CreateBeneficiariesStrategy(),
            perms=DealsPerms(),
            validations=CreateBeneficiaryValidation(),
        )
        beneficiary = beneficiary.create_beneficiary(
            beneficiary_input=beneficiary_input,
            user=user,
        )
        return CreateBeneficiaryMutation(beneficiary=beneficiary)


class UploadBulkBeneficiariesMutation(graphene.Mutation):
    resource_management = graphene.Field(BulkBeneficiaryObjectType)

    class Arguments:
        file = graphene.String(required=True)
        name = graphene.String(required=True)

    @staticmethod
    @authentication_required
    def mutate(self, info, file: str, name: str):
        user = info.context.user
        beneficiary = BeneficiaryService(
            strategy=UploadBulkBeneficiariesStrategy(),
            perms=DealsPerms(),
            validations=UploadBulkBeneficiaryValidation(),
        )
        resource_management = beneficiary.create_bulk_beneficiary(
            user_input={"file": file, "name": name},
            user=user,
        )
        return UploadBulkBeneficiariesMutation(resource_management=resource_management)


class Mutation(graphene.ObjectType):
    create_offer_mutation = CreateOfferMutation.Field()
    update_offer_mutation = UpdateOfferMutation.Field()
    create_favorite_offer_mutation = CreateFavoriteOfferMutation.Field()
    delete_favorite_offer_mutation = DeleteFavoriteOfferMutation.Field()
    chose_deal_favorite_offer = ChoseDealFavoriteOfferMutation.Field()
    create_deal_mutation = CreateDealMutation.Field()
    update_deal_mutation = UpdateDealMutation.Field()
    finish_deal_mutation = FinishDealMutation.Field()
    reject_deal_mutation = RejectDealMutation.Field()
    create_request_mutation = CreateRequestMutation.Field()
    share_offers_with_beneficiary = ShareOffersWithBeneficiaryMutation.Field()
    activate_request = ActivateRequestMutation.Field()
    create_activation_notification = CreateActivationNotificationMutation.Field()
    create_agreement_mutation = CreateAgreementMutation.Field()
    update_agreement_mutation = UpdateAgreementMutation.Field()
    finish_agreement_mutation = FinishAgreementMutation.Field()
    create_beneficiary_mutation = CreateBeneficiaryMutation.Field()
    upload_bulk_beneficiary_mutation = UploadBulkBeneficiariesMutation.Field()
