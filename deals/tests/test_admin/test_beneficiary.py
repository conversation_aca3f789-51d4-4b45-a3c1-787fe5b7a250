from django.contrib.admin import AdminSite
from django.http import HttpRequest
from django.test import TestCase

from deals.admin import BeneficiaryAdmin
from deals.models import Beneficiary
from utils.tests.factories import UserFactory, BeneficiaryFactory


class MockRequest(HttpRequest):
    def __init__(self, user=None):
        super().__init__()
        self.user = user


class BeneficiaryModelAdminTest(TestCase):
    def setUp(self):
        self.site = AdminSite()
        self.admin_beneficiary = BeneficiaryAdmin(Beneficiary, self.site)
        self.user = UserFactory(is_superuser=True)
        self.request = MockRequest(user=self.user)
        self.beneficiary = BeneficiaryFactory()

    def test_beneficiary_admin_registration(self):
        self.assertIsNotNone(self.admin_beneficiary)

    def test_list_display(self):
        # Check if the list_display is correctly defined in the admin class
        expected_list_display = ["id", "name"]
        self.assertEqual(self.admin_beneficiary.list_display, expected_list_display)

    def test_search_fields(self):
        # Check if the search fields are correctly defined
        expected_search_fields = ["name", "code"]
        self.assertEqual(self.admin_beneficiary.search_fields, expected_search_fields)

    def test_queryset(self):
        # Test that the correct queryset is returned
        queryset = self.admin_beneficiary.get_queryset(self.request)
        self.assertIn(self.beneficiary, queryset)

    def test_has_view_permission(self):
        # Test the view permission logic
        self.assertTrue(self.admin_beneficiary.has_view_permission(self.request))

    def test_has_add_permission(self):
        # Test the add permission logic
        self.assertFalse(self.admin_beneficiary.has_add_permission(self.request))

    def test_has_change_permission(self):
        # Test the change permission logic
        self.assertFalse(self.admin_beneficiary.has_change_permission(self.request))

    def test_has_delete_permission(self):
        # Test the delete permission logic
        self.assertFalse(self.admin_beneficiary.has_delete_permission(self.request))

    def test_get_fields(self):
        self.assertListEqual(
            self.admin_beneficiary.get_fields(self.request),
            ["user", "data_source", "code", "external_id", "name", "data"],
        )
