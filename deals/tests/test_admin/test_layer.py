from django.contrib.admin import AdminSite
from django.http import HttpRequest
from django.test import TestCase

from deals.admin import LayerAdmin
from deals.models import Layer
from utils.tests.factories import UserFactory, LayerFactory


class MockRequest(HttpRequest):
    def __init__(self, user=None):
        super().__init__()
        self.user = user


class BeneficiaryModelAdminTest(TestCase):
    def setUp(self):
        self.site = AdminSite()
        self.admin_layer = LayerAdmin(Layer, self.site)
        self.user = UserFactory(is_superuser=True)
        self.request = MockRequest(user=self.user)
        self.layer = LayerFactory()

    def test_layer_admin_registration(self):
        self.assertIsNotNone(self.admin_layer)

    def test_list_display(self):
        # Check if the list_display is correctly defined in the admin class
        expected_list_display = ("__str__", "id", "key")
        self.assertEqual(self.admin_layer.list_display, expected_list_display)

    def test_search_fields(self):
        # Check if the search fields are correctly defined
        expected_search_fields = ("id", "key", "title")
        self.assertEqual(self.admin_layer.search_fields, expected_search_fields)

    def test_queryset(self):
        # Test that the correct queryset is returned
        queryset = self.admin_layer.get_queryset(self.request)
        self.assertIn(self.layer, queryset)

    def test_has_view_permission(self):
        # Test the view permission logic
        self.assertTrue(self.admin_layer.has_view_permission(self.request))

    def test_has_add_permission(self):
        # Test the add permission logic
        self.assertTrue(self.admin_layer.has_add_permission(self.request))

    def test_has_change_permission(self):
        # Test the change permission logic
        self.assertTrue(self.admin_layer.has_change_permission(self.request))

    def test_has_delete_permission(self):
        # Test the delete permission logic
        self.assertTrue(self.admin_layer.has_delete_permission(self.request))

    def test_get_fields(self):
        self.assertListEqual(
            self.admin_layer.get_fields(self.request),
            ["key", "title", "description", "metadata", "boundaries"],
        )
        self.assertListEqual(
            self.admin_layer.get_fields(self.request, self.layer),
            ["title", "description", "metadata", "boundaries", "key"],
        )
