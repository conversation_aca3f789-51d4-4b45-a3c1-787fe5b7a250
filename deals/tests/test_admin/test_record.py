from django.contrib.admin import AdminSite
from django.http import HttpRequest
from django.test import TestCase

from deals.admin import RecordAdmin
from deals.models import Record
from utils.tests.factories import UserFactory, RecordFactory


class MockRequest(HttpRequest):
    def __init__(self, user=None):
        super().__init__()
        self.user = user


class RecordModelAdminTest(TestCase):
    def setUp(self):
        self.site = AdminSite()
        self.admin_record = RecordAdmin(Record, self.site)
        self.user = UserFactory(is_superuser=True)
        self.request = MockRequest(user=self.user)
        self.record = RecordFactory()

    def test_layer_admin_registration(self):
        self.assertIsNotNone(self.admin_record)

    def test_list_display(self):
        # Check if the list_display is correctly defined in the admin class
        expected_list_display = ("__str__", "layer", "id")
        self.assertEqual(self.admin_record.list_display, expected_list_display)

    def test_search_fields(self):
        # Check if the search fields are correctly defined
        expected_search_fields = ("id",)
        self.assertEqual(self.admin_record.search_fields, expected_search_fields)

    def test_queryset(self):
        # Test that the correct queryset is returned
        queryset = self.admin_record.get_queryset(self.request)
        self.assertIn(self.record, queryset)

    def test_has_view_permission(self):
        # Test the view permission logic
        self.assertTrue(self.admin_record.has_view_permission(self.request))

    def test_has_add_permission(self):
        # Test the add permission logic
        self.assertFalse(self.admin_record.has_add_permission(self.request))

    def test_has_change_permission(self):
        # Test the change permission logic
        self.assertTrue(self.admin_record.has_change_permission(self.request))

    def test_has_delete_permission(self):
        # Test the delete permission logic
        self.assertFalse(self.admin_record.has_delete_permission(self.request))

    def test_get_fields(self):
        self.assertListEqual(
            self.admin_record.get_fields(self.request),
            ["layer", "geometry", "source_properties"],
        )
        self.assertListEqual(
            self.admin_record.get_fields(self.request, self.record),
            ["layer", "geometry", "source_properties"],
        )
