from django.contrib.admin import AdminSite
from django.http import HttpRequest
from django.test import TestCase

from deals.admin import OfferAdmin
from deals.models import FavoriteOffer
from users.models import UserRoleChoices
from utils.tests.factories import (
    UserFactory,
    OrderFactory,
    RenRoleFactory,
    BeneficiaryFactory,
    TaskFactory,
    FavoriteOfferFactory,
)


class MockRequest(HttpRequest):
    def __init__(self, user=None):
        super().__init__()
        self.user = user


class BeneficiaryModelAdminTest(TestCase):
    def setUp(self):
        self.site = AdminSite()
        self.admin_offer = OfferAdmin(FavoriteOffer, self.site)
        self.user = UserFactory(is_superuser=True)
        self.request = MockRequest(user=self.user)
        self.beneficiary = BeneficiaryFactory()
        self.order = OrderFactory(beneficiaries=[self.beneficiary])
        self.negotiator = UserFactory(
            roles=[RenRoleFactory(role=UserRoleChoices.NEGOTIATORS)]
        )
        self.customer_service = UserFactory(
            roles=[RenRoleFactory(rolle=UserRoleChoices.CUSTOMER_SERVICES)]
        )
        self.task = TaskFactory(
            order=self.order,
            beneficiary=self.beneficiary,
            customer_service=self.customer_service,
            negotiator=self.negotiator,
        )
        self.favorite_offer = FavoriteOfferFactory(task=self.task, offer_id=100)

    def test_deal_admin_registration(self):
        self.assertIsNotNone(self.admin_offer)

    def test_list_display(self):
        # Check if the list_display is correctly defined in the admin class
        expected_list_display = (
            "id",
            "external_offer_id",
            "status",
        )
        self.assertEqual(self.admin_offer.list_display, expected_list_display)

    def test_search_fields(self):
        # Check if the search fields are correctly defined
        expected_search_fields = ("id", "license_number")
        self.assertEqual(self.admin_offer.search_fields, expected_search_fields)

    def test_queryset(self):
        # Test that the correct queryset is returned
        queryset = self.admin_offer.get_queryset(self.request)
        self.assertIn(self.favorite_offer, queryset)

    def test_has_view_permission(self):
        # Test the view permission logic
        self.assertTrue(self.admin_offer.has_view_permission(self.request))

    def test_has_add_permission(self):
        # Test the add permission logic
        self.assertFalse(self.admin_offer.has_add_permission(self.request))

    def test_has_change_permission(self):
        # Test the change permission logic
        self.assertFalse(self.admin_offer.has_change_permission(self.request))

    def test_has_delete_permission(self):
        # Test the delete permission logic
        self.assertFalse(self.admin_offer.has_delete_permission(self.request))

    def test_get_fields(self):

        self.assertListEqual(
            self.admin_offer.get_fields(self.request, self.favorite_offer),
            ["task", "offer_id", "reserved"],
        )
