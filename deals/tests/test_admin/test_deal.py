from django.contrib.admin import AdminSite
from django.http import HttpRequest
from django.test import TestCase

from deals.admin import DealAdmin
from deals.models import Deal
from users.models import UserRoleChoices
from utils.tests.factories import (
    UserFactory,
    DealFactory,
    FavoriteOfferFactory,
    TaskFactory,
    BeneficiaryFactory,
    OrderFactory,
    RenRoleFactory,
)


class MockRequest(HttpRequest):
    def __init__(self, user=None):
        super().__init__()
        self.user = user


class BeneficiaryModelAdminTest(TestCase):
    def setUp(self):
        self.site = AdminSite()
        self.admin_deal = DealAdmin(Deal, self.site)
        self.user = UserFactory(is_superuser=True)
        self.request = MockRequest(user=self.user)
        self.beneficiary = BeneficiaryFactory()
        self.order = OrderFactory(beneficiaries=[self.beneficiary])
        self.negotiator = UserFactory(
            roles=[RenRoleFactory(role=UserRoleChoices.NEGOTIATORS)]
        )
        self.customer_service = UserFactory(
            roles=[RenRoleFactory(rolle=UserRoleChoices.CUSTOMER_SERVICES)]
        )
        self.task = TaskFactory(
            order=self.order,
            beneficiary=self.beneficiary,
            customer_service=self.customer_service,
            negotiator=self.negotiator,
        )
        self.favorite_offer = FavoriteOfferFactory(task=self.task, offer_id=100)
        self.deal = DealFactory(favorite_offer=self.favorite_offer)

    def test_deal_admin_registration(self):
        self.assertIsNotNone(self.admin_deal)
        self.assertEqual(str(self.deal), f"{self.favorite_offer} - {self.deal.id}")

    def test_list_display(self):
        # Check if the list_display is correctly defined in the admin class
        expected_list_display = (
            "id",
            "favorite_offer",
            "status",
            "created",
            "modified",
        )
        self.assertEqual(self.admin_deal.list_display, expected_list_display)

    def test_search_fields(self):
        # Check if the search fields are correctly defined
        expected_search_fields = ("id", "favorite_offer__license_number")
        self.assertEqual(self.admin_deal.search_fields, expected_search_fields)

    def test_queryset(self):
        # Test that the correct queryset is returned
        queryset = self.admin_deal.get_queryset(self.request)
        self.assertIn(self.deal, queryset)

    def test_has_view_permission(self):
        # Test the view permission logic
        self.assertTrue(self.admin_deal.has_view_permission(self.request))

    def test_has_add_permission(self):
        # Test the add permission logic
        self.assertFalse(self.admin_deal.has_add_permission(self.request))

    def test_has_change_permission(self):
        # Test the change permission logic
        self.assertFalse(self.admin_deal.has_change_permission(self.request))

    def test_has_delete_permission(self):
        # Test the delete permission logic
        self.assertFalse(self.admin_deal.has_delete_permission(self.request))

    def test_get_fields(self):
        self.assertListEqual(
            self.admin_deal.get_fields(self.request),
            ["favorite_offer", "status", "form_data"],
        )
        self.assertListEqual(
            self.admin_deal.get_fields(self.request, self.deal),
            ["favorite_offer", "status", "form_data"],
        )
