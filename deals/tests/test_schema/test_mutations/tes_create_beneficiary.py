from unittest.mock import MagicMock

from deals.tests.utils import BaseTestQueries
from utils.tests.factories import (
    FormSchemaFactory,
    BeneficiaryFactory,
)


class CreateBeneficiaryMutationTestCase(BaseTestQueries):
    def setUp(self):
        super().setUp()
        self.superuser_auth_request = MagicMock()
        self.superuser_auth_request.user = self.superuser
        self.form_schema = FormSchemaFactory(
            key="add-beneficiaries",
            json_schema={
                "form": {
                    "type": "object",
                    "required": ["destination"],
                    "properties": {
                        "destination": {
                            "enum": ["صندوق التنمية", "مستفيد خارجي"],
                            "type": "string",
                            "title": "جهة الطلب",
                        },
                        "personalData": {
                            "type": "object",
                            "title": "البيانات الشخصية",
                            "required": ["name", "ageHijri", "mobileNumber"],
                            "properties": {
                                "name": {"type": "string", "title": "الاسم"},
                                "ageHijri": {
                                    "type": "integer",
                                    "title": "العمر (هجري)",
                                    "minimum": 18,
                                },
                                "mobileNumber": {
                                    "type": "string",
                                    "title": "رقم الجوال",
                                    "pattern": "^5\\d{8}$",
                                },
                            },
                        },
                        "financialData": {
                            "type": "object",
                            "title": "البيانات  الشخصية المالية",
                            "required": ["salary", "salaryBank", "monthlyDeduction"],
                            "propertieas": {
                                "salary": {
                                    "type": "number",
                                    "title": "الراتب",
                                    "minimum": 0,
                                },
                                "salaryBank": {
                                    "enum": [
                                        "البنك الأهلي السعودي",
                                        "مصرف الراجحي",
                                        "بنك الرياض",
                                        "بنك ساب",
                                        "البنك العربي الوطني",
                                        "مصرف الانماء",
                                        "البنك السعودي للاستثمار",
                                        "البنك السعودي الفرنسي",
                                        "بنك الجزيرة",
                                        "بنك البلاد",
                                        "بنك الخليج الدولي",
                                        "بنك الامارات",
                                        "بنك أبو ظبي الأول",
                                    ],
                                    "type": "string",
                                    "title": "بنك الراتب",
                                },
                                "monthlyDeduction": {
                                    "type": "number",
                                    "title": "الاستقطاع الشهري",
                                },
                            },
                        },
                        "supportPackages": {
                            "type": "object",
                            "title": "باقات الدعم",
                            "required": ["product"],
                            "properties": {
                                "product": {
                                    "enum": [
                                        "البناء الذاتي",
                                        "الوحدات الجاهزة",
                                        "البيع على الخارطة",
                                    ],
                                    "type": "string",
                                    "title": "المنتج",
                                }
                            },
                        },
                        "professionalData": {
                            "type": "object",
                            "title": "البيانات الشخصية المهنية",
                            "required": ["job"],
                            "properties": {
                                "job": {
                                    "enum": [
                                        "حكومي مدني",
                                        "خاص",
                                        "عسكري",
                                        "عمل حر",
                                        "متقاعد",
                                        "مدني",
                                        "موظف بنك",
                                    ],
                                    "type": "string",
                                    "title": "الوظيفة",
                                }
                            },
                        },
                        "personalRealEstateData": {
                            "type": "object",
                            "title": "البيانات الشخصية العقارية",
                            "required": [
                                "hasLand",
                                "hasBuildingLicense",
                                "startedBuilding",
                            ],
                            "properties": {
                                "hasLand": {
                                    "enum": ["نعم", "لا"],
                                    "type": "string",
                                    "title": "لديه ارض",
                                },
                                "startedBuilding": {
                                    "enum": ["نعم", "لا"],
                                    "type": "string",
                                    "title": "بدأ بالبناء",
                                },
                                "hasBuildingLicense": {
                                    "enum": ["نعم", "لا"],
                                    "type": "string",
                                    "title": "لديه رخصة بناء",
                                },
                            },
                        },
                    },
                },
                "UISchema": {},
            },
        )
        self.mutation = """
            mutation MyMutation($formData: JSONString!, $formSchema: String!) {
              createBeneficiaryMutation(
                beneficiaryInput: {formSchema: $formSchema, formData: $formData}
              ) {
                beneficiary {
                  assignedTaskStatus
                  code
                  externalId
                  id
                  name
                  taskId
                  taskStatus
                  beneficiaryData {
                    age
                    avgFund
                    city
                    destination
                    familyMember
                    job
                    mobile
                    product
                    region
                    salary
                    salaryBank
                  }
                }
              }
            }
        """
        self.variables = {
            "formSchema": self.form_schema.key,
            "formData": '{   "destination": "صندوق التنمية",   "personalData": {     "name": "dfgdfgdfgfdfdfg6",     "ageHijri": 20,     "mobileNumber": "*********"   },   "professionalData": {     "job": "خاص"   },   "financialData": {     "salary": **********,     "salaryBank": "بنك ساب",     "monthlyDeduction": **********   },   "personalRealEstateData": {     "hasLand": "نعم",     "startedBuilding": "نعم",     "hasBuildingLicense": "نعم"   },   "supportPackages": {     "product": "البناء الذاتي"   } }',
        }

    def test_valid_mutation(self):
        result = self.client.execute(
            self.mutation, variables=self.variables, context=self.superuser_auth_request
        )
        self.assertNotIn("errors", result)
        beneficiary = result["data"]["createBeneficiaryMutation"]["beneficiary"]
        self.assertDictEqual(
            beneficiary,
            {
                "assignedTaskStatus": None,
                "code": None,
                "externalId": None,
                "id": 3,
                "name": "dfgdfgdfgfdfdfg6",
                "taskId": None,
                "taskStatus": None,
                "beneficiaryData": {
                    "age": 20,
                    "avgFund": None,
                    "city": None,
                    "destination": "صندوق التنمية",
                    "familyMember": None,
                    "job": "خاص",
                    "mobile": "*********",
                    "product": "البناء الذاتي",
                    "region": None,
                    "salary": **********,
                    "salaryBank": "بنك ساب",
                },
            },
        )

    def test_mutation_with_exists_beneficiary_data(self):
        BeneficiaryFactory(data={"personalData": {"mobileNumber": "*********"}})
        result = self.client.execute(
            self.mutation, variables=self.variables, context=self.superuser_auth_request
        )
        self.assertIn("errors", result)
        errors = result["errors"][0]
        self.assertEqual(errors["message"], "Bad Request")
        self.assertDictEqual(
            errors["extensions"]["http"]["reason"],
            {"mobile": "Beneficiary already exists"},
        )
        BeneficiaryFactory(data={"personalData": {"mobileNumber": "1"}})
        result = self.client.execute(
            self.mutation, variables=self.variables, context=self.superuser_auth_request
        )
        self.assertIn("errors", result)
        errors = result["errors"][0]
        self.assertEqual(errors["message"], "Bad Request")
        self.assertDictEqual(
            errors["extensions"]["http"]["reason"],
            {"mobile": "Beneficiary already exists"},
        )
