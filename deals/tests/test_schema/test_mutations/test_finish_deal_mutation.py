from unittest.mock import patch

from deals.models import DealStatus
from deals.tests.utils import BaseDealsTestCase
from utils.tests.factories import (
    FavoriteOfferFactory,
    FormSchemaFactory,
    DealFactory,
    completed_offers_example,
)


class FinishDealMutationTestCase(BaseDealsTestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()  # Call parent setup if needed
        cls.patcher = patch("utils.locations.Locations.get_makan_offers")

        cls.mock_fetch_offer_details = cls.patcher.start()
        cls.mock_fetch_offer_details.return_value = [completed_offers_example()]

    def setUp(self):
        super().setUp()
        self.offer_id = 100
        self.favorite_offer = FavoriteOfferFactory(
            task=self.task, offer_id=self.offer_id
        )
        self.deal = DealFactory(
            favorite_offer=self.favorite_offer, form_data={"test": "test"}
        )
        self.form_schema = FormSchemaFactory(key="deal_schema")
        self.mutation = """
            mutation MyMutation($deal_id: Int!, $form_schema_key: String!) {
              finishDealMutation(dealInput: {dealId: $deal_id, formSchemaKey: $form_schema_key}) {
                deal {
                  id
                  formData
                  status

                }
              }
           }
        """

    def test_valid_finish_deal_mutation(self):
        variables = {"deal_id": self.deal.id, "form_schema_key": self.form_schema.key}
        result = self.client.execute(
            self.mutation, variables=variables, context=self.auth_request
        )
        self.deal.refresh_from_db()
        self.assertNotIn("errors", result)
        self.assertEqual(
            result["data"]["finishDealMutation"]["deal"]["id"], f"{self.deal.id}"
        )
        self.assertDictEqual(
            result["data"]["finishDealMutation"]["deal"]["status"],
            {"key": DealStatus.DEAL_FINISHED.value, "display": "Deal Finished"},
        )
        self.assertEqual(self.deal.status, DealStatus.DEAL_FINISHED)

    def test_finish_deal_mutation_with_wrong_deal_id(self):
        wrong_deal_id = self.deal.id + 1
        variables = {"deal_id": wrong_deal_id, "form_schema_key": self.form_schema.key}
        result = self.client.execute(
            self.mutation, variables=variables, context=self.auth_request
        )
        self.assertIn("errors", result)
        self.assertEqual(result["errors"][0]["message"], "Not Found")
        self.assertEqual(result["errors"][0]["extensions"]["http"]["status"], 404)
        self.assertEqual(
            result["errors"][0]["extensions"]["http"]["status_text"], "Not Found"
        )
        self.assertDictEqual(
            result["errors"][0]["extensions"]["http"]["reason"],
            {"deal": f"Deal {wrong_deal_id} not found"},
        )

    def test_finish_deal_when_negotiator_has_no_permissions(self):
        self.task.negotiator = None
        self.task.save(update_fields=["negotiator"])
        variables = {"deal_id": self.deal.id, "form_schema_key": self.form_schema.key}
        result = self.client.execute(
            self.mutation, variables=variables, context=self.auth_request
        )
        self.assertIn("errors", result)
        self.assertEqual(result["errors"][0]["message"], "Forbidden")
        self.assertDictEqual(
            result["errors"][0]["extensions"]["http"]["reason"],
            {"user": "Permission Denied"},
        )

    def test_finish_deal_when_form_schema_not_found(self):
        variables = {"deal_id": self.deal.id, "form_schema_key": "tttttt"}
        result = self.client.execute(
            self.mutation, variables=variables, context=self.auth_request
        )
        self.assertIn("errors", result)
        self.assertEqual(result["errors"][0]["message"], "Not Found")
        self.assertDictEqual(
            result["errors"][0]["extensions"]["http"]["reason"],
            {"formSchemaKey": "invalid Form Schema key"},
        )

    def test_finish_deal_when_deal_is_cancelled(self):
        self.deal.status = DealStatus.DEAL_CANCELLED
        self.deal.save(update_fields=["status"])
        variables = {"deal_id": self.deal.id, "form_schema_key": self.form_schema.key}
        result = self.client.execute(
            self.mutation, variables=variables, context=self.auth_request
        )
        self.assertIn("errors", result)
        self.assertEqual(result["errors"][0]["message"], "Bad Request")
        self.assertDictEqual(
            result["errors"][0]["extensions"]["http"]["reason"],
            {"status": "can't cancel deal yet"},
        )

    def test_finish_deal_when_deal_is_finished(self):
        self.deal.status = DealStatus.DEAL_FINISHED
        self.deal.save(update_fields=["status"])
        variables = {"deal_id": self.deal.id, "form_schema_key": self.form_schema.key}
        result = self.client.execute(
            self.mutation, variables=variables, context=self.auth_request
        )
        self.assertIn("errors", result)
        self.assertEqual(result["errors"][0]["message"], "Bad Request")
        self.assertDictEqual(
            result["errors"][0]["extensions"]["http"]["reason"],
            {"status": "can't Finish Deal yet"},
        )

    def test_finish_deal_when_deal_has_no_form_data(self):
        self.deal.form_data = None
        self.deal.save(update_fields=["form_data"])
        variables = {"deal_id": self.deal.id, "form_schema_key": self.form_schema.key}
        result = self.client.execute(
            self.mutation, variables=variables, context=self.auth_request
        )
        self.assertIn("errors", result)
        self.assertEqual(result["errors"][0]["message"], "Bad Request")
        self.assertDictEqual(
            result["errors"][0]["extensions"]["http"]["reason"],
            {"formData": "Fulfill Form Data of Deal first"},
        )

    def test_finish_deal_when_deal_form_data_has_drafts(self):
        self.deal.form_data = {"drafts": {"test": "test"}}
        self.deal.save(update_fields=["form_data"])
        variables = {"deal_id": self.deal.id, "form_schema_key": self.form_schema.key}
        result = self.client.execute(
            self.mutation, variables=variables, context=self.auth_request
        )
        self.assertIn("errors", result)
        self.assertEqual(result["errors"][0]["message"], "Bad Request")
        self.assertDictEqual(
            result["errors"][0]["extensions"]["http"]["reason"],
            {"formData": "remove drafts first before finish deal"},
        )

    def test_finish_deal_when_form_has_invalid_schema(self):
        self.deal.form_data = {"contact": "contact"}
        self.deal.save(update_fields=["form_data"])
        self.form_schema.json_schema = {
            "form": {
                "type": "object",
                "$schema": "http://json-schema.org/draft-07/schema#",
                "properties": {"contact": {"$ref": "#/definitions/contact"}},
                "definitions": {
                    "contact": {
                        "type": "object",
                        "title": "التواصل",
                        "required": ["callStatus"],
                        "properties": {
                            "callStatus": {
                                "enum": ["تم الرد", "لم يتم الرد"],
                                "type": "string",
                                "title": "حالة الاتصال",
                                "default": "تم الرد",
                            }
                        },
                    }
                },
            },
            "UISchema": {},
        }
        self.form_schema.save(update_fields=["json_schema"])
        variables = {"deal_id": self.deal.id, "form_schema_key": self.form_schema.key}
        result = self.client.execute(
            self.mutation, variables=variables, context=self.auth_request
        )
        self.assertIn("errors", result)
        self.assertEqual(result["errors"][0]["message"], "Bad Request")
        self.assertDictEqual(
            result["errors"][0]["extensions"]["http"]["reason"],
            {
                "formData": "JsonSchema Validation Error: 'contact' is not of type 'object'"
            },
        )
