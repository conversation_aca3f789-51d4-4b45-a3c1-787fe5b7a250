from unittest.mock import patch

from django.utils.translation import gettext_lazy as _

from deals.models import FavoriteOffer
from deals.tests.utils import BaseDealsTestCase
from users.models import UserRoleChoices
from utils.tests.factories import (
    FavoriteOfferFactory,
    UserFactory,
    RenRoleFactory,
)
from utils.tests.factories import FormSchemaFactory


class CreateFavoriteOfferMutationTestCase(BaseDealsTestCase):
    def setUp(self):
        super().setUp()
        factory = FormSchemaFactory(json_schema={"form": {}, "key": "test_key"})
        self.form_schema = factory
        self.mutation = """
        mutation MyMutation($OfferId: ID!, $TaskId: Int!) {
          createFavoriteOfferMutation(offerInput: {offerId: $OfferId, taskId: $TaskId}) {
              favoriteOffer {
              offerId
              taskId
            }
          }
        }
        """
        self.offer_id = 100
        self.variables = {
            "OfferId": str(self.offer_id),
            "TaskId": self.task.id,
        }

    def test_valid_create_favorite_offer(self):
        self.assertEqual(FavoriteOffer.objects.count(), 0)
        result = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        self.assertNotIn("errors", result)
        self.assertTrue(
            result.get("data", {})
            .get("createFavoriteOfferMutation", {})
            .get("favoriteOffer")
        )
        data = result["data"]["createFavoriteOfferMutation"]["favoriteOffer"]
        self.assertEqual(data["taskId"], self.task.id)
        self.assertEqual(data["offerId"], str(self.offer_id))

    def test_create_offer_with_anonymous_user(self):
        result = self.client.execute(
            self.mutation, variables=self.variables, context=self.non_auth_request
        )
        self.assertIn("errors", result)
        self.assertEqual(result["errors"][0]["message"], "Unauthorized")
        self.assertEqual(result["errors"][0]["extensions"]["http"]["status"], 401)
        self.assertEqual(
            result["errors"][0]["extensions"]["http"]["status_text"], "Unauthorized"
        )

    def test_create_offer_with_non_authorized_user(self):
        result = self.client.execute(
            self.mutation, variables=self.variables, context=self.non_authorized_request
        )
        self.assertIn("errors", result)
        self.assertEqual(result["errors"][0]["message"], "Forbidden")
        self.assertEqual(result["errors"][0]["extensions"]["http"]["status"], 403)
        self.assertEqual(
            result["errors"][0]["extensions"]["http"]["status_text"], "Forbidden"
        )
        self.assertDictEqual(
            result["errors"][0]["extensions"]["http"]["reason"],
            {"user": "Permission Denied"},
        )

    def test_create_favorite_offer_with_invalid_task(self):
        self.variables["TaskId"] = 0
        result = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        self.assertIn("errors", result)
        errors = result["errors"][0]
        self.assertEqual(errors["message"], "Not Found")
        self.assertDictEqual(
            errors["extensions"]["http"]["reason"],
            {"task": "task matching query doesn't found"},
        )

    def test_create_duplicated_favorite_offer(self):
        FavoriteOfferFactory(offer_id=self.offer_id, task=self.task)
        result = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        self.assertIn("errors", result)
        errors = result["errors"][0]
        self.assertEqual(errors["message"], "Not Found")
        self.assertDictEqual(
            errors["extensions"]["http"]["reason"],
            {"offer": "task already have this offer"},
        )

    def test_create_favorite_offer_with_invalid_permission(self):
        negotiator = UserFactory(
            roles=[RenRoleFactory(role=UserRoleChoices.NEGOTIATORS)]
        )
        self.auth_request.user = negotiator
        result = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        self.assertIn("errors", result)
        errors = result["errors"][0]
        self.assertEqual(errors["message"], "Forbidden")
        self.assertDictEqual(
            errors["extensions"]["http"]["reason"], {"user": "Permission Denied"}
        )

    def test_mutation_if_task_has_many_favorite_offers(self):
        FavoriteOfferFactory.create_batch(2, task=self.task, offer_id=1)
        result = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        self.assertIn("errors", result)
        errors = result["errors"][0]
        self.assertDictEqual(
            errors["extensions"]["http"]["reason"],
            {
                "offers": _("task already have %(task_favorite_offers_count)s offers")
                % {"task_favorite_offers_count": self.task.favorite_offers.count()}
            },
        )

    @patch(
        "deals.serializers.offers_serializer.CreateFavoriteOfferSerializer.is_valid",
        return_value=False,
    )
    def test_mutation_with_invalid_serializer(self, mocked_serializer):
        result = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        self.assertIn("errors", result)
