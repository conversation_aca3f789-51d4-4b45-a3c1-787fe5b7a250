from unittest.mock import patch

from django.utils.translation import gettext_lazy as _

from deals.models import Deal, DealStatus, RequestStatus
from deals.tests.utils import BaseDealsTestCase
from orders.models import TaskStatus
from utils.tests.factories import (
    FavoriteOfferFactory,
    DealFactory,
    RequestFactory,
    completed_offers_example,
)


class CreateDealMutationTestCase(BaseDealsTestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()  # Call parent setup if needed
        cls.patcher = patch("utils.locations.Locations.get_makan_offers")
        cls.mock_fetch_offer_details = cls.patcher.start()
        cls.mock_fetch_offer_details.return_value = [completed_offers_example()]

    def setUp(self):
        super().setUp()
        self.offer_id = 100
        self.request = RequestFactory(task=self.task, status=RequestStatus.ACCEPTED)
        self.task.form_data = {
            "customer_service": {"beneficiaryData": {"test": "test"}},
            "negotiator": {
                "placeOrder": {"test": "test"},
                "negotiatorData": {"test": "test"},
            },
        }
        self.task.status = TaskStatus.NEGOTIATE_WITH_OWNER
        self.task.save(update_fields=["form_data", "status"])
        self.favorite_offer = FavoriteOfferFactory(
            task=self.task, offer_id=self.offer_id, reserved=True
        )
        self.mutation = """
            mutation MyMutation($favorite_offer_id: Int!){
              createDealMutation(dealInput: {favoriteOfferId: $favorite_offer_id}) {
                deal {
                  id
                  formData
                  status
                }
              }
            }
        """
        self.variables = {
            "favorite_offer_id": self.favorite_offer.id,
        }

    def test_valid_create_deal(self):
        self.assertEqual(
            Deal.objects.filter(favorite_offer=self.favorite_offer).count(), 0
        )
        result = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        self.assertNotIn("errors", result)
        created_deal = getattr(self.favorite_offer, "deal", None)
        self.assertIsNotNone(created_deal)
        self.assertEqual(
            f"{created_deal.id}", result["data"]["createDealMutation"]["deal"]["id"]
        )
        self.assertIsNone(result["data"]["createDealMutation"]["deal"]["formData"])
        self.assertDictEqual(
            result["data"]["createDealMutation"]["deal"]["status"],
            {"key": DealStatus.CHOOSING_OFFER, "display": "Choosing Offer"},
        )

    def test_create_deal_with_wrong_user_permissions(self):
        variables = {
            "favorite_offer_id": self.favorite_offer.id,
            "task_id": self.task.id,
        }
        self.auth_request.user = self.customer_service
        result = self.client.execute(
            self.mutation, variables=variables, context=self.auth_request
        )
        self.assertIn("errors", result)
        self.assertEqual(result["errors"][0]["message"], _("Forbidden"))

    def test_create_deal_with_wrong_favorite_offer_id(self):
        wrong_favorite_offer_id = 2231
        variables = {
            "favorite_offer_id": wrong_favorite_offer_id,
            "task_id": self.task.id,
        }
        result = self.client.execute(
            self.mutation, variables=variables, context=self.auth_request
        )
        self.assertIn("errors", result)
        self.assertEqual(
            Deal.objects.filter(favorite_offer=self.favorite_offer).count(), 0
        )
        self.assertEqual(result["errors"][0]["message"], "Not Found")
        self.assertEqual(result["errors"][0]["extensions"]["http"]["status"], 404)
        self.assertEqual(
            result["errors"][0]["extensions"]["http"]["status_text"], "Not Found"
        )
        self.assertDictEqual(
            result["errors"][0]["extensions"]["http"]["reason"],
            {"favoriteOfferId": f"Favorite Offer {wrong_favorite_offer_id} not found"},
        )

    def test_create_deal_when_task_has_ongoing_deal(self):
        new_favorite_offer = FavoriteOfferFactory(task=self.task, offer_id=101)
        deal = DealFactory(
            favorite_offer=new_favorite_offer, status=DealStatus.CHOOSING_OFFER
        )
        variables = {
            "favorite_offer_id": self.favorite_offer.id,
            "task_id": self.task.id,
        }
        self.assertEqual(
            Deal.objects.filter(favorite_offer=self.favorite_offer).count(), 0
        )
        result = self.client.execute(
            self.mutation, variables=variables, context=self.auth_request
        )
        self.assertEqual(result["errors"][0]["message"], "Bad Request")
        self.assertEqual(result["errors"][0]["extensions"]["http"]["status"], 400)
        self.assertEqual(
            result["errors"][0]["extensions"]["http"]["status_text"], "Bad Request"
        )
        self.assertDictEqual(
            result["errors"][0]["extensions"]["http"]["reason"],
            {"deal": f"Deal for task {deal.favorite_offer.task.id} already exists"},
        )

    def test_create_deal_when_task_has_ongoing_deal_with_the_same_favorite_offer(self):
        DealFactory(
            favorite_offer=self.favorite_offer, status=DealStatus.CHOOSING_OFFER
        )
        variables = {
            "favorite_offer_id": self.favorite_offer.id,
            "task_id": self.task.id,
        }
        result = self.client.execute(
            self.mutation, variables=variables, context=self.auth_request
        )
        self.assertEqual(result["errors"][0]["message"], "Bad Request")
        self.assertEqual(result["errors"][0]["extensions"]["http"]["status"], 400)
        self.assertEqual(
            result["errors"][0]["extensions"]["http"]["status_text"], "Bad Request"
        )
        self.assertDictEqual(
            result["errors"][0]["extensions"]["http"]["reason"],
            {
                "favoriteOffer": _(
                    "Favorite offer %(favorite_offer_id)s already have deal"
                )
                % {"favorite_offer_id": self.favorite_offer.id}
            },
        )

    def test_create_deal_when_task_form_data_missing_required_keys(self):
        self.task.form_data = None
        self.task.save(update_fields=["form_data"])
        variables = {
            "favorite_offer_id": self.favorite_offer.id,
            "task_id": self.task.id,
        }
        result = self.client.execute(
            self.mutation, variables=variables, context=self.auth_request
        )
        self.assertEqual(result["errors"][0]["message"], "Bad Request")
        self.assertEqual(result["errors"][0]["extensions"]["http"]["status"], 400)
        self.assertEqual(
            result["errors"][0]["extensions"]["http"]["status_text"], "Bad Request"
        )
        self.assertDictEqual(
            result["errors"][0]["extensions"]["http"]["reason"],
            {
                "formData": _(
                    f"Key 'placeOrder' is missing or empty in task {self.task.id} form data"
                )
                % {}
            },
        )
