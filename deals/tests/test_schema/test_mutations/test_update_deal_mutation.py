import json
from unittest.mock import patch

from deals.models import DealStatus
from deals.tests.utils import BaseDealsTestCase
from utils.tests.factories import (
    FavoriteOfferFactory,
    DealFactory,
    FormSchemaFactory,
    completed_offers_example,
)


class TestUpdateDealMutation(BaseDealsTestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.patcher = patch("utils.locations.Locations.get_makan_offers")
        cls.mock_fetch_offer_details = cls.patcher.start()
        cls.mock_fetch_offer_details.return_value = [completed_offers_example()]

    def setUp(self):
        super().setUp()
        self.offer_id = 100
        self.favorite_offer = FavoriteOfferFactory(
            task=self.task, offer_id=self.offer_id
        )
        self.deal = DealFactory(favorite_offer=self.favorite_offer)
        self.form_schema = FormSchemaFactory(key="deal_schema")
        self.mutation = """
            mutation MyMutation($deal_id: Int!, $form_data: JSONString!, $form_schema_key: String!, $is_draft: Boolean!) {
              updateDealMutation(dealInput: {dealId: $deal_id, formData: $form_data, formSchemaKey: $form_schema_key, isDraft: $is_draft}) {
                deal {
                  id
                  formData
                  status

                }
              }
           }
        """

    def test_valid_update_deal(self):
        variables = {
            "deal_id": self.deal.id,
            "form_data": json.dumps({"test": "test"}),
            "form_schema_key": self.form_schema.key,
            "is_draft": False,
        }
        result = self.client.execute(
            self.mutation, variables=variables, context=self.auth_request
        )
        self.assertNotIn("errors", result)
        self.assertDictEqual(
            result["data"]["updateDealMutation"]["deal"]["formData"], {"test": "test"}
        )

    def test_update_deal_with_wrong_deal_id(self):
        wrong_deal_id = 5547
        variables = {
            "deal_id": wrong_deal_id,
            "form_data": json.dumps({"test": "test"}),
            "form_schema_key": self.form_schema.key,
            "is_draft": False,
        }
        result = self.client.execute(
            self.mutation, variables=variables, context=self.auth_request
        )
        self.assertIn("errors", result)
        self.assertEqual(result["errors"][0]["message"], "Not Found")
        self.assertDictEqual(
            result["errors"][0]["extensions"]["http"]["reason"],
            {"deal": f"Deal {wrong_deal_id} not found"},
        )

    def test_update_deal_when_deal_is_finished(self):
        self.deal.status = DealStatus.DEAL_FINISHED
        self.deal.save(update_fields=["status"])
        variables = {
            "deal_id": self.deal.id,
            "form_data": json.dumps({"test": "test"}),
            "form_schema_key": self.form_schema.key,
            "is_draft": False,
        }
        result = self.client.execute(
            self.mutation, variables=variables, context=self.auth_request
        )
        self.assertIn("errors", result)
        self.assertEqual(result["errors"][0]["message"], "Bad Request")
        self.assertDictEqual(
            result["errors"][0]["extensions"]["http"]["reason"],
            {"status": "can't Finish Deal yet"},
        )

    def test_update_deal_when_deal_is_cancelled(self):
        self.deal.status = DealStatus.DEAL_CANCELLED
        self.deal.save(update_fields=["status"])
        variables = {
            "deal_id": self.deal.id,
            "form_data": json.dumps({"test": "test"}),
            "form_schema_key": self.form_schema.key,
            "is_draft": False,
        }
        result = self.client.execute(
            self.mutation, variables=variables, context=self.auth_request
        )
        self.assertIn("errors", result)
        self.assertEqual(result["errors"][0]["message"], "Bad Request")
        self.assertDictEqual(
            result["errors"][0]["extensions"]["http"]["reason"],
            {"status": "can't cancel deal yet"},
        )

    def test_update_deal_when_negotiator_has_no_permissions(self):
        self.task.negotiator = None
        self.task.save(update_fields=["negotiator"])
        variables = {
            "deal_id": self.deal.id,
            "form_data": json.dumps({"test": "test"}),
            "form_schema_key": self.form_schema.key,
            "is_draft": False,
        }
        result = self.client.execute(
            self.mutation, variables=variables, context=self.auth_request
        )
        self.assertIn("errors", result)
        self.assertEqual(result["errors"][0]["message"], "Forbidden")
        self.assertDictEqual(
            result["errors"][0]["extensions"]["http"]["reason"],
            {"user": "Permission Denied"},
        )

    def test_update_deal_when_form_schema_not_found(self):
        variables = {
            "deal_id": self.deal.id,
            "form_data": json.dumps({"test": "test"}),
            "form_schema_key": "tttttt",
            "is_draft": False,
        }
        result = self.client.execute(
            self.mutation, variables=variables, context=self.auth_request
        )
        self.assertIn("errors", result)
        self.assertEqual(result["errors"][0]["message"], "Not Found")
        self.assertDictEqual(
            result["errors"][0]["extensions"]["http"]["reason"],
            {"formSchemaKey": "invalid Form Schema key"},
        )

    def test_update_deal_when_data_is_draft(self):
        variables = {
            "deal_id": self.deal.id,
            "form_data": json.dumps({"test": "test"}),
            "form_schema_key": self.form_schema.key,
            "is_draft": True,
        }
        result = self.client.execute(
            self.mutation, variables=variables, context=self.auth_request
        )
        self.assertNotIn("errors", result)
        self.assertDictEqual(
            result["data"]["updateDealMutation"]["deal"]["formData"],
            {"drafts": {"test": "test"}},
        )
