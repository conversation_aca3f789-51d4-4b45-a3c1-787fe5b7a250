from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from mock.mock import MagicMock

from deals.models import (
    Layer,
)
from deals.tests.utils import BaseTestQueries
from utils.tests.factories import (
    LayerFactory,
)

User = get_user_model()


class LayerTestCase(BaseTestQueries):
    def setUp(self):
        super().setUp()
        self.layers = LayerFactory.create_batch(5)
        self.query = """
            query MyQuery {
              layers {
                data {
                  title
                  modified
                  metadata
                  key
                  id
                  description
                  created
                }
                count
              }
            }
        """

    def test_query_authorization(self):
        response = self.client.execute(self.query, context=self.non_auth_request)
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_list_query(self):
        response = self.client.execute(self.query, context=self.auth_request)
        # Check if the query is successful
        self.assertNotIn("errors", response)
        layers_data = response["data"]["layers"]
        self.assertEqual(layers_data["count"], 5)
        layers_data = layers_data["data"][0]
        layer = Layer.objects.get(id=layers_data["id"])
        self.assertEqual(layers_data["id"], str(layer.id))
        self.assertEqual(layers_data["key"], layer.key)
        self.assertEqual(layers_data["title"], layer.title)
        self.assertEqual(layers_data["description"], layer.description)
        self.assertEqual(layers_data["metadata"], str(layer.metadata))

    def test_query_with_pk(self):
        query = """
            query MyQuery ($pk:Int){
                  layers(pk: $pk) {
                data {
                  title
                  modified
                  metadata
                  key
                  id
                  description
                  created
                }
                count
              }
            }
        """
        auth_request = MagicMock()
        layer = Layer.objects.first()
        variables = {"pk": layer.pk}
        response = self.client.execute(query, variables=variables, context=auth_request)
        # Check if the query is successful
        self.assertNotIn("errors", response)
        layers_data = response["data"]["layers"]
        self.assertEqual(layers_data["count"], 1)
        layers_data = layers_data["data"][0]
        self.assertEqual(layers_data["id"], str(layer.id))
        self.assertEqual(layers_data["key"], layer.key)
        self.assertEqual(layers_data["title"], layer.title)
        self.assertEqual(layers_data["description"], layer.description)
        self.assertEqual(layers_data["metadata"], str(layer.metadata))
