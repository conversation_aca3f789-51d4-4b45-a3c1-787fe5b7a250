from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from mock.mock import MagicMock

from deals.models import (
    Beneficiary,
)
from deals.tests.utils import BaseTestQueries
from utils.tests.factories import (
    BeneficiaryFactory,
    OrderFactory,
    TaskFactory,
)

User = get_user_model()


class BeneficiariesTestCase(BaseTestQueries):
    def setUp(self):
        super().setUp()
        self.beneficiaries = BeneficiaryFactory.create_batch(size=5)
        self.query = """
        query MyQuery {
          beneficiaries {
            count
            data {
              assignedTaskStatus
              code
              externalId
              id
              name
              taskId
              taskStatus
              beneficiaryData {
                age
                avgFund
                city
                destination
                familyMember
                job
                mobile
                product
                region
                salary
                salaryBank
              }
            }

          }
        }
        """

    def test_query_authorization(self):
        response = self.client.execute(self.query, context=self.non_auth_request)
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_query_with_invalid_permission(self):
        auth_request = MagicMock()
        auth_request.user = self.customer_service[0]
        response = self.client.execute(self.query, context=auth_request)
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Forbidden"))

    def test_list_query(self):
        auth_request = MagicMock()
        auth_request.user = self.project_manager
        response = self.client.execute(self.query, context=auth_request)
        # Check if the query is successful
        self.assertNotIn("errors", response)
        beneficiary_data = response["data"]["beneficiaries"]
        self.assertEqual(beneficiary_data["count"], 5)
        beneficiary_data = beneficiary_data["data"][0]
        beneficiary = Beneficiary.objects.get(id=beneficiary_data.get("id"))
        self.assertIsNone(beneficiary_data.get("taskId"))
        self.assertIsNone(beneficiary_data.get("taskStatus"))
        self.assertIsNone(beneficiary_data["assignedTaskStatus"])
        self.assertEqual(beneficiary_data.get("externalId"), beneficiary.external_id)

    def test_list_query_with_assigned_tasks(self):
        """
        test query while task assigned for beneficiary
        """
        auth_request = MagicMock()
        auth_request.user = self.project_manager
        order = OrderFactory(beneficiaries=self.beneficiaries)
        beneficiary = Beneficiary.objects.get(id=self.beneficiaries[0].id)
        TaskFactory(
            beneficiary=beneficiary,
            customer_service=self.customer_service[0],
            order=order,
        )
        response = self.client.execute(self.query, context=auth_request)
        # Check if the query is successful
        self.assertNotIn("errors", response)
        beneficiaries = response["data"]["beneficiaries"]["data"]
        beneficiaries = [beneficiary["id"] for beneficiary in beneficiaries]
        # make sure that Beneficiary not in response
        self.assertNotIn(beneficiary.id, beneficiaries)

    def test_query_with_filteration(self):
        auth_request = MagicMock()
        auth_request.user = self.project_manager
        beneficiary = Beneficiary.objects.order_by("id").first()
        query = """
            query MyQuery($age: String!, $salary: String!) {
              beneficiaries(
                filters: [
             {field: "data__personalData__ageHijri", value: $age, clause: iexact},
             {field: "data__financialData__salary", value: $salary, clause: iexact}]
              ) {
                data {
                  name
                  id
                  externalId
                  code
                  beneficiaryData {
                    age
                    avgFund
                    city
                    destination
                    familyMember
                    job
                    mobile
                    product
                    region
                    salary
                    salaryBank
                  }
                }
              }
            }
        """
        variables = {
            "salary": beneficiary.extract_data(field="salary"),
            "age": beneficiary.extract_data(field="age"),
        }
        response = self.client.execute(query, context=auth_request, variables=variables)
        # Check if the query is successful
        self.assertNotIn("errors", response)
        beneficiary_data = response["data"]["beneficiaries"]["data"]
        # check response filter
        for obj in beneficiary_data:
            self.assertEqual(
                obj["beneficiaryData"]["age"], beneficiary.extract_data(field="age")
            )
