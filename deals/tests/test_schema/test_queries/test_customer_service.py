from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from mock.mock import MagicMock

from deals.tests.utils import BaseTestQueries
from users.models import UserRoleChoices

User = get_user_model()


class CustomerServiceTestCase(BaseTestQueries):
    def setUp(self):
        super().setUp()
        self.query = """
        query MyQuery {
          customerServices {
            count
            data {
              email
              finishedTasksCount
              firstName
              id
              inProgressTasksCount
              lastName
              pendingReviewTasksCount
              rejectedTasksCount
              totalTasksCount
            }
          }
        }
        """

    def test_query_authorization(self):
        response = self.client.execute(self.query, context=self.non_auth_request)
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_query_with_invalid_permission(self):
        auth_request = MagicMock()
        auth_request.user = self.customer_service[0]
        response = self.client.execute(self.query, context=auth_request)
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Forbidden"))

    def test_list_query(self):
        auth_request = MagicMock()
        auth_request.user = self.project_manager
        response = self.client.execute(self.query, context=auth_request)
        # Check if the query is successful
        self.assertNotIn("errors", response)
        user_data = response["data"]["customerServices"]
        self.assertEqual(user_data["count"], 6)
        user_data = response["data"]["customerServices"]["data"][0]
        user = User.objects.get(id=user_data["id"])
        self.assertEqual(user_data["totalTasksCount"], 0)
        self.assertEqual(
            user_data["inProgressTasksCount"],
            user.customer_service_in_progress_tasks_count,
        )
        self.assertEqual(user_data["finishedTasksCount"], 0)
        self.assertEqual(
            user_data["pendingReviewTasksCount"],
            0,
        )
        self.assertEqual(user_data["rejectedTasksCount"], 0)
        self.assertEqual(user_data["id"], str(user.id))
        self.assertEqual(user_data["firstName"], user.first_name)
        self.assertEqual(user_data["lastName"], user.last_name)
        self.assertEqual(user_data["email"], user.email)
        # check if any other users with different roles exists
        user_ids = [user["id"] for user in response["data"]["customerServices"]["data"]]
        invalid_user = User.objects.filter(
            roles__role__in=[
                UserRoleChoices.PROJECT_MANAGERS,
                UserRoleChoices.NEGOTIATORS,
            ]
        ).first()
        self.assertNotIn(str(invalid_user.id), user_ids)

    def test_query_with_pk(self):
        query = """
        query MyQuery ($pk:Int){
          customerServices(pk: $pk){
            count
            data {
              email
              finishedTasksCount
              firstName
              id
              inProgressTasksCount
              lastName
              pendingReviewTasksCount
              rejectedTasksCount
              totalTasksCount
            }
          }
        }
        """
        auth_request = MagicMock()
        auth_request.user = self.project_manager
        user = User.objects.filter(
            roles__role=UserRoleChoices.CUSTOMER_SERVICES
        ).first()
        variables = {"pk": user.pk}
        response = self.client.execute(query, variables=variables, context=auth_request)
        # Check if the query is successful
        self.assertNotIn("errors", response)
        user_data = response["data"]["customerServices"]
        self.assertEqual(user_data["count"], 1)
        user_data = user_data["data"][0]
        self.assertEqual(user_data["totalTasksCount"], 0)
        self.assertEqual(
            user_data["inProgressTasksCount"],
            0,
        )
        self.assertEqual(user_data["finishedTasksCount"], 0)
        self.assertEqual(
            user_data["pendingReviewTasksCount"],
            0,
        )
        self.assertEqual(user_data["rejectedTasksCount"], 0)
        self.assertEqual(user_data["id"], str(user.id))
        self.assertEqual(user_data["firstName"], user.first_name)
        self.assertEqual(user_data["lastName"], user.last_name)
        self.assertEqual(user_data["email"], user.email)
