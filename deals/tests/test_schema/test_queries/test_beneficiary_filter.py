from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _

from deals.management.commands.load_ren_locations import LAYERS_KEYS
from deals.models import BENEFICIARY_DATA_MAPPER, Layer, Record, Beneficiary
from deals.tests.utils import RecordsTestCase

User = get_user_model()


class BeneficiaryFilterTestCase(RecordsTestCase):
    def setUp(self):
        super().setUp()
        self.query = """
            query MyQuery {
              beneficiaryFilterOptions {
                city
                job
                product
                region
                salaryBank
              }
            }
        """

    def test_query_authorization(self):
        response = self.client.execute(self.query, context=self.non_auth_request)
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def get_db_values_list(self, field):
        field = BENEFICIARY_DATA_MAPPER.get(field, "")
        field = "__".join(field.split("."))
        filters = {f"data__{field}"}
        values = Beneficiary.objects.distinct(*filters).values_list(*filters, flat=True)
        return values

    def test_city(self):
        response = self.client.execute(self.query, context=self.auth_request)
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["beneficiaryFilterOptions"]
        data: dict = data["city"]
        city_db = self.get_db_values_list("city")
        city_layer = Layer.objects.filter(key=LAYERS_KEYS.get("city")).first()
        city_records = Record.objects.filter(layer=city_layer).values_list(
            "source_properties__city_name", flat=True
        )
        values = city_records.union(city_db)
        self.assertEqual(len(data), len(values))

    def test_region(self):
        response = self.client.execute(self.query, context=self.auth_request)
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["beneficiaryFilterOptions"]
        data: dict = data["region"]
        region_db = self.get_db_values_list("region")
        region_layer = Layer.objects.filter(key=LAYERS_KEYS.get("region")).first()
        region_records = Record.objects.filter(layer=region_layer).values_list(
            "source_properties__region_name", flat=True
        )
        values = region_records.union(region_db)
        self.assertEqual(len(data), len(values))

    def test_job(self):
        response = self.client.execute(self.query, context=self.auth_request)
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["beneficiaryFilterOptions"]
        data: dict = data["job"]
        job_db = self.get_db_values_list("region")
        self.assertEqual(len(data), len(job_db))

    def test_product(self):
        response = self.client.execute(self.query, context=self.auth_request)
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["beneficiaryFilterOptions"]
        data: dict = data["product"]
        product_db = self.get_db_values_list("product")
        self.assertEqual(len(data), len(product_db))

    def test_salary_bank(self):
        response = self.client.execute(self.query, context=self.auth_request)
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["beneficiaryFilterOptions"]
        data: dict = data["salaryBank"]
        salary_bank_db = self.get_db_values_list("salary_bank")
        self.assertEqual(len(data), len(salary_bank_db))
