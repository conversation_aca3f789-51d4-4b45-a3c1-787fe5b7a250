from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _

from deals.management.commands.load_ren_locations import LAYERS_KEYS
from deals.models import (
    Record,
)
from deals.tests.utils import RecordsTestCase

User = get_user_model()


class CityTestCase(RecordsTestCase):
    def setUp(self):
        super().setUp()
        self.query = """
            query MyQuery {
              cities {
                count
                data {
                  created
                  geometry
                  modified
                  sourceProperties
                  layer {
                    created
                    description
                    id
                    key
                    metadata
                    modified
                    title
                  }
                }
              }
            }
        """

    def test_query_authorization(self):
        response = self.client.execute(self.query, context=self.non_auth_request)
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_list_query(self):
        response = self.client.execute(self.query, context=self.auth_request)
        records = Record.objects.filter(layer__key=LAYERS_KEYS["city"]).distinct(
            "source_properties__city_name"
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["cities"]
        self.assertEqual(data["count"], records.count())
        data = data["data"][0]
        record = records.filter(source_properties__id=data["sourceProperties"]["id"])[0]
        self.assertEqual(data.get("layer")["key"], record.layer.key)
        self.assertEqual(data.get("sourceProperties"), record.source_properties)
        self.assertTrue(data["geometry"], record.geometry.wkt)

    def test_query_with_pk(self):
        query = """
        query MyQuery($regionId:Int){
              cities(regionId: $regionId) {
                count
                data {
                  created
                  geometry
                  modified
                  sourceProperties
                  layer {
                    created
                    description
                    id
                    key
                    metadata
                    modified
                    title
                  }
                }
              }
            }
        """
        record = Record.objects.filter(layer__key=LAYERS_KEYS["city"]).distinct(
            "source_properties__city_name"
        )[0]
        variables = {"regionId": record.source_properties.get("region_id")}
        response = self.client.execute(
            query, variables=variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["cities"]["data"][0]
        self.assertEqual(data.get("layer")["key"], record.layer.key)
        self.assertEqual(data.get("sourceProperties"), record.source_properties)
        self.assertTrue(data["geometry"], record.geometry.wkt)

    def test_query_with_invalid_layer(self):
        self.query_with_invalid_layer()
