from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _

from deals.management.commands.load_ren_locations import LAYERS_KEYS
from deals.models import Record
from deals.tests.utils import RecordsTestCase

User = get_user_model()


class DistrictTestCase(RecordsTestCase):
    def setUp(self):
        super().setUp()
        self.query = """
            query MyQuery {
              districts {
                count
                data {
                  created
                  geometry
                  modified
                  sourceProperties
                  layer {
                    created
                    description
                    id
                    key
                    metadata
                    modified
                    title
                  }
                }
              }
            }
        """

    def test_query_authorization(self):
        response = self.client.execute(self.query, context=self.non_auth_request)
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_list_query(self):
        response = self.client.execute(self.query, context=self.auth_request)
        records = Record.objects.filter(layer__key=LAYERS_KEYS["districts"]).distinct(
            "source_properties__district_name"
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["districts"]
        self.assertEqual(
            data["count"], records.distinct("source_properties__district_name").count()
        )
        data = data["data"][0]
        record = records.filter(source_properties__id=data["sourceProperties"]["id"])[0]
        self.assertEqual(data.get("layer")["key"], record.layer.key)
        self.assertEqual(data.get("sourceProperties"), record.source_properties)
        self.assertTrue(data["geometry"], record.geometry.wkt)

    def test_query_with_pk(self):
        query = """
        query MyQuery($cityId:Int, $zonesId:[Int]){
              districts(cityId: $cityId, zonesId: $zonesId) {
                count
                data {
                  created
                  geometry
                  modified
                  sourceProperties
                  layer {
                    created
                    description
                    id
                    key
                    metadata
                    modified
                    title
                  }
                }
              }
            }
        """
        city = Record.objects.filter(layer__key=LAYERS_KEYS["city"]).distinct(
            "source_properties__city_name"
        )[0]
        city_id = city.source_properties.get("id")
        self.assertIsNotNone(city_id)
        zone = Record.objects.filter(
            layer__key=LAYERS_KEYS["zone"], source_properties__city_id=city_id
        ).distinct("source_properties__zone_name")[0]
        if not zone:
            zone_id = []
        else:
            zone_id = [zone.source_properties.get("id")]
        variables = {"cityId": city_id, "zonesId": zone_id}
        response = self.client.execute(
            query, variables=variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["districts"]["data"]
        if not data:
            self.assertEqual(response["data"]["districts"]["count"], 0)
            return
        record = Record.objects.filter(
            source_properties__id=data["sourceProperties"]["id"]
        ).first()
        self.assertEqual(data.get("layer")["key"], record.layer.key)
        self.assertDictEqual(data.get("sourceProperties"), record.source_properties)
        self.assertTrue(data["geometry"], record.geometry.wkt)
        # test query without city_id
        query = """
               query MyQuery($cityId:Int,){
                     districts(cityId: $cityId) {
                       count
                       data {
                         created
                         geometry
                         modified
                         sourceProperties
                         layer {
                           created
                           description
                           id
                           key
                           metadata
                           modified
                           title
                         }
                       }
                     }
                   }
               """

        variables = {
            "cityId": city_id,
        }
        response = self.client.execute(
            query, variables=variables, context=self.auth_request
        )
        data = response["data"]["districts"]
        # Check if the query is successful
        self.assertNotIn("errors", response)
        districts = Record.objects.filter(
            layer__key=LAYERS_KEYS["districts"],
            source_properties__zone_id__isnull=True,
            source_properties__city_id__in=[int(city_id)],
        ).distinct("source_properties__district_name")
        self.assertEqual(districts.count(), data["count"])

    def test_query_with_invalid_layer(self):
        self.query_with_invalid_layer()
