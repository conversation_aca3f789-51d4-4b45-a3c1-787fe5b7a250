from unittest.mock import patch

from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _

from deals.models import FavoriteOffer
from deals.tests.utils import BaseTestQueries
from orders.models import Task
from users.models import UserRoleChoices
from utils.tests.factories import (
    UserFactory,
    BeneficiaryFactory,
    OrderFactory,
    FavoriteOfferFactory,
)

User = get_user_model()


class FavoriteOffersTestCase(BaseTestQueries):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()  # Call parent setup if needed
        cls.patcher = patch("utils.locations.Locations.get_makan_offers")
        cls.mock_fetch_offer_details = cls.patcher.start()
        cls.mock_fetch_offer_details.return_value = []

    def setUp(self):
        super().setUp()
        self.user = UserFactory(roles=[self.roles[UserRoleChoices.NEGOTIATORS.value]])
        self.auth_request.user = self.user
        self.beneficiaries = BeneficiaryFactory()
        self.order = OrderFactory(beneficiaries=[self.beneficiaries])
        self.customer_service = UserFactory(
            roles=[self.roles[UserRoleChoices.CUSTOMER_SERVICES.value]]
        )
        tasks = []
        for beneficiary in self.order.beneficiaries.all():
            task = Task(
                form_data={
                    "negotiator": {
                        "negotiatorData": {"test": "test"},
                        "placeOrder": {"test": "test"},
                    }
                },
                order=self.order,
                beneficiary=beneficiary,
                customer_service=self.customer_service,
                negotiator=self.auth_request.user,
            )
            tasks.append(task)
        Task.objects.bulk_create(tasks)
        self.task = tasks[0]
        self.offer_id = 100
        self.favorite_offers = FavoriteOfferFactory.create_batch(
            10, task=self.task, offer_id=self.offer_id
        )
        self.query = """
            query MyQuery($taskId:Int!){
              favoriteOffers(taskId: $taskId) {
                count
                data {
                  hasDeal
                  id
                  taskId
                }

              }
            }
        """
        self.variables = {"taskId": self.task.id}

    def test_query_authorization(self):
        response = self.client.execute(
            self.query, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))
        self.auth_request.user = self.customer_service
        response = self.client.execute(
            self.query, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Forbidden"))
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {
                "user": _("Permission Denied") % {},
            },
        )

    def test_list_query(self):
        response = self.client.execute(
            self.query, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["favoriteOffers"]
        self.assertEqual(data["count"], 10)
        data = data["data"][0]
        favorite_offer = FavoriteOffer.objects.get(id=data["id"])
        self.assertEqual(data["id"], str(favorite_offer.id))
        self.assertEqual(data["taskId"], self.task.id)

    @patch("orders.models.Task.objects.filter")
    def test_query_with_invalid_task(self, mock_task):
        mock_task.return_value = Task.objects.none()
        response = self.client.execute(
            self.query, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Not Found"))
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {
                "task": _("Task %(task)s does not exist")
                % {"task": self.variables["taskId"]},
            },
        )
