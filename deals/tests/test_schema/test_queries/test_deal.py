from unittest.mock import patch

from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _

from deals.tests.utils import BaseTestQueries
from orders.models import Task
from utils.tests.factories import (
    BeneficiaryFactory,
    OrderFactory,
    TaskFactory,
    FavoriteOfferFactory,
    DealFactory,
)

User = get_user_model()


class DealsTestCase(BaseTestQueries):
    def setUp(self):
        super().setUp()
        beneficiary = BeneficiaryFactory()
        self.order = OrderFactory(beneficiaries=[beneficiary])
        self.task = TaskFactory(
            order=self.order,
            beneficiary=beneficiary,
            customer_service=self.customer_service[0],
            negotiator=self.negotiator,
        )
        self.favorite_offer = FavoriteOfferFactory(task=self.task, offer_id=100)
        self.deal = DealFactory(favorite_offer=self.favorite_offer)
        self.query = """
       query MyQuery ($taskId: Int!){
          deals(taskId: $taskId) {
            count
            data {
              formData
              id
              status
              task {
                assignedStatus
                beneficiary {
                  code
                  externalId
                  name
                }
                created
                formData
                id
                modified
                status
                order {
                  assignedToCustomerServicesTaskCount
                  assignedToNegotiatorTaskCount
                  created
                  endDate
                  id
                  modified
                  pendingReviewTasksCount
                  rejectedTasksCount
                  startDate
                  tasksCount
                }
                negotiator {
                  phone
                  lastName
                  isSuperuser
                  isStaff
                  id
                  email
                  avatar
                  firstName
                }
                customerService {
                  avatar
                  email
                  firstName
                  id
                  isStaff
                  isSuperuser
                  lastName
                  phone
                  roles {
                    role
                  }
                }
              }

            }
          }
        }
        """
        self.variables = {"taskId": self.task.id}

    def test_query_authorization(self):
        response = self.client.execute(
            self.query, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))
        # Check if the query is successful
        self.auth_request.user = self.customer_service[0]
        response = self.client.execute(
            self.query, variables=self.variables, context=self.auth_request
        )
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Forbidden"))
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {
                "user": _("Permission Denied") % {},
            },
        )

    def test_list_query(self):
        self.auth_request.user = self.negotiator
        response = self.client.execute(
            self.query, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)
        data = response["data"]["deals"]
        self.assertEqual(data["count"], 1)
        data = data["data"][0]
        self.assertEqual(data["id"], str(self.deal.id))
        self.assertIsNone(data["formData"])
        self.assertDictEqual(
            data["status"],
            {
                "key": self.deal.status,
                "display": self.deal.get_status_display(),
            },
        )
        task = data["task"]
        self.assertDictEqual(
            task["assignedStatus"],
            {
                "key": self.task.assigned_status,
                "display": self.task.get_assigned_status_display(),
            },
        )
        self.assertEqual(task["beneficiary"]["code"], str(self.task.beneficiary.code))
        self.assertIsNone(task["formData"])
        self.assertEqual(task["id"], str(self.task.id))
        order = task["order"]
        self.assertEqual(order["id"], str(self.order.id))
        negotiator = task["negotiator"]
        self.assertEqual(
            negotiator["id"], str(self.deal.favorite_offer.task.negotiator_id)
        )
        customer_service = task["customerService"]
        self.assertEqual(
            customer_service["id"],
            str(self.deal.favorite_offer.task.customer_service_id),
        )

    @patch("orders.models.Task.objects.filter")
    def test_query_with_invalid_task(self, mock_task):
        mock_task.return_value = Task.objects.none()
        response = self.client.execute(
            self.query, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Not Found"))
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {
                "task": _("Task %(task)s does not exist")
                % {"task": self.variables["taskId"]},
            },
        )
