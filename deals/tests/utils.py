import os
from unittest.mock import MagicMock, patch

from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AnonymousUser
from django.core.management import call_command
from django.test import TestCase
from django.test import override_settings
from django.utils.translation import activate
from django.utils.translation import gettext_lazy as _
from graphene.test import Client

from app.schema import schema
from deals.management.commands.load_ren_locations import LAYERS_KEYS
from deals.models import Layer
from users.models import UserRoleChoices
from utils.tests.factories import RenRoleFactory
from utils.tests.factories import (
    UserFactory,
    BeneficiaryFactory,
    OrderFactory,
    TaskFactory,
)

User = get_user_model()


class BaseDealsTestCase(TestCase):
    def setUp(self):
        activate("en")
        self.negotiator = UserFactory(
            roles=[RenRoleFactory(role=UserRoleChoices.NEGOTIATORS)]
        )
        self.customer_service = UserFactory(
            roles=[RenRoleFactory(rolle=UserRoleChoices.CUSTOMER_SERVICES)]
        )
        self.beneficiary = BeneficiaryFactory()
        self.order = OrderFactory(beneficiaries=[self.beneficiary])
        self.task = TaskFactory(
            order=self.order,
            beneficiary=self.beneficiary,
            customer_service=self.customer_service,
            negotiator=self.negotiator,
        )
        self.client = Client(schema)
        self.auth_request = MagicMock()
        self.auth_request.user = self.negotiator
        self.non_auth_request = MagicMock()
        self.non_auth_request.user = AnonymousUser()
        self.non_authorized_request = MagicMock()
        self.non_authorized_request.user = self.customer_service
        self.maxDiff = None


class BaseTestQueries(TestCase):
    def setUp(self):
        activate("ar")
        self.roles = {
            UserRoleChoices.CUSTOMER_SERVICES.value: RenRoleFactory(
                role=UserRoleChoices.CUSTOMER_SERVICES
            ),
            UserRoleChoices.PROJECT_MANAGERS.value: RenRoleFactory(
                role=UserRoleChoices.PROJECT_MANAGERS
            ),
            UserRoleChoices.NEGOTIATORS.value: RenRoleFactory(
                role=UserRoleChoices.NEGOTIATORS
            ),
        }
        self.user = UserFactory()
        self.superuser = UserFactory(
            is_superuser=True,
            roles=[self.roles[UserRoleChoices.PROJECT_MANAGERS.value]],
        )
        self.negotiator = UserFactory(
            roles=[self.roles[UserRoleChoices.NEGOTIATORS.value]]
        )
        self.project_manager = UserFactory(
            roles=[self.roles[UserRoleChoices.PROJECT_MANAGERS.value]]
        )
        self.customer_service = UserFactory.create_batch(
            size=5, roles=[self.roles[UserRoleChoices.CUSTOMER_SERVICES.value]]
        )
        self.client = Client(schema)
        self.auth_request = MagicMock()
        self.auth_request.user = self.user
        self.non_auth_request = MagicMock()
        self.non_auth_request.user = AnonymousUser()
        self.maxDiff = None


class RecordsTestCase(BaseTestQueries):
    def setUp(self):
        super().setUp()

    @classmethod
    @override_settings(
        MEDIA_ROOT=os.path.join(settings.BASE_DIR, "deals", "tests", "fixtures")
    )
    def setUpTestData(cls):
        super().setUpTestData()
        files_path = os.path.join(
            settings.BASE_DIR, "deals", "tests", "fixtures", "sample_geopackage_data"
        )
        files = os.listdir(files_path)
        for file in files:
            # create records per layer
            file_path = os.path.join(files_path, file)
            layer_name = file.split(".")[0]
            call_command(
                "load_ren_locations",
                layer_key=LAYERS_KEYS[layer_name],
                file_path=file_path,
            )

    @patch("deals.models.Layer.objects.filter")
    def query_with_invalid_layer(self, mock_resolve_regions):
        mock_resolve_regions.return_value = Layer.objects.none()
        response = self.client.execute(self.query, context=self.auth_request)
        # Check if the query is successful
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Not Found"))
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {
                "layer": _("Layer matching query does not exist") % {},
            },
        )
