from django.test import TestCase
from gabbro.utils import generate_random_string

from deals.models.beneficiary import Beneficiary


class BaseBeneficiaryTestCase(TestCase):
    def setUp(self):
        pass


class BeneficiaryTestCase(BaseBeneficiaryTestCase):
    def setUp(self):
        super(BeneficiaryTestCase, self).setUp()

    def test_model_creation(self):
        """
        Test creation of a beneficiary Model
        """
        beneficiaries = []
        for i in range(1, 6):
            beneficiary = Beneficiary(
                code=generate_random_string(length=20),
                external_id=i,
                name=generate_random_string(length=20),
            )
            beneficiaries.append(beneficiary)
        Beneficiary.objects.bulk_create(beneficiaries)
        self.assertEqual(Beneficiary.objects.count(), 5)

    def test_beneficiary_str(self):
        """
        Test string representation of a beneficiary
        """
        beneficiary = Beneficiary.objects.create(
            code=generate_random_string(length=20),
            external_id=1,
            name=generate_random_string(length=20),
        )
        self.assertEqual(str(beneficiary), beneficiary.name)
        # test with code
        beneficiary = Beneficiary.objects.create(
            code=generate_random_string(length=20),
            external_id=1,
        )
        self.assertEqual(str(beneficiary), beneficiary.code)

    def test_beneficiary_extract_data(self):
        data = {
            "familyData": {"familyMembersCount": 5},
            "job": {"job": "job value"},
        }
        beneficiary = Beneficiary.objects.create(
            code=generate_random_string(length=20), external_id=1, data=data
        )
        family_member = beneficiary.extract_data(field="family_member")
        self.assertEqual(
            beneficiary.data["familyData"]["familyMembersCount"], family_member
        )
        other_field = beneficiary.extract_data(field="other_field")
        self.assertIsNone(other_field)
