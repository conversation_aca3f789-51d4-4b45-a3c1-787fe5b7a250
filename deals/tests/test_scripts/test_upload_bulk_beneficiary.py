import os
from unittest.mock import MagicMock, patch

import pandas as pd
from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.messages import DEFAULT_LEVELS
from django.test import TestCase
from django.utils.translation import gettext_lazy as _

from deals.scripts.upload_bulk_benefeciary import UploadBulkBeneficiary
from utils.tests.factories import BeneficiaryFactory, FormSchemaFactory

User = get_user_model()


class UploadBulkBeneficiaryTestCase(TestCase):
    def setUp(self):
        self.beneficiary1 = BeneficiaryFactory(code="REDF-01-103", external_id=101)
        self.beneficiary2 = BeneficiaryFactory(code="REDF-01-99", external_id=99)
        self.file_path = os.path.join(
            settings.BASE_DIR,
            "deals",
            "tests",
            "fixtures",
            "sample beneficiaries",
            "sample.xlsx",
        )
        self.form_schem = FormSchemaFactory(
            json_schema={
                "form": {
                    "type": "object",
                    "required": ["request"],
                    "properties": {
                        "ID_CODE": {"type": ["string", "null"]},
                        "request": {
                            "type": "object",
                            "properties": {
                                "registrationDate": {
                                    "type": ["string", "null"],
                                    "format": "date",
                                }
                            },
                        },
                        "location": {
                            "type": "object",
                            "properties": {
                                "city": {"type": ["string", "null"]},
                                "region": {"type": ["string", "null"]},
                            },
                        },
                        "familyData": {
                            "type": "object",
                            "properties": {
                                "familyMembersCount": {"type": ["integer", "null"]}
                            },
                        },
                        "destination": {"type": ["string", "null"]},
                        "personalData": {
                            "type": "object",
                            "required": ["name", "mobileNumber"],
                            "properties": {
                                "name": {"type": "string"},
                                "ageHijri": {"type": ["integer", "null"]},
                                "mobileNumber": {
                                    "type": "string",
                                    "pattern": "^5\\d{8}$",
                                },
                            },
                        },
                        "financialData": {
                            "type": "object",
                            "properties": {
                                "AIP1": {"type": ["null", "number"]},
                                "AIP2": {"type": ["null", "number"]},
                                "AIP3": {"type": ["null", "number"]},
                                "AIP4": {"type": ["null", "number"]},
                                "AIP5": {"type": ["null", "number"]},
                                "salary": {"type": ["null", "number"]},
                                "funder1": {"type": ["string", "null"]},
                                "funder2": {"type": ["string", "null"]},
                                "funder3": {"type": ["string", "null"]},
                                "funder4": {"type": ["string", "null"]},
                                "funder5": {"type": ["string", "null"]},
                                "salaryBank": {"type": ["string", "null"]},
                                "entitlement": {"type": ["null", "string"]},
                                "fundingAmount": {"type": ["number", "null"]},
                                "additionalIncome": {"type": ["number", "null"]},
                                "housingAllowance": {"type": ["number", "null"]},
                                "monthlyDeduction": {"type": ["number", "null"]},
                                "remainingDurationMonths": {
                                    "type": ["integer", "null"]
                                },
                            },
                        },
                        "supportPackages": {
                            "type": "object",
                            "properties": {"product": {"type": ["null", "string"]}},
                        },
                        "professionalData": {
                            "type": "object",
                            "properties": {"job": {"type": ["null", "string"]}},
                        },
                        "personalRealEstateData": {
                            "type": "object",
                            "properties": {
                                "hasLand": {"type": ["null", "string"]},
                                "startedBuilding": {"type": ["null", "string"]},
                                "hasBuildingLicense": {"type": ["null", "string"]},
                            },
                        },
                        "رقم التقنيات": {"type": ["integer", "null"]},
                    },
                }
            },
            key="upload-bulk-beneficiaries",
        )
        self.file_obj = open(self.file_path, "rb")
        self.mock_file = MagicMock()
        self.mock_instance = UploadBulkBeneficiary(metadata={"file": self.mock_file})
        self.instance = UploadBulkBeneficiary(metadata={"file": self.file_obj})

    def test_file_is_none(self):
        self.mock_instance.file = None
        valid, message = self.mock_instance.validate_file()
        self.assertFalse(valid)
        self.assertEqual(message, "File Is Required")

    def test_invalid_file_extension(self):
        valid, message = self.mock_instance.validate_file()
        self.assertFalse(valid)
        self.assertEqual(message, "Invalid File Extension")

    @patch("pandas.read_excel")
    def test_missing_columns(self, mock_read_excel):
        self.mock_file.name = "valid_file.xlsx"

        # Mocking a DataFrame with missing columns
        mock_df = pd.DataFrame(
            {"wrong_column_1": [1, 2, 3], "wrong_column_2": [4, 5, 6]}
        )
        mock_read_excel.return_value = mock_df

        valid, message = self.instance.validate_file()
        self.assertFalse(valid)
        self.assertEqual(message, "Missing Columns")

    def test_valid_file(self):
        valid, message = self.instance.validate_file()
        self.assertTrue(valid)
        self.assertEqual(message, "")

    def test_valid_instance_execution(self):
        result, file_messages, user_message, errors_file = self.instance.execute()
        self.assertTrue(result)
        self.assertListEqual(
            user_message,
            [
                {
                    "message": _("%(created_records)s Beneficiary Created")
                    % {"created_records": 8},
                    "level": DEFAULT_LEVELS["SUCCESS"],
                },
                {
                    "message": _("%(error_count)s Error") % {"error_count": 2},
                    "level": DEFAULT_LEVELS["ERROR"],
                },
            ],
        )

    @patch("deals.scripts.upload_bulk_benefeciary.pd.read_excel")
    def test_execute_with_null_data(self, mock_read_excel):
        mock_df = pd.DataFrame(
            {
                "ID_CODE": ["123"],
                "external_id": ["123"],
                "name": ["123"],
                "رقم الجوال": "0525555",
            }
        )
        mock_read_excel.return_value = mock_df
        result, file_messages, user_message, errors_file = self.mock_instance.execute()
        self.assertListEqual(
            user_message,
            [
                {
                    "message": _("%(created_records)s Beneficiary Created")
                    % {"created_records": 0},
                    "level": DEFAULT_LEVELS["SUCCESS"],
                },
                {
                    "message": _("%(error_count)s Error") % {"error_count": 1},
                    "level": DEFAULT_LEVELS["ERROR"],
                },
            ],
        )

    @patch("deals.scripts.upload_bulk_benefeciary.pd.read_excel")
    @patch.object(UploadBulkBeneficiary, "bulk_create")
    def test_execute_exception(self, mock_bulk_create, mock_read_excel):
        mock_df = pd.DataFrame(
            {
                "ID_CODE": [],
                "external_id": [],
                "name": [],
            }
        )
        mock_read_excel.return_value = mock_df
        mock_bulk_create.side_effect = Exception("Simulated error")
        # Call execute and get the results
        result, file_messages, user_message, errors_file = self.mock_instance.execute()
        # Assertions for the exception path
        self.assertFalse(result)
        self.assertIn("errors", file_messages)
        self.assertDictEqual(
            file_messages["errors"],
            {
                "file_errors": {},
                "script_errors": {"1": ["Simulated error"]},
            },
        )
        self.assertEqual(
            user_message,
            [
                {
                    "message": _("%(created_records)s Beneficiary Created")
                    % {"created_records": 0},
                    "level": DEFAULT_LEVELS["SUCCESS"],
                },
                {
                    "message": _("%(error_count)s Error") % {"error_count": 1},
                    "level": DEFAULT_LEVELS["ERROR"],
                },
            ],
        )
