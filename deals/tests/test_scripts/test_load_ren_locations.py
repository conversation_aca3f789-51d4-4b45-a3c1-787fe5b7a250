import os
from unittest.mock import patch, MagicMock

from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.gis.geos import GEOSException
from django.test import TestCase, override_settings

from deals.management.commands.load_ren_locations import LAYERS_KEYS
from deals.models import Layer, Record
from deals.scripts.load_ren_locations import LoadRenLocations
from utils.general_utils import open_storage_file

User = get_user_model()


@override_settings(
    MEDIA_ROOT=os.path.join(settings.BASE_DIR, "deals", "tests", "fixtures")
)
class LoadRenLocationsTestCase(TestCase):
    def setUp(self):
        self.files = {}
        files_path = os.path.join(
            settings.BASE_DIR, "deals", "tests", "fixtures", "sample_geopackage_data"
        )
        files = os.listdir(files_path)
        for file in files:
            file_path = os.path.join(files_path, file)
            layer_key = file.split(".")[0]
            self.files[layer_key] = file_path
            Layer.objects.create(key=layer_key)
        layer_key, file_path = list(self.files.items())[0]
        self.layer = Layer.objects.get(key=layer_key)
        self.instance = LoadRenLocations(
            metadata={"file": file_path, "layer": self.layer, "patch_size": 100}
        )

    def test_script_execution_with_invalid_file_path(self):
        layer_key = list(self.files.items())[0][0]
        self.layer = Layer.objects.get(key=layer_key)
        file_path = os.path.join("deals", "tests", "fixtures", "non-exist-file.xlsx")
        instance = LoadRenLocations(
            metadata={"file": file_path, "layer": self.layer, "patch_size": 100}
        )
        execute = instance.execute()
        self.assertIsNone(execute)

    @patch("utils.geometry.get_valid_geometry")
    def test_create_records_with_valid_features(self, mock_get_valid_geometry):
        # Mock valid geometry
        valid_geometry = MagicMock()
        valid_geometry.valid = True
        mock_get_valid_geometry.return_value = valid_geometry

        features = [
            {
                "properties": {"name": "Feature1"},
                "geometry": {"type": "Point", "coordinates": [0, 0]},
            },
            {
                "properties": {"name": "Feature2"},
                "geometry": {"type": "Point", "coordinates": [1, 1]},
            },
        ]

        with patch.object(Record.objects, "bulk_create") as mock_bulk_create:
            records = self.instance.create_records_from_features(
                features, self.layer, Record
            )

            # Assertions
            self.assertEqual(len(records), 2)
            self.assertEqual(records[0].source_properties, {"name": "Feature1"})
            self.assertEqual(records[1].source_properties, {"name": "Feature2"})
            mock_bulk_create.assert_called_once_with(
                records, batch_size=100, ignore_conflicts=True
            )

    @patch("deals.scripts.load_ren_locations.get_valid_geometry")
    def test_create_records_with_invalid_geometry_object(self, mock_get_valid_geometry):
        # Mock invalid geometry, which raises a GEOSException
        mock_get_valid_geometry.side_effect = GEOSException("Invalid geometry")
        features = [
            {
                "properties": {"name": "Feature1"},
                "geometry": {"type": "Point", "coordinates": [0, 0]},
            }
        ]
        with patch.object(Record.objects, "bulk_create") as mock_bulk_create:
            records = self.instance.create_records_from_features(
                features, self.layer, Record
            )

            # Assertions
            self.assertEqual(len(records), 0)  # No valid records should be created
            mock_bulk_create.assert_not_called()
        mock_geometry = MagicMock()
        mock_geometry.valid = False
        mock_get_valid_geometry.return_value = mock_geometry
        with patch.object(Record.objects, "bulk_create") as mock_bulk_create:
            records = self.instance.create_records_from_features(
                features, self.layer, Record
            )

            # Assertions
            self.assertEqual(len(records), 0)  # No valid records should be created
            mock_bulk_create.assert_not_called()

    @patch("deals.scripts.load_ren_locations.get_valid_geometry")
    def test_create_records_with_invalid_geometry(self, mock_get_valid_geometry):
        features = [
            {
                "properties": {"name": "Feature1"},
                "geometry": {"type": "Point", "coordinates": [0, 0]},
            }
        ]
        mock_geometry = MagicMock()
        mock_geometry.valid = False
        mock_get_valid_geometry.return_value = mock_geometry
        with patch.object(Record.objects, "bulk_create") as mock_bulk_create:
            records = self.instance.create_records_from_features(
                features, self.layer, Record
            )
            # Assertions
            self.assertEqual(len(records), 0)  # No valid records should be created
            mock_bulk_create.assert_not_called()

    def test_read_records_from_geopackage(self):
        region_file_object = self.files[LAYERS_KEYS["region"]]
        region_file_object = open_storage_file(region_file_object)
        result = self.instance.read_records_from_geopackage(file=region_file_object)
        result = list(result)
        self.assertEqual(
            len(result), 12
        )  # Ensure we get the expected number of features

    def test_valid_execution_file(self):
        region_file = self.files[LAYERS_KEYS["region"]]
        layer = Layer.objects.get(key=LAYERS_KEYS["region"])
        instance = LoadRenLocations(
            metadata={"file": region_file, "layer": layer, "patch_size": 100}
        )
        result = instance.execute()
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 12)
        record = result[0]
        self.assertIsInstance(record, Record)
        self.assertEqual(record.layer_id, layer.id)
