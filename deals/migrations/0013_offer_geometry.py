# Generated by Django 3.2.25 on 2024-12-02 15:55

import django.contrib.gis.db.models.fields
from django.db import migrations
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('deals', '0012_auto_20241128_1116'),
    ]

    operations = [
        migrations.AddField(
            model_name='offer',
            name='record',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='offer', to='deals.record', verbose_name='Record'),
        ),

        migrations.AddField(
            model_name='layer',
            name='boundaries',
            field=django.contrib.gis.db.models.fields.PolygonField(blank=True, null=True, srid=4326,
                                                                   verbose_name='Boundaries'),
        ),
    ]
