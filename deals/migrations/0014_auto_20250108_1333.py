# Generated by Django 3.2.25 on 2025-01-08 13:33

from django.db import migrations, models
import django.db.models.expressions
import jsoneditor.fields.django3_jsonfield


class Migration(migrations.Migration):

    dependencies = [
        ('deals', '0013_offer_geometry'),
    ]

    operations = [
        migrations.AlterField(
            model_name='beneficiary',
            name='data',
            field=jsoneditor.fields.django3_jsonfield.JSONField(blank=True, default=dict, null=True, verbose_name='Data'),
        ),
        migrations.AlterField(
            model_name='deal',
            name='form_data',
            field=jsoneditor.fields.django3_jsonfield.JSONField(blank=True, null=True, verbose_name='Form Data'),
        ),
        migrations.AlterField(
            model_name='offer',
            name='data',
            field=jsoneditor.fields.django3_jsonfield.JSONField(blank=True, null=True, verbose_name='Offer Data'),
        ),
        migrations.AddIndex(
            model_name='beneficiary',
            index=models.Index(django.db.models.expressions.F('data__ID_CODE'), name='beneficiary_id_code'),
        ),
        migrations.AddIndex(
            model_name='beneficiary',
            index=models.Index(django.db.models.expressions.F('data__personalData__mobileNumber'), name='beneficiary_phone'),
        ),
        migrations.AddIndex(
            model_name='beneficiary',
            index=models.Index(django.db.models.expressions.F('data__location__region'), name='beneficiary_region'),
        ),
        migrations.AddIndex(
            model_name='beneficiary',
            index=models.Index(django.db.models.expressions.F('data__location__city'), name='beneficiary_city'),
        ),
        migrations.AddIndex(
            model_name='beneficiary',
            index=models.Index(django.db.models.expressions.F('data__location__product'), name='beneficiary_product'),
        ),
        migrations.AddIndex(
            model_name='beneficiary',
            index=models.Index(django.db.models.expressions.F('data__professionalData__job'), name='beneficiary_job'),
        ),
        migrations.AddIndex(
            model_name='beneficiary',
            index=models.Index(django.db.models.expressions.F('data__financialData__salaryBank'), name='beneficiary_salary_bank'),
        ),
        migrations.AddIndex(
            model_name='beneficiary',
            index=models.Index(django.db.models.expressions.F('data__personalData__age'), name='beneficiary_age'),
        ),
        migrations.AddIndex(
            model_name='beneficiary',
            index=models.Index(django.db.models.expressions.F('data__familyData__familyMembersCount'), name='beneficiary_family_member'),
        ),
        migrations.AddIndex(
            model_name='beneficiary',
            index=models.Index(django.db.models.expressions.F('data__financialData__fundingAmount'), name='beneficiary_funding_amount'),
        ),
        migrations.AddIndex(
            model_name='offer',
            index=models.Index(django.db.models.expressions.F('data__completingOffer__offerData__id'), name='offer_id'),
        ),
        migrations.AddIndex(
            model_name='record',
            index=models.Index(django.db.models.expressions.F('source_properties__id'), name='record_id'),
        ),
        migrations.AddIndex(
            model_name='record',
            index=models.Index(django.db.models.expressions.F('source_properties__city_id'), name='record_city_id'),
        ),
        migrations.AddIndex(
            model_name='record',
            index=models.Index(django.db.models.expressions.F('source_properties__zone_id'), name='record_zone_id'),
        ),
        migrations.AddIndex(
            model_name='record',
            index=models.Index(django.db.models.expressions.F('source_properties__region_id'), name='record_region_id'),
        ),
    ]
