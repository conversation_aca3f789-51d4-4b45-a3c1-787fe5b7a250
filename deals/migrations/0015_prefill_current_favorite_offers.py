# Generated by Django 3.2 on 2025-01-01 09:24
import logging

from django.db import migrations, models

logger = logging.getLogger("deals")


def prefill_current_favorite_offers(apps, schema_editor):
    FavoriteOffer = apps.get_model("deals", "FavoriteOffer")
    favorite_offers = FavoriteOffer.objects.only("id", "offer_id")
    logger.debug(
        f"prefill_current_favorite_offers: favorite_offers_count: {len(favorite_offers)}"
    )
    for favorite_offer in favorite_offers:
        favorite_offer.new_offer_id = favorite_offer.offer_id
        logger.debug(
            f"prefill_current_favorite_offers: favorite_offer_id: {favorite_offer.id} new_offer_id: {favorite_offer.new_offer_id}"
        )
    FavoriteOffer.objects.bulk_update(favorite_offers, ["new_offer_id"])


class Migration(migrations.Migration):

    dependencies = [("deals", "0014_auto_20250108_1333")]

    operations = [
        migrations.AddField(
            model_name="favoriteoffer",
            name="new_offer_id",
            field=models.IntegerField(
                db_index=True, null=True, verbose_name="Offer ID"
            ),
        ),
        migrations.RunPython(prefill_current_favorite_offers, migrations.RunPython.noop)
    ]
