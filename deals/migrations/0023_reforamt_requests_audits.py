# Generated by Django 3.2.25 on 2025-03-05 09:28
import logging

from django.db import migrations
from django.db.models import Q

from auditing.models import Audit
from deals.models import RequestStatus, Request

logger = logging.getLogger("deals")

def reformat_requests_audits(apps, schema_editor):
    ContentType = apps.get_model('contenttypes', 'ContentType')
    requests_audits = Audit.objects.filter(
        ~Q(data__data={}) & ~Q(data__data=[]) & Q(data__data__isnull=False),
        content_type=ContentType.objects.get_for_model(Request).id,
    ).only("data")
    logger.debug(
        f"reformat_requests_audits: requests_audits: {requests_audits.count()}, audits: {list(requests_audits.values_list('id', flat=True))}"
    )
    new_audits = []
    for request in requests_audits:
        logger.debug(
            f"reformat_requests_audits: requests_audits_id: {request.id}"
        )
        request_data = request.data or {}
        status = request_data.get("status")
        if isinstance(status, int):
            if request_data.get("status") == 2:
                request.data = {
                    "offers": {},
                    "status": {str(RequestStatus.REJECTED.value): str(
                            RequestStatus.REJECTED.label
                    )}
                }
        if isinstance(status, dict):
            if "1" in status:
                request.data = {
                    "offers": {},
                    "status": request_data.get("status", {})
                }

            elif "3" in status:
                request.data = {
                    "offers": {str(request_data.get("data", {}).get("offer")): {}},
                    "status": request_data.get("status", {})
                }
            elif "4" in status:
                offer_id = request_data.get("data", {}).get("offers", [])
                request.data = {
                    "offers": {str(offer_id[0]): {}} if offer_id else {},
                    "status": request_data.get("status", {})
                }
        new_audits.append(request)
    Audit.objects.bulk_update(new_audits, ["data"])


class Migration(migrations.Migration):

    dependencies = [
        ('deals', '0022_reformat_records_geometry'),
    ]

    operations = [
        migrations.RunPython(reformat_requests_audits),
    ]
