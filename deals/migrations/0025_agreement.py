# Generated by Django 3.2.25 on 2025-03-10 12:03

import django.db.models.deletion
import django_extensions.db.fields
import jsoneditor.fields.django3_jsonfield
from django.db import migrations, models





class Migration(migrations.Migration):

    dependencies = [
        # ('orders', '0026_auto_20250108_1333'),
        ('deals', '0024_reforamt_accepted_requests_audits'),
    ]

    operations = [
        migrations.CreateModel(
            name='Agreement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('status', models.PositiveSmallIntegerField(choices=[(1, 'Choosing Offer'), (2, 'In Progress'), (3, 'Deal Finished'), (4, 'Deal Cancelled')], db_index=True, default=2, verbose_name='Status')),
                ('form_data', jsoneditor.fields.django3_jsonfield.JSONField(blank=True, null=True, verbose_name='Form Data')),
                ('task', models.OneToOneField(on_delete=django.db.models.deletion.PROTECT, related_name='agreement', to='orders.task', verbose_name='Task')),
            ],
            options={
                'verbose_name': 'Agreement',
                'verbose_name_plural': 'Agreements',
            },
        ),
    ]
