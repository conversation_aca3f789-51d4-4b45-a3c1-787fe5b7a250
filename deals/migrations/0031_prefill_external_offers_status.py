# Generated by Django 3.2.25 on 2025-04-15 08:25
import logging

from django.db import migrations

from deals.models import DealStatus, OfferStatus

logger = logging.getLogger("deals")


def prefill_external_offers_status(apps, schema_editor):
    ExternalOffer = apps.get_model("deals", "ExternalOffer")
    Deal = apps.get_model("deals", "Deal")
    deals = Deal.objects.filter(status=DealStatus.DEAL_FINISHED).select_related(
        "favorite_offer"
    )
    external_offer_ids = deals.values_list("favorite_offer__offer_id", flat=True)
    external_offers = ExternalOffer.objects.filter(
        external_offer_id__in=external_offer_ids
    ).only("external_offer_id")
    logger.debug(
        f"prefill_external_offers_status: offers: {len(external_offer_ids)},\n offers: {list(external_offer_ids)} \n"
    )
    for external_offer in external_offers:
        logger.debug(f"prefill_external_offers_status: offer: {external_offer.id}")
        external_offer.status = OfferStatus.RESERVED

    ExternalOffer.objects.bulk_update(external_offers, ["status"])


class Migration(migrations.Migration):

    dependencies = [
        ("deals", "0030_externaloffer_status"),
    ]

    operations = [migrations.RunPython(prefill_external_offers_status)]
