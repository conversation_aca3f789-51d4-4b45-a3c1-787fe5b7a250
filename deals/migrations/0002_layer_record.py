# Generated by Django 3.2.25 on 2024-07-10 11:15

import django.contrib.gis.db.models.fields
from django.db import migrations, models
import django.db.models.deletion
import django_extensions.db.fields
import jsoneditor.fields.django3_jsonfield


class Migration(migrations.Migration):

    dependencies = [
        ("deals", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Layer",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "key",
                    models.SlugField(
                        max_length=200, unique=True, verbose_name="Unique Form Key"
                    ),
                ),
                (
                    "title",
                    models.Char<PERSON>ield(
                        blank=True,
                        max_length=200,
                        null=True,
                        verbose_name="Layer Title",
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True, null=True, verbose_name="Layer Description"
                    ),
                ),
                (
                    "metadata",
                    jsoneditor.fields.django3_jsonfield.JSONField(
                        blank=True, default=dict, verbose_name="Metadata"
                    ),
                ),
            ],
            options={
                "verbose_name": "Layer",
                "verbose_name_plural": "Layers",
            },
        ),
        migrations.CreateModel(
            name="Record",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "geometry",
                    django.contrib.gis.db.models.fields.GeometryField(
                        blank=True,
                        null=True,
                        srid=4326,
                        verbose_name="Geometry Collection Record",
                    ),
                ),
                (
                    "source_properties",
                    jsoneditor.fields.django3_jsonfield.JSONField(
                        blank=True, null=True, verbose_name="Source Properties"
                    ),
                ),
                (
                    "layer",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="deals.layer",
                        verbose_name="Related Layer",
                    ),
                ),
            ],
            options={
                "verbose_name": "Geometry Record",
                "verbose_name_plural": "Geometry Records",
            },
        ),
    ]
