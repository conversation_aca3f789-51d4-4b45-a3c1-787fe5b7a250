# Generated by Django 3.2.25 on 2025-04-07 13:26

import logging

from django.contrib.contenttypes.models import ContentType
from django.db import migrations
from django.db.models import Q

from auditing.models import Actions

logger = logging.getLogger("deals")


def prefill_external_offers_data(apps, external_offer):
    Audit = apps.get_model("auditing", "Audit")
    Request = apps.get_model("deals", "Request")
    offer_audits = (
        Audit.objects.filter(
            ~Q(data__offers={}),
            action=Actions.ADD,
            content_type=ContentType.objects.get_for_model(Request).id,
            data__isnull=False,
            data__offers__has_key=str(external_offer.external_offer_id),
        )
        .only("id", "data", "created")
        .order_by("-created")
        .first()
    )
    external_offer.data = (
        offer_audits.data.get("offers", {}).get(str(external_offer.external_offer_id))
        if offer_audits
        else None
    )
    return external_offer


def prefill_requests_offer_data(apps, schema_editor):
    OfferRequest = apps.get_model("deals", "OfferRequest")
    Request = apps.get_model("deals", "Request")
    Audit = apps.get_model("auditing", "Audit")
    ExternalOffer = apps.get_model("deals", "ExternalOffer")

    requests_audits = (
        Audit.objects.filter(
            ~Q(data__offers={}),
            action=Actions.ADD,
            content_type=ContentType.objects.get_for_model(Request).id,
            data__isnull=False,
        )
        .distinct("object_id")
        .values_list("object_id", "data__offers")
    )

    offers_requests = OfferRequest.objects.only("request", "id", "data")
    requests_audits_map = {object_id: offers for object_id, offers in requests_audits}
    updated_external_offers = []

    for offer_request in offers_requests:
        offer_obj = offer_request.external_offer or offer_request.internal_offer
        if not offer_obj:
            continue
        offer_data = requests_audits_map.get(str(offer_request.request_id), {}).get(
            str(offer_obj.external_offer_id)
        )
        if not offer_data:
            request_audit = (
                Audit.objects.filter(
                    ~Q(data__offers={}),
                    action=Actions.ADD,
                    content_type=ContentType.objects.get_for_model(Request).id,
                    data__isnull=False,
                    data__offers__has_key=str(offer_obj.external_offer_id),
                )
                .only("id", "data", "created")
                .order_by("-created")
                .first()
            )
            offer_data = (
                request_audit.data.get("offers", {}).get(
                    str(offer_obj.external_offer_id)
                )
                if request_audit
                else None
            )

        # check if ExternalOffer Data is Updated
        if offer_request.external_offer and not offer_request.external_offer.data:
            external_offer = prefill_external_offers_data(
                apps=apps, external_offer=offer_request.external_offer
            )
            updated_external_offers.append(external_offer)

        offer_request.data = offer_data

    OfferRequest.objects.bulk_update(offers_requests, ["data"])
    if updated_external_offers:
        ExternalOffer.objects.bulk_update(updated_external_offers, ["data"])

    logger.debug(
        f"prefill_requests_offer_data: requests: {offers_requests.count()},\n requests: {list(offers_requests.values_list('id', flat=True))} \n"
    )


def prefill_requests_offers(apps, schema_editor):
    # Fetch necessary data upfront
    Request = apps.get_model("deals", "Request")
    Offer = apps.get_model("deals", "Offer")
    ExternalOffer = apps.get_model("deals", "ExternalOffer")
    OfferRequest = apps.get_model("deals", "OfferRequest")

    # Pre-fetch requests with non-null data and their offer ids
    requests = Request.objects.filter(data__isnull=False).only("id", "data")
    offers_ids = list(requests.values_list("data__offers", flat=True))
    unique_offer_ids = {offer_id for sublist in offers_ids for offer_id in sublist}
    internal_offers = Offer.objects.filter(external_offer_id__in=unique_offer_ids).only(
        "id", "external_offer_id"
    )

    # Create a mapping of external_offer_id
    internal_offer_map = {offer.external_offer_id: offer for offer in internal_offers}
    existing_external_offers = dict()
    created_offer_requests = []
    new_external_offers = []
    existing_offer_requests = set()
    logger.debug(
        f"prefill_requests_offers: requests: {requests.count()}, requests: {list(requests.values_list('id', flat=True))}"
    )

    for request in requests:
        logger.debug(f"prefill_requests_offers: request_id: {request.id}")
        offers = request.data.get("offers", [])

        for offer_id in offers:
            # Check if it's an internal offer first
            internal_offer = internal_offer_map.get(offer_id)
            if (
                internal_offer
                and (request.id, internal_offer.external_offer_id)
                not in existing_offer_requests
            ):
                created_offer_requests.append(
                    OfferRequest(
                        internal_offer=internal_offer,
                        request=request,
                        created=request.created,
                    )
                )
                existing_offer_requests.add(
                    (request.id, internal_offer.external_offer_id)
                )
            else:
                # If it's an external offer and doesn't already exist, create it
                if offer_id not in existing_external_offers.keys():
                    external_offer = ExternalOffer(external_offer_id=offer_id)
                    new_external_offers.append(external_offer)
                    existing_external_offers[offer_id] = external_offer

                # Create OfferRequest if this combination is unique
                if (request.id, offer_id) not in existing_offer_requests:
                    offer = existing_external_offers[offer_id]
                    created_offer_requests.append(
                        OfferRequest(
                            external_offer=offer,
                            request=request,
                            created=request.created,
                        )
                    )
                    existing_offer_requests.add((request.id, offer_id))

        request.data = None

    if new_external_offers:
        ExternalOffer.objects.bulk_create(new_external_offers)

    OfferRequest.objects.bulk_create(created_offer_requests)

    Request.objects.bulk_update(requests, fields=["data"])

    logger.debug(
        f"prefill_requests_offers: ExternalOffer: {len(new_external_offers)},"
        f"OfferRequest: {len(created_offer_requests)}"
    )


class Migration(migrations.Migration):

    dependencies = [
        ("deals", "0028_auto_20250412_1111"),
    ]

    operations = [
        migrations.RunPython(prefill_requests_offers),
        migrations.RunPython(prefill_requests_offer_data),
    ]
