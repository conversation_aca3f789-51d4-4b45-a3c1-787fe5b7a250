# Generated by Django 3.2 on 2024-08-15 10:52

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("deals", "0007_favoriteoffer_license_number"),
    ]

    operations = [
        migrations.AlterField(
            model_name="favoriteoffer",
            name="status",
            field=models.PositiveSmallIntegerField(
                choices=[
                    (1, "Reserved"),
                    (2, "Not Reserved"),
                    (3, "Reserved For Time"),
                ],
                db_index=True,
                default=2,
                verbose_name="Status",
            ),
        ),
        migrations.AddConstraint(
            model_name="favoriteoffer",
            constraint=models.UniqueConstraint(
                fields=("task", "license_number"), name="unique_task_license_number"
            ),
        ),
    ]
