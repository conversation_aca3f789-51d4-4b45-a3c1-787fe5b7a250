# Generated by Django 3.2.25 on 2025-02-18 08:36
import logging
from django.db import migrations
from utils.geometry import convert_geometry
logger = logging.getLogger("deals")


def reformat_records_geometry(apps, schema_editor):
    Record = apps.get_model('deals', 'Record')
    records = Record.objects.filter(layer__key="ren-offers", geometry__isnull=False).only("geometry", "id")
    updated_records = []
    logger.debug(
        f"reformat_records_geometry: records_count: {records.count()}, records: {list(records.values_list('id', flat=True))}"
    )
    for record in records:
        logger.debug(
            f"reformat_records_geometry: record: {record.id}"
        )
        geometry = convert_geometry(str(record.geometry.wkt))
        record.geometry = geometry
        updated_records.append(record)

    Record.objects.bulk_update(updated_records, ["geometry"])


class Migration(migrations.Migration):

    dependencies = [
        ('deals', '0021_add_offers_to_vd'),
    ]

    operations = [
        migrations.RunPython(reformat_records_geometry)
    ]
