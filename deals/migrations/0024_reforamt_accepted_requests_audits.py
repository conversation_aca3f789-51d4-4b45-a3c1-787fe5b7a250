# Generated by Django 3.2.25 on 2025-03-05 13:06

import logging

from django.db import migrations
from django.db.models import Q

from auditing.models import Audit
from deals.models import RequestStatus, Request
logger = logging.getLogger("deals")


def reformat_accepted_requests_audits(apps, schema_editor):
    ContentType = apps.get_model('contenttypes', 'ContentType')
    FavoriteOffer = apps.get_model('deals', 'FavoriteOffer')
    requests_audits = Audit.objects.filter(
        data__status__isnull=False,
        data__status__has_key="3",
        content_type=ContentType.objects.get_for_model(Request).id,
    ).only("data")
    logger.debug(
        f"reformat_accepted_requests_audits: requests_audits: {requests_audits.count()}, audits: {list(requests_audits.values_list('id', flat=True))}"
    )
    new_audits = []
    for request in requests_audits:
        logger.debug(
            f"reformat_accepted_requests_audits: requests_audits_id: {request.id}"
        )
        request_data = request.data or {}
        status = request_data.get("status", {})
        favorite_offer_id = request_data.get("offer")
        offer_id = FavoriteOffer.objects.filter(id=favorite_offer_id).first()
        request.data = {
                "offers": {str(offer_id.offer_id): {}} if offer_id else {},
                "status": status
            }

        new_audits.append(request)
    Audit.objects.bulk_update(new_audits, ["data"])



class Migration(migrations.Migration):

    dependencies = [
        ('deals', '0023_reforamt_requests_audits'),
    ]

    operations = [
        migrations.RunPython(reformat_accepted_requests_audits),
    ]
