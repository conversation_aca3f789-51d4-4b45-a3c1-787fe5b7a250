# Generated by Django 3.2.25 on 2024-11-27 09:48

import django_extensions.db.fields
import jsoneditor.fields.django3_jsonfield
from django.db import migrations, models






class Migration(migrations.Migration):

    dependencies = [
        ('deals', '0009_alter_deal_status'),
    ]

    operations = [
        migrations.CreateModel(
            name='Offer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('license_number', models.CharField(blank=True, max_length=255, null=True, verbose_name='License Number')),
                ('offer_type', models.PositiveSmallIntegerField(choices=[(1, 'Internal'), (2, 'External')], db_index=True, default=1, verbose_name='Offer Type')),
                ('status', models.PositiveSmallIntegerField(choices=[(1, 'Reserved'), (2, 'Not Reserved'), (3, 'Reserved For Time')], db_index=True, default=2, verbose_name='Status')),
                ('data', jsoneditor.fields.django3_jsonfield.JSONField(blank=True, db_index=True, null=True, verbose_name='Offer Data')),
            ],
            options={
                'verbose_name': 'Offer',
                'verbose_name_plural': 'Offers',
            },
        ),
    ]
