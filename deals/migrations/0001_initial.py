# Generated by Django 3.2.25 on 2024-06-20 15:01

from django.db import migrations, models
import jsoneditor.fields.django3_jsonfield


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Beneficiary",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        db_index=True, max_length=255, verbose_name="Code"
                    ),
                ),
                (
                    "external_id",
                    models.IntegerField(db_index=True, verbose_name="External ID"),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="Name"
                    ),
                ),
                (
                    "data",
                    jsoneditor.fields.django3_jsonfield.JSONField(
                        blank=True,
                        db_index=True,
                        default=dict,
                        null=True,
                        verbose_name="Data",
                    ),
                ),
                (
                    "status",
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, "Not Assigned"),
                            (2, "Assigned To Customer Service"),
                            (3, "Assigned To Negotiator"),
                            (4, "Closed"),
                        ],
                        db_index=True,
                        default=1,
                        verbose_name="Status",
                    ),
                ),
            ],
            options={
                "verbose_name": "Beneficiary",
                "verbose_name_plural": "Beneficiaries",
                "unique_together": {("code", "external_id")},
            },
        ),
    ]
