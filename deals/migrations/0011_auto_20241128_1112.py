# Generated by Django 3.2.25 on 2024-11-28 11:12
import logging

import django.db.models.deletion
from django.db import migrations
from django.db import migrations, models

logger = logging.getLogger("deals")

def _create_new_offers(apps, schema_editor):
    Offer = apps.get_model('deals', 'Offer')
    FavoriteOffer = apps.get_model('deals', 'FavoriteOffer')
    favorite_offers = FavoriteOffer.objects.all()
    created_offers = {}
    updated_favorite_offers = []
    logger.debug(f"_create_new_offers: favorite_offers_count: {favorite_offers.count()}")
    for favorite_offer in favorite_offers:
        logger.debug(f"_create_new_offers: favorite_offer_id: {favorite_offer.id} offer_status: {favorite_offer.get_status_display()}")
        offer = Offer(
            license_number=favorite_offer.license_number,
            offer_type=favorite_offer.offer_type,
            status=favorite_offer.status,
            data=favorite_offer.data,
        )
        created_offers[favorite_offer] = offer
    if created_offers:
        Offer.objects.bulk_create(list(created_offers.values()))
        for favorite_offer, offer in created_offers.items():
            favorite_offer.offer = offer
            updated_favorite_offers.append(favorite_offer)
        FavoriteOffer.objects.bulk_update(updated_favorite_offers, ['offer'])

    logger.debug(f"_create_new_offers: offers_created_count: {len(created_offers)}")

class Migration(migrations.Migration):

    dependencies = [
        ('deals', '0010_auto_20241127_0948'),
    ]

    operations = [
        migrations.AddField(
            model_name='favoriteoffer',
            name='offer',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE,
                                    related_name='favorite_offers', to='deals.offer', verbose_name='Favorite Offer'),
        ),
        migrations.RunPython(_create_new_offers),

    ]
