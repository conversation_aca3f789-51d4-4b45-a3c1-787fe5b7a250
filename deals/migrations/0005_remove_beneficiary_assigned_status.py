# Generated by Django 3.2.25 on 2024-07-29 11:05

from django.db import migrations


def move_assigned_status(apps, schema_editor):
    Task = apps.get_model("orders", "Task")
    tasks = Task.objects.all().select_related("beneficiary")
    for task in tasks:
        task.assigned_status = task.beneficiary.assigned_status
    Task.objects.bulk_update(tasks, fields=["assigned_status"])


class Migration(migrations.Migration):

    dependencies = [
        ("deals", "0004_auto_20240724_0932"),
        ("orders", "0006_task_assigned_status"),
    ]

    operations = [
        migrations.RunPython(move_assigned_status),
        migrations.RemoveField(
            model_name="beneficiary",
            name="assigned_status",
        ),
    ]
