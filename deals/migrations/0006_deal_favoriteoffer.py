# Generated by Django 3.2 on 2024-08-14 14:41

from django.db import migrations, models
import django.db.models.deletion
import django_extensions.db.fields
import jsoneditor.fields.django3_jsonfield


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0007_fulfill_task_data"),
        ("deals", "0005_remove_beneficiary_assigned_status"),
    ]

    operations = [
        migrations.CreateModel(
            name="FavoriteOffer",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "offer_type",
                    models.PositiveSmallIntegerField(
                        choices=[(1, "Internal"), (2, "External")],
                        db_index=True,
                        default=2,
                        verbose_name="Offer Type",
                    ),
                ),
                (
                    "status",
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, "Reserved"),
                            (2, "Not Reserved"),
                            (3, "Reserved For 48 Hour"),
                        ],
                        db_index=True,
                        default=1,
                        verbose_name="Status",
                    ),
                ),
                (
                    "data",
                    jsoneditor.fields.django3_jsonfield.JSONField(
                        blank=True, db_index=True, null=True, verbose_name="Offer Data"
                    ),
                ),
                (
                    "task",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="favorite_offers",
                        to="orders.task",
                        verbose_name="Task",
                    ),
                ),
            ],
            options={
                "verbose_name": "Favorite Offer",
                "verbose_name_plural": "Favorite Offers",
            },
        ),
        migrations.CreateModel(
            name="Deal",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "status",
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, "Choosing Offer"),
                            (2, "Scheduled Appointment"),
                            (3, "Negotiate on Price"),
                            (4, "Offer Proposal"),
                            (5, "Offer Accept Decline"),
                            (6, "Set Conditions"),
                            (7, "Prepare Contract"),
                            (8, "Review Contract"),
                            (9, "Collect Signature"),
                            (10, "Rate Offer"),
                            (11, "Collect Cash"),
                            (12, "Legal Check"),
                            (13, "Final Review"),
                            (14, "Transfer Cash"),
                            (15, "Collect Keys"),
                            (16, "Deal Finished"),
                            (17, "Deal Cancelled"),
                        ],
                        db_index=True,
                        default=1,
                        verbose_name="Status",
                    ),
                ),
                (
                    "form_data",
                    jsoneditor.fields.django3_jsonfield.JSONField(
                        blank=True, db_index=True, null=True, verbose_name="Form Data"
                    ),
                ),
                (
                    "favorite_offer",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="deal",
                        to="deals.favoriteoffer",
                        verbose_name="Offer",
                    ),
                ),
            ],
            options={
                "verbose_name": "Deal",
                "verbose_name_plural": "Deals",
            },
        ),
    ]
