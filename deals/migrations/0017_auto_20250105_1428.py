# Generated by Django 3.2.25 on 2025-01-05 14:28

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_extensions.db.fields
import jsoneditor.fields.django3_jsonfield


class Migration(migrations.Migration):

    dependencies = [
        # ('orders', '0025_alter_task_status'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('deals', '0016_auto_20250101_0926'),
    ]

    operations = [
        migrations.AddField(
            model_name='beneficiary',
            name='user',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='beneficiary', to=settings.AUTH_USER_MODEL, verbose_name='User'),
        ),
        migrations.AddField(
            model_name='favoriteoffer',
            name='reserved',
            field=models.BooleanField(default=False, verbose_name='Reserved'),
        ),
        migrations.CreateModel(
            name='Request',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('status', models.PositiveSmallIntegerField(choices=[(1, 'Pending'), (2, 'Send'), (3, 'Accepted'), (4, 'Rejected')], db_index=True, default=1, verbose_name='Record Status')),
                ('data', jsoneditor.fields.django3_jsonfield.JSONField(blank=True, null=True, verbose_name='Request Data')),
                ('task', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='requests', to='orders.task', verbose_name='Task')),
            ],
            options={
                'verbose_name': 'Request ',
                'verbose_name_plural': 'Requests',
            },
        ),
    ]
