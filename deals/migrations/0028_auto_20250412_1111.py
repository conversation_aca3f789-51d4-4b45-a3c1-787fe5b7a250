# Generated by Django 3.2.25 on 2025-04-12 11:11

import django.db.models.deletion
import django_extensions.db.fields
import jsoneditor.fields.django3_jsonfield
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("deals", "0027_alter_favoriteoffer_offer_id"),
    ]

    operations = [
        migrations.CreateModel(
            name="ExternalOffer",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "external_offer_id",
                    models.PositiveBigIntegerField(
                        db_index=True, unique=True, verbose_name="External Offer ID"
                    ),
                ),
                (
                    "data",
                    jsoneditor.fields.django3_jsonfield.JSONField(
                        blank=True, null=True, verbose_name="Offer Data"
                    ),
                ),
            ],
            options={
                "verbose_name": "External Offer",
                "verbose_name_plural": "External Offers",
            },
        ),
        migrations.AlterModelOptions(
            name="offer",
            options={"verbose_name": "REN Offer", "verbose_name_plural": "REN Offers"},
        ),
        migrations.CreateModel(
            name="OfferRequest",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("INCLUDED", "included in request"),
                            ("EXCLUDED", "excluded from request"),
                        ],
                        db_index=True,
                        default="INCLUDED",
                        max_length=20,
                        verbose_name="Offer Status",
                    ),
                ),
                (
                    "data",
                    jsoneditor.fields.django3_jsonfield.JSONField(
                        blank=True, null=True, verbose_name="Offer Request Data"
                    ),
                ),
                (
                    "external_offer",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="requests",
                        to="deals.externaloffer",
                    ),
                ),
                (
                    "internal_offer",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="requests",
                        to="deals.offer",
                    ),
                ),
                (
                    "request",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="offers",
                        to="deals.request",
                    ),
                ),
            ],
            options={
                "verbose_name": "Offer Request",
                "verbose_name_plural": "Offer Requests",
                "unique_together": {
                    ("external_offer", "request"),
                    ("internal_offer", "request"),
                },
            },
        ),
    ]
