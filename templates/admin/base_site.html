{% extends "admin/base.html" %}
{% load i18n %}

{% block title %}{% if subtitle %}{{ subtitle }} | {% endif %}{{ title }} |
    {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block branding %}
    <h1 id="site-name"><a href="{% url 'admin:index' %}">{{ site_header|default:_('Django administration') }}</a></h1>
{% endblock %}

{% block nav-global %}
<form action="{% url 'set_language' %}" method="post">{% csrf_token %}
    <input name="next" type="hidden" value="{{ redirect_to }}">
    <select name="language">
        {% get_current_language as LANGUAGE_CODE %}
        {% get_available_languages as LANGUAGES %}
        {% get_language_info_list for LANGUAGES as languages %}
        {% for language in languages %}
            <option value="{{ language.code }}"{% if language.code == LANGUAGE_CODE %} selected{% endif %}>
                {{ language.name_local }} ({{ language.code }})
            </option>
        {% endfor %}
    </select>
    <input type="submit" value="Go">
</form>
{% endblock %}
{% block extrastyle %}
    <style>
        {# disable system default dark theme #}
        {# TODO: move to Gabbro #}
        @media (prefers-color-scheme: dark) {
            :root {
                --primary: #79aec8;
                --primary-fg: #fff;

                --body-fg: #333;
                --body-bg: #fff;
                --body-quiet-color: #666;
                --body-loud-color: #000;

                --breadcrumbs-fg: #c4dce8;
                --breadcrumbs-bg: var(--primary);

                --link-fg: #447e9b;
                --link-hover-color: #036;
                --link-selected-fg: #5b80b2;

                --hairline-color: #e8e8e8;
                --border-color: #ccc;

                --error-fg: #ba2121;

                --message-success-bg: #dfd;
                --message-warning-bg: #ffc;
                --message-error-bg: #ffefef;

                --darkened-bg: #f8f8f8; /* A bit darker than --body-bg */
                --selected-bg: #e4e4e4; /* E.g. selected table cells */
                --selected-row: #ffc;

                --close-button-bg: #888; /* Previously #bbb, contrast 1.92 */
                --close-button-hover-bg: #747474;
            }
        }
    </style>
{% endblock %}
