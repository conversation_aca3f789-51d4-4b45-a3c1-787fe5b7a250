from django.utils.translation import gettext_lazy as _
from notifications.models import Notification

from orders.models import Task
from users.models import User, UserRoleChoices
from utils.general_utils import create_notification


class NotificationMixin:
    @staticmethod
    def send_notification_for_bulk_assign(
        tasks: [Task], recipient: User, user: User, role: UserRoleChoices
    ):
        tasks_count = len(tasks)
        if tasks_count == 1:
            task = tasks.first()
            notifications = [
                # send notification to customer_service
                {
                    "description": "You have been assigned a new task: {task_id}.",
                    "target": task,
                    "recipient": recipient,
                    "title": _("New Task Assigned") % {},
                    "actor": user,
                    "level": Notification.LEVELS.info,
                    "data": {
                        "translated_keys": {"task_id": task.id},
                        "user_role": {"key": role.label, "value": role.value},
                    },
                },
                # send notifications to project_manager
                {
                    "actor": user,
                    "description": "Task {task_id} reassigned to {customer_service_name} successfully.",
                    "recipient": recipient,
                    "title": _("New Task Assigned") % {},
                    "level": Notification.LEVELS.info,
                    "data": {
                        "translated_keys": {
                            "task_id": task.id,
                            "customer_service_name": f"{recipient.first_name} {recipient.last_name}",
                        },
                        "user_role": {"key": role.label, "value": role.value},
                    },
                },
            ]
        else:
            notifications = [
                # send notification to customer_service
                {
                    "description": "You have been assigned {tasks_count} new tasks.",
                    "recipient": recipient,
                    "title": _("New Task Assigned") % {},
                    "actor": user,
                    "level": Notification.LEVELS.info,
                    "data": {
                        "user_role": {"key": role.label, "value": role.value},
                        "translated_keys": {"tasks_count": tasks_count},
                    },
                },
                # send notifications to project_manager
                {
                    "actor": user,
                    "description": "{tasks_count} Tasks reassigned successfully to {customer_service_name}.",
                    "recipient": recipient,
                    "title": _("New Task Assigned") % {},
                    "level": Notification.LEVELS.info,
                    "data": {
                        "translated_keys": {
                            "customer_service_name": f"{recipient.first_name} {recipient.last_name}",
                            "tasks_count": tasks_count,
                        },
                        "user_role": {"key": role.label, "value": role.value},
                    },
                },
            ]

        create_notification(notifications=notifications)
