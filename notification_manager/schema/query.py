import graphene
from django.contrib.auth import get_user_model

from notification_manager.schema.object_types import NotificationListType
from utils.graphene.decorators import authentication_required
from utils.graphene.query import (
    PageInfo,
    DjangoFilterInput,
    build_q,
    filter_qs_paginate_with_count,
)

User = get_user_model()


class Query(graphene.ObjectType):
    notifications = graphene.Field(
        NotificationListType,
        pk=graphene.Int(),
        page_info=PageInfo(),
        filters=graphene.List(DjangoFilterInput),
    )

    @staticmethod
    @authentication_required
    def resolve_notifications(root, info, pk: int = None, page_info=None, filters=None):
        user: User = info.context.user
        queryset = user.notifications.active()
        unread_count = user.notifications.unread().count()
        return NotificationListType(
            *filter_qs_paginate_with_count(queryset, build_q(pk, filters), page_info),
            unread_count,
        )
