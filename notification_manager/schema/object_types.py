import graphene
from django.utils.translation import gettext_lazy as _
from graphene_django.types import DjangoObjectType
from notifications.models import Notification
from django.contrib.contenttypes.models import ContentType
from orders.models import Task
from graphene_gis.scalars import JSONScalar


class NotificationType(DjangoObjectType):
    user_role = graphene.Field(JSONScalar)
    timesince = graphene.String()
    actor = graphene.String()
    task_id = graphene.Int()

    class Meta:
        model = Notification
        fields = [
            "id",
            "level",
            "unread",
            "description",
            "verb",
            "timesince",
            "data",
        ]

    def resolve_timesince(self: Notification, info):
        return str(self.timestamp)

    def resolve_user_role(self: Notification, info):
        data = self.data or {}
        return data.get("user_role")

    def resolve_task_id(self: Notification, info):
        task_content_type = ContentType.objects.get_for_model(Task)
        target = self.target
        if target and self.target_content_type == task_content_type:
            return target.id

    def resolve_actor(self: Notification, info):
        return str(self.actor_content_type.model)

    def resolve_description(self: Notification, info):
        data = self.data or {}
        translated_keys = data.get("translated_keys", {})
        return _(self.description).format(**translated_keys)


class NotificationListType(graphene.ObjectType):
    data = graphene.List(NotificationType)
    count = graphene.Int()
    unread_count = graphene.Int()
