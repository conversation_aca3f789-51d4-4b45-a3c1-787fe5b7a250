import graphene
from django.utils.translation import gettext_lazy as _
from gabbro.graphene import NotFound

from notification_manager.schema.object_types import NotificationType
from utils.graphene.decorators import authentication_required


class MarkNotificationsAsRead(graphene.Mutation):
    count = graphene.Int()

    class Input:
        pk = graphene.Int(required=False)

    @staticmethod
    @authentication_required
    def mutate(root, info, pk=None):
        user = info.context.user
        if not pk:
            queryset = user.notifications.mark_all_as_read()
            return MarkNotificationsAsRead(count=queryset)
        notification = user.notifications.filter(pk=pk).first()
        if not notification:
            raise NotFound(
                reason={"notification": _("This notification not exist") % {}}
            )
        notification.mark_as_read()
        return MarkNotificationsAsRead(count=1)


class NotificationsDelete(graphene.Mutation):
    notification = graphene.Field(NotificationType)

    class Input:
        pk = graphene.Int()

    @staticmethod
    @authentication_required
    def mutate(root, info, pk):
        user = info.context.user
        notification = user.notifications.active().filter(pk=pk).first()
        if not notification:
            raise NotFound(
                reason={"notification": _("This notification not exist") % {}}
            )
        notification.deleted = True
        notification.save(update_fields=["deleted"])
        return NotificationsDelete(notification=notification)


class Mutation(graphene.ObjectType):
    mark_notifications_as_read_mutation = MarkNotificationsAsRead.Field()
    notifications_delete_mutation = NotificationsDelete.Field()
