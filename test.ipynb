{"cells": [{"cell_type": "code", "execution_count": null, "id": "1e007d34", "metadata": {}, "outputs": [], "source": ["# To access your Django app within a Jupyter Notebook, you need to set up the Django environment.\n", "import os\n", "import sys\n", "\n", "# Set the path to your Django project directory (where manage.py is located)\n", "sys.path.append('/path/to/your/django/project')\n", "\n", "# Set the DJANGO_SETTINGS_MODULE environment variable\n", "os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'your_project_name.settings')\n", "\n", "import django\n", "django.setup()\n", "\n", "# Now you can import your Django app models and use them\n", "# from your_app_name.models import YourModel"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}