version: '3.7'
services:
  ren-ci:
    build:
      context: .
      dockerfile: ./docker/ci/Dockerfile
    image: ren-ci
    container_name: ren-ci
    env_file:
      - .env
    ports:
      - "8000:8000"
    volumes:
      - .:/app
    command: /start-tests.sh

  db:
    image: kartoza/postgis:14-3.1
    container_name: ren-ci-db
    env_file:
      - .env
    ports:
      - "5434:5432"
    volumes:
      - postgres_data:/var/lib/postgresql
volumes:
  postgres_data:
