version: '3.7'
services:
  vd_backend:
    container_name: ren_backend
    build: .
    restart: always
    command: >
      sh -c "python manage.py migrate --no-input
             python manage.py collectstatic --no-input
             gunicorn --workers=2 app.wsgi:application --bind 0.0.0.0:8000 --reload"
    volumes:
      - .:/app
      - ren_static_volume:/app/static
    ports:
      - 8000:8000
    depends_on:
      - postgres

  postgres:
    image: kartoza/postgis
    container_name: ren_backend_db
    ports:
      - 5433:5432
    volumes:
      - vd_postgres_data:/var/lib/postgresql/data/
    environment:
      - POSTGRES_DB=ren_backend
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=passw0rd

volumes:
  ren_postgres_data:
  ren_static_volume:
