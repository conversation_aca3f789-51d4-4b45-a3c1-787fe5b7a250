# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-17 13:42+0200\n"
"PO-Revision-Date: 2025-01-19 12:50+0000\n"
"Last-Translator: admin <PERSON>eo <<EMAIL>>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 "
"&& n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"
"X-Translated-Using: django-rosetta 0.9.7\n"

#: app/urls.py:27 app/urls.py:28
msgid "Real Estate Negotiator"
msgstr "مفاوض عقاري"

#: auditing/apps.py:8
msgid "Auditing"
msgstr "التدقيق"

#: auditing/models/audit.py:10
msgid "Add"
msgstr "إضافة"

#: auditing/models/audit.py:11
msgid "Edit"
msgstr "تحرير"

#: auditing/models/audit.py:12
msgid "Delete"
msgstr "حذف"

#: auditing/models/audit.py:25 orders/models/notes.py:16
msgid "Created by"
msgstr "تم الإنشاء بواسطة"

#: auditing/models/audit.py:28
msgid "action"
msgstr "إجراء"

#: auditing/models/audit.py:30
msgid "data"
msgstr "بيانات"

#: auditing/models/audit.py:36
msgid "Audit"
msgstr "تدقيق"

#: auditing/models/audit.py:37 deals/models/agreements.py:28
#: deals/models/deal.py:33 deals/models/offers.py:105 orders/models/order.py:16
#: orders/models/task.py:97
msgid "Audits"
msgstr "عمليات التدقيق"

#: common/apps.py:8
msgid "Common"
msgstr "عام"

#: deals/admin/offer.py:45
msgid "Reservation Remaining Hours"
msgstr "ساعات الحجز المتبقية"

#: deals/apps.py:8 deals/models/deal.py:37
msgid "Deals"
msgstr "الصفقات"

#: deals/mixins/request.py:13
msgid "you dont have pending request"
msgstr ""

#: deals/mixins/request.py:18 deals/validators/offer.py:117
#: deals/validators/offer.py:213 deals/validators/request.py:60
#, python-format
msgid ""
"task %(task_id)s already has expired request, please reactivate the link"
msgstr ""

#: deals/models/agreements.py:21 deals/models/deal.py:26
#: deals/models/offers.py:94 orders/models/task.py:83
#: resource_management/models/resource_mangement.py:37
msgid "Status"
msgstr "الحالة"

#: deals/models/agreements.py:26 deals/models/deal.py:31
#: orders/models/task.py:94
msgid "Form Data"
msgstr "بيانات النموذج"

#: deals/models/agreements.py:31
msgid "Agreement"
msgstr ""

#: deals/models/agreements.py:32
msgid "Agreements"
msgstr ""

#: deals/models/beneficiary.py:27
#, fuzzy
#| msgid "Users"
msgid "User"
msgstr "المستخدمون"

#: deals/models/beneficiary.py:32
msgid "Code"
msgstr "كود"

#: deals/models/beneficiary.py:33
msgid "External ID"
msgstr "المعرف الخارجي"

#: deals/models/beneficiary.py:35
#: resource_management/models/resource_mangement.py:32
msgid "Name"
msgstr "الاسم"

#: deals/models/beneficiary.py:37
#: resource_management/models/resource_mangement.py:46
msgid "Data"
msgstr "البيانات"

#: deals/models/beneficiary.py:41 orders/models/task.py:77 users/models.py:14
msgid "Beneficiary"
msgstr "المستفيد"

#: deals/models/beneficiary.py:42 orders/models/order.py:12
msgid "Beneficiaries"
msgstr "المستفيدين"

#: deals/models/deal.py:9
msgid "Choosing Offer"
msgstr "اختيار العرض"

#: deals/models/deal.py:10 orders/models/task.py:30
msgid "In Progress"
msgstr "جاري التنفيذ"

#: deals/models/deal.py:11
msgid "Deal Finished"
msgstr "تم إنهاء الصفقة"

#: deals/models/deal.py:12
msgid "Deal Cancelled"
msgstr "تم إلغاء الصفقة"

#: deals/models/deal.py:20 deals/models/offers.py:108
msgid "Offer"
msgstr "العرض"

#: deals/models/deal.py:36
msgid "Deal"
msgstr "الصفقة"

#: deals/models/layer.py:9 formschemas/models/form_schema.py:12
msgid "Unique Form Key"
msgstr "مفتاح النموذج الفريد"

#: deals/models/layer.py:15
msgid "Layer Title"
msgstr "عنوان الطبقة"

#: deals/models/layer.py:18
msgid "Layer Description"
msgstr "وصف الطبقة"

#: deals/models/layer.py:21
msgid "Metadata"
msgstr "بيانات وصفية"

#: deals/models/layer.py:26
#, fuzzy
#| msgid "Beneficiaries"
msgid "Boundaries"
msgstr "المستفيدين"

#: deals/models/layer.py:30
msgid "Layer"
msgstr "الطبقة"

#: deals/models/layer.py:31
msgid "Layers"
msgstr "الطبقات"

#: deals/models/offers.py:64
msgid "Internal"
msgstr "داخلي"

#: deals/models/offers.py:65
msgid "External"
msgstr "خارجي"

#: deals/models/offers.py:69 deals/models/offers.py:155
#: deals/strategies/offers.py:456
msgid "Reserved"
msgstr "محجوز"

#: deals/models/offers.py:70 deals/strategies/offers.py:458
msgid "Not Reserved"
msgstr "غير محجوز"

#: deals/models/offers.py:71
msgid "Reserved For Time"
msgstr "محجوز لفترة"

#: deals/models/offers.py:76
#, fuzzy
#| msgid "External ID"
msgid "External Offer ID"
msgstr "المعرف الخارجي"

#: deals/models/offers.py:82
msgid "License Number"
msgstr "رقم الترخيص"

#: deals/models/offers.py:88
msgid "Offer Type"
msgstr "نوع العرض"

#: deals/models/offers.py:104
msgid "Offer Data"
msgstr "بيانات العرض"

#: deals/models/offers.py:109
#, fuzzy
#| msgid "Offer"
msgid "Offers"
msgstr "العرض"

#: deals/models/offers.py:149 deals/models/request.py:19
#: orders/models/notes.py:11 orders/models/task.py:100
msgid "Task"
msgstr "مهمة"

#: deals/models/offers.py:152
#, fuzzy
#| msgid "Offer Data"
msgid "Offer ID"
msgstr "بيانات العرض"

#: deals/models/offers.py:158
msgid "Favorite Offer"
msgstr "العرض المفضل"

#: deals/models/offers.py:159
msgid "Favorite Offers"
msgstr "العروض المفضلة"

#: deals/models/record.py:14
msgid "Related Layer"
msgstr "الطبقة المرتبطة"

#: deals/models/record.py:19
msgid "Geometry Collection Record"
msgstr "سجل مجموعة الهندسة"

#: deals/models/record.py:24
msgid "Source Properties"
msgstr "خصائص المصدر"

#: deals/models/record.py:28
msgid "Geometry Record"
msgstr "السجل الجغرافي"

#: deals/models/record.py:29
msgid "Geometry Records"
msgstr "السجلات الجغرافية"

#: deals/models/request.py:9
#: resource_management/models/resource_mangement.py:23
msgid "Pending"
msgstr "معلق"

#: deals/models/request.py:10
msgid "Sent"
msgstr ""

#: deals/models/request.py:11
msgid "Accepted"
msgstr ""

#: deals/models/request.py:12 orders/models/task.py:31
msgid "Rejected"
msgstr "مرفوض"

#: deals/models/request.py:26
#, fuzzy
#| msgid "Assigned Status"
msgid "Record Status"
msgstr "حالة التعيين"

#: deals/models/request.py:34
msgid "Request Data"
msgstr ""

#: deals/models/request.py:38
msgid "Request "
msgstr ""

#: deals/models/request.py:39
msgid "Requests"
msgstr ""

#: deals/permissions/deals.py:11 deals/permissions/deals.py:22
#: deals/permissions/deals.py:33 deals/permissions/offers.py:11
#: deals/permissions/offers.py:22 deals/permissions/offers.py:34
#: deals/permissions/offers.py:46 deals/permissions/request.py:16
#: deals/tests/test_schema/test_queries/test_deal.py:112
#: deals/tests/test_schema/test_queries/test_favorite_offer.py:90
#: orders/permissions/notes.py:17 orders/permissions/orders.py:11
#: orders/permissions/tasks.py:25 orders/permissions/tasks.py:41
#: orders/permissions/tasks.py:49 orders/permissions/tasks.py:56
#: orders/permissions/tasks.py:68 orders/permissions/tasks.py:75
#: orders/permissions/tasks.py:82 orders/permissions/tasks.py:89
#: orders/permissions/tasks.py:101
#: orders/tests/test_schema/test_mutations.py:138
#: orders/tests/test_schema/test_mutations.py:343
#: orders/tests/test_schema/test_mutations.py:522
#: orders/tests/test_schema/test_mutations.py:712
#: orders/tests/test_schema/test_mutations.py:882
#: orders/tests/test_schema/test_mutations.py:1041
#: orders/tests/test_schema/test_mutations.py:1197
#: orders/tests/test_schema/test_mutations.py:1312
#: orders/tests/test_schema/test_mutations.py:1466
#: orders/tests/test_schema/test_mutations.py:1634
#: orders/tests/test_schema/test_queries.py:119
#: orders/tests/test_schema/test_queries.py:431
#: orders/tests/test_schema/test_queries.py:443
#: orders/tests/test_schema/test_queries.py:643
msgid "Permission Denied"
msgstr "تم رفض الإذن"

#: deals/schema/mutations.py:190
#, fuzzy
#| msgid "Offer Deleted Successfully"
msgid "Favorite Offer Removed Successfully"
msgstr "تم حذف العرض بنجاح"

#: deals/schema/mutations.py:360
msgid "Activate Task Offers"
msgstr ""

#: deals/schema/mutations.py:362
#, python-format
msgid ""
"المستفيد %(beneficiary_name)s طلب اعاده تفعيل عروض المهمة رقم %(task_id)s "
"لمده 48 ساعه اخري"
msgstr ""

#: deals/schema/object_types.py:222
#, python-format
msgid "%(label)s"
msgstr ""

#: deals/schema/query.py:369
msgid "you don't have any task"
msgstr ""

#: deals/schema/query.py:426
msgid "you must provide offer_id with pk"
msgstr ""

#: deals/scripts/upload_bulk_benefeciary.py:64
#: deals/tests/test_scripts/test_upload_bulk_beneficiary.py:37
msgid "File Is Required"
msgstr "الملف مطلوب"

#: deals/scripts/upload_bulk_benefeciary.py:67
#: deals/tests/test_scripts/test_upload_bulk_beneficiary.py:42
msgid "Invalid File Extension"
msgstr "امتداد الملف غير صالح"

#: deals/scripts/upload_bulk_benefeciary.py:73
#: deals/tests/test_scripts/test_upload_bulk_beneficiary.py:56
msgid "Missing Columns"
msgstr "أعمدة مفقودة"

#: deals/scripts/upload_bulk_benefeciary.py:107
msgid "Duplicated Beneficiary Data"
msgstr "بيانات مستفيد مكررة"

#: deals/scripts/upload_bulk_benefeciary.py:115
msgid "Beneficiary Data Updated"
msgstr "تم تحديث بيانات المستفيد"

#: deals/scripts/upload_bulk_benefeciary.py:290
#: deals/tests/test_scripts/test_upload_bulk_beneficiary.py:69
#: deals/tests/test_scripts/test_upload_bulk_beneficiary.py:91
#, python-format
msgid "%(created_records)s Beneficiary Created"
msgstr "تم إنشاء %(created_records)s مستفيد"

#: deals/scripts/upload_bulk_benefeciary.py:292
#: deals/tests/test_scripts/test_upload_bulk_beneficiary.py:70
#: deals/tests/test_scripts/test_upload_bulk_beneficiary.py:92
#, python-format
msgid "%(updated_beneficiaries)s Beneficiary Updated"
msgstr "تم تحديث %(updated_beneficiaries)s مستفيد"

#: deals/scripts/upload_bulk_benefeciary.py:294
#: deals/tests/test_scripts/test_upload_bulk_beneficiary.py:72
#: deals/tests/test_scripts/test_upload_bulk_beneficiary.py:94
#, python-format
msgid "%(error_count)s Error"
msgstr "%(error_count)s خطأ"

#: deals/scripts/upload_bulk_benefeciary.py:300
#: deals/tests/test_scripts/test_upload_bulk_beneficiary.py:116
msgid "Error With Script"
msgstr "خطأ في البرنامج النصي"

#: deals/serializers/agreement_serializer.py:24
#, python-format
msgid "Deal is %(status)s, can't be Finished"
msgstr "حاله الصفقه %(status)s، لا يمكن أتمامها"

#: deals/serializers/agreement_serializer.py:41
#: deals/serializers/deal_serializer.py:53
#, python-format
msgid "Deal is %(status)s, can't be Canceled"
msgstr "حاله الصفقه %(status)s، لا يمكن الغائها"

#: deals/strategies/agreements.py:148 deals/strategies/deals.py:284
msgid "Fulfill Form Data of Deal first"
msgstr "أكمل بيانات نموذج الصفقة أولاً"

#: deals/strategies/agreements.py:152 deals/strategies/deals.py:288
msgid "remove drafts first before finish deal"
msgstr "قم بإزالة المسودات أولاً قبل إنهاء الصفقة"

#: deals/strategies/deals.py:169
#, fuzzy
#| msgid "Beneficiary Data Updated"
msgid "Beneficiary Selected an Offer"
msgstr "تم تحديث بيانات المستفيد"

#: deals/strategies/deals.py:171
#, python-format
msgid ""
"المستفيد %(beneficiary_name)s اختار العرض %(favorite_offer_id)s للمهمة رقم "
"%(task_id)s."
msgstr ""

#: deals/strategies/offers.py:358
msgid "uncompleted task form data"
msgstr ""

#: deals/strategies/offers.py:546
#, fuzzy
#| msgid "District is Required"
msgid "This field is required."
msgstr "الحي مطلوب"

#: deals/strategies/request.py:48
msgid "Beneficiary Rejected Offer/s"
msgstr ""

#: deals/strategies/request.py:50
#, python-format
msgid "المستفيد %(beneficiary_name)s طلب عروض جديدة للمهمة رقم %(task_id)s."
msgstr ""

#: deals/tests/test_schema/test_mutations/test_create_deal_mutation.py:84
#: deals/tests/test_schema/test_queries/test_beneficiaries.py:65
#: deals/tests/test_schema/test_queries/test_customer_service.py:45
#: deals/tests/test_schema/test_queries/test_deal.py:108
#: deals/tests/test_schema/test_queries/test_favorite_offer.py:86
#: deals/tests/test_schema/test_queries/test_negotiator.py:45
msgid "Forbidden"
msgstr ""

#: deals/tests/test_schema/test_mutations/test_create_deal_mutation.py:154
#: deals/validators/deal.py:105
#, fuzzy, python-format
#| msgid "Favorite Offer %(favorite_offer_id)s not found"
msgid "Favorite offer %(favorite_offer_id)s already have deal"
msgstr "لم يتم العثور على العرض المفضل %(favorite_offer_id)s"

#: deals/tests/test_schema/test_mutations/test_create_deal_mutation.py:179
#, fuzzy, python-brace-format
#| msgid "Key '%(key)s' is missing or empty in task %(task_id)s form data"
msgid "Key 'placeOrder' is missing or empty in task {self.task.id} form data"
msgstr "المفتاح '%(key)s' مفقود أو فارغ في مهمة %(task_id)s بيانات النموذج"

#: deals/tests/test_schema/test_mutations/test_create_favorite_offer.py:129
#: deals/validators/offer.py:84
#, python-format
msgid "task already have %(task_favorite_offers_count)s offers"
msgstr ""

#: deals/tests/test_schema/test_queries/test_beneficiaries.py:57
#: deals/tests/test_schema/test_queries/test_beneficiary_filter.py:30
#: deals/tests/test_schema/test_queries/test_city.py:43
#: deals/tests/test_schema/test_queries/test_customer_service.py:37
#: deals/tests/test_schema/test_queries/test_deal.py:101
#: deals/tests/test_schema/test_queries/test_district.py:41
#: deals/tests/test_schema/test_queries/test_favorite_offer.py:79
#: deals/tests/test_schema/test_queries/test_layer.py:41
#: deals/tests/test_schema/test_queries/test_negotiator.py:37
#: deals/tests/test_schema/test_queries/test_region.py:43
#: deals/tests/test_schema/test_queries/test_zone.py:47
#: orders/tests/test_schema/test_mutations.py:127
#: orders/tests/test_schema/test_mutations.py:134
#: orders/tests/test_schema/test_mutations.py:315
#: orders/tests/test_schema/test_mutations.py:340
#: orders/tests/test_schema/test_mutations.py:494
#: orders/tests/test_schema/test_mutations.py:519
#: orders/tests/test_schema/test_mutations.py:684
#: orders/tests/test_schema/test_mutations.py:709
#: orders/tests/test_schema/test_mutations.py:839
#: orders/tests/test_schema/test_mutations.py:879
#: orders/tests/test_schema/test_mutations.py:1013
#: orders/tests/test_schema/test_mutations.py:1038
#: orders/tests/test_schema/test_mutations.py:1169
#: orders/tests/test_schema/test_mutations.py:1194
#: orders/tests/test_schema/test_mutations.py:1299
#: orders/tests/test_schema/test_mutations.py:1309
#: orders/tests/test_schema/test_mutations.py:1453
#: orders/tests/test_schema/test_mutations.py:1463
#: orders/tests/test_schema/test_mutations.py:1621
#: orders/tests/test_schema/test_mutations.py:1631
#: orders/tests/test_schema/test_queries.py:107
#: orders/tests/test_schema/test_queries.py:115
#: orders/tests/test_schema/test_queries.py:116
#: orders/tests/test_schema/test_queries.py:248
#: orders/tests/test_schema/test_queries.py:393
#: orders/tests/test_schema/test_queries.py:428
#: orders/tests/test_schema/test_queries.py:440
#: orders/tests/test_schema/test_queries.py:616
#: orders/tests/test_schema/test_queries.py:640
msgid "Unauthorized"
msgstr ""

#: deals/tests/test_schema/test_queries/test_deal.py:166
#: deals/tests/test_schema/test_queries/test_favorite_offer.py:115
#: deals/tests/utils.py:122 orders/tests/test_schema/test_mutations.py:151
#: orders/tests/test_schema/test_mutations.py:326
#: orders/tests/test_schema/test_mutations.py:355
#: orders/tests/test_schema/test_mutations.py:505
#: orders/tests/test_schema/test_mutations.py:534
#: orders/tests/test_schema/test_mutations.py:695
#: orders/tests/test_schema/test_mutations.py:850
#: orders/tests/test_schema/test_mutations.py:865
#: orders/tests/test_schema/test_mutations.py:1024
#: orders/tests/test_schema/test_mutations.py:1055
#: orders/tests/test_schema/test_mutations.py:1180
#: orders/tests/test_schema/test_mutations.py:1324
#: orders/tests/test_schema/test_mutations.py:1339
#: orders/tests/test_schema/test_mutations.py:1478
#: orders/tests/test_schema/test_mutations.py:1493
#: orders/tests/test_schema/test_mutations.py:1646
#: orders/tests/test_schema/test_mutations.py:1714
#: orders/tests/test_schema/test_queries.py:413
#: orders/tests/test_schema/test_queries.py:625
msgid "Not Found"
msgstr "غير موجود"

#: deals/tests/test_schema/test_queries/test_deal.py:170
#: deals/tests/test_schema/test_queries/test_favorite_offer.py:119
#: deals/validators/task.py:13
#, python-format
msgid "Task %(task)s does not exist"
msgstr "المهمة %(task)s غير موجودة"

#: deals/tests/utils.py:126 deals/validators/layer.py:14
msgid "Layer matching query does not exist"
msgstr "الطبقة المطابقة للاستعلام غير موجودة"

#: deals/validators/agreement.py:37
msgid "The agreement is already being processed."
msgstr ""

#: deals/validators/agreement.py:76
#, fuzzy
#| msgid "Deal %(deal_id)s not found"
msgid "Deal not found"
msgstr "لم يتم العثور على الصفقة %(deal_id)s"

#: deals/validators/deal.py:32
#, python-format
msgid "please submit all task %(task_id)s forms first"
msgstr ""

#: deals/validators/deal.py:52 deals/validators/request.py:32
#, fuzzy, python-format
#| msgid "Task %(task_id)s is already Finished, can't be Updated"
msgid "task %(task_id)s doesn't have any request, can't create request"
msgstr "تم إنهاء المهمة %(task_id)s بالفعل، لا يمكن تحديثها"

#: deals/validators/deal.py:60
#, fuzzy, python-format
#| msgid "Task %(task_id)s is already Finished, can't be Updated"
msgid "task %(task_id)s already has request, can't create deal"
msgstr "تم إنهاء المهمة %(task_id)s بالفعل، لا يمكن تحديثها"

#: deals/validators/deal.py:71
#, python-format
msgid "Deal for task %(task_id)s already exists"
msgstr ""

#: deals/validators/deal.py:86
#, python-format
msgid "Favorite Offer %(favorite_offer_id)s not found"
msgstr "لم يتم العثور على العرض المفضل %(favorite_offer_id)s"

#: deals/validators/deal.py:94
msgid "offer is not reserved by beneficiary"
msgstr ""

#: deals/validators/deal.py:127 deals/validators/offer.py:51
#: deals/validators/offer.py:131
#, fuzzy
#| msgid "Layer matching query does not exist"
msgid "offer matching query doesn't exist"
msgstr "الطبقة المطابقة للاستعلام غير موجودة"

#: deals/validators/deal.py:155
msgid "can't Finish Deal yet"
msgstr "لا يمكن إنهاء الصفقة بعد"

#: deals/validators/deal.py:156
msgid "can't cancel deal yet"
msgstr "لا يمكن إلغاء الصفقة بعد"

#: deals/validators/deal.py:169
#, python-format
msgid "Deal %(deal_id)s not found"
msgstr "لم يتم العثور على الصفقة %(deal_id)s"

#: deals/validators/deal.py:212
#, fuzzy, python-format
#| msgid "Favorite Offer %(favorite_offer_id)s not found"
msgid "favorite offer %(favorite_offer_id)s not found for this task"
msgstr "لم يتم العثور على العرض المفضل %(favorite_offer_id)s"

#: deals/validators/deal.py:226
#, fuzzy, python-format
#| msgid "Favorite Offer %(favorite_offer_id)s not found"
msgid "favorite offer %(favorite_offer_id)s already have deal"
msgstr "لم يتم العثور على العرض المفضل %(favorite_offer_id)s"

#: deals/validators/offer.py:35 orders/tests/test_schema/test_mutations.py:592
#: orders/validators/tasks.py:55 orders/validators/tasks.py:58
#, fuzzy
#| msgid "invalid Form Schema key"
msgid "invalid key"
msgstr "مفتاح نموذج غير صالح"

#: deals/validators/offer.py:42
#, fuzzy
#| msgid "invalid Action"
msgid "invalid offer location"
msgstr "إجراء غير صالح"

#: deals/validators/offer.py:60
#, fuzzy, python-format
#| msgid "Favorite Offer %(favorite_offer_id)s not found"
msgid "Offer %(offer_id)s already have deal"
msgstr "لم يتم العثور على العرض المفضل %(favorite_offer_id)s"

#: deals/validators/offer.py:90
#, fuzzy
#| msgid "Task is already Finished, can't add Offer"
msgid "task already have this offer"
msgstr "تم إنهاء المهمة بالفعل، لا يمكن إضافة العرض"

#: deals/validators/offer.py:97
#, fuzzy
#| msgid "task is already finished, can't be updated"
msgid "offer already have deal"
msgstr "تم إنهاء المهمة بالفعل، لا يمكن تحديثها"

#: deals/validators/offer.py:138
#, fuzzy, python-format
#| msgid "Task is already Finished, can't be Updated"
msgid "Task %(task_id)s already have deal, can't be deleted"
msgstr "تم إنهاء المهمة بالفعل، لا يمكن تحديثها"

#: deals/validators/offer.py:168
msgid "task has no favorite offer, please add some offers"
msgstr ""

#: deals/validators/offer.py:181
#, fuzzy
#| msgid "Favorite Offer %(favorite_offer_id)s not found"
msgid "can't share offers that already have deal"
msgstr "لم يتم العثور على العرض المفضل %(favorite_offer_id)s"

#: deals/validators/offer.py:200
#, python-format
msgid "cant share these uncompleted offers %(difference)s"
msgstr ""

#: deals/validators/offer.py:221
msgid "beneficiary didn't response for your last request."
msgstr ""

#: deals/validators/offer.py:228
#, fuzzy
#| msgid "Task is already Finished, can't add Offer"
msgid "task already have deal, can't share offers again"
msgstr "تم إنهاء المهمة بالفعل، لا يمكن إضافة العرض"

#: deals/validators/request.py:41
#, fuzzy, python-format
#| msgid "Task %(task_id)s is already Finished, can't be Updated"
msgid "task %(task_id)s already has request, can't create new request"
msgstr "تم إنهاء المهمة %(task_id)s بالفعل، لا يمكن تحديثها"

#: deals/validators/request.py:52
msgid "please remove offer first before request new offer"
msgstr ""

#: deals/validators/request.py:79
#, python-format
msgid "task %(task_id)s doesn't have any request, can't activate request"
msgstr ""

#: deals/validators/request.py:89
#, fuzzy, python-format
#| msgid "Task %(task_id)s is already Finished, can't be Updated"
msgid "task %(task_id)s already has active request, can't create new request"
msgstr "تم إنهاء المهمة %(task_id)s بالفعل، لا يمكن تحديثها"

#: deals/validators/request.py:109
#, fuzzy
#| msgid "Task %(task_id)s is already Finished, can't be Updated"
msgid "you already have Pending request, please wait for request approval"
msgstr "تم إنهاء المهمة %(task_id)s بالفعل، لا يمكن تحديثها"

#: formschemas/apps.py:8 formschemas/models/form_schema.py:22
msgid "Form Schemas"
msgstr "مخططات النموذج"

#: formschemas/mixins/form_schema.py:48 formschemas/mixins/form_schema.py:67
#: orders/tests/test_schema/test_mutations.py:358
#: orders/tests/test_schema/test_mutations.py:537
#: orders/tests/test_schema/test_mutations.py:1058
msgid "invalid Form Schema key"
msgstr "مفتاح نموذج غير صالح"

#: formschemas/mixins/form_schema.py:55
msgid "only 1 form allowed"
msgstr "مسموح بنموذج واحد فقط"

#: formschemas/models/form_schema.py:16
msgid "JSON Schema"
msgstr "مخطط JSON"

#: formschemas/models/form_schema.py:17
msgid "Form and UI Schemas"
msgstr "مخططات النموذج وواجهة المستخدم"

#: formschemas/models/form_schema.py:21
msgid "Form Schema"
msgstr "مخطط النموذج"

#: notification_manager/mixins/notifications.py:16
msgid "You have been assigned a new task: {task_id}."
msgstr "تم أسناد مهمة جديدة أليك برقم :{task_id}."

#: notification_manager/mixins/notifications.py:17
msgid "You have been assigned {tasks_count} new tasks."
msgstr "تم أسناد {tasks_count} مهام جديدة اليك."

#: notification_manager/mixins/notifications.py:21
#: notification_manager/mixins/notifications.py:35
msgid "New Task Assigned"
msgstr "غير معين"

#: notification_manager/mixins/notifications.py:30
msgid "Task {task_id} reassigned to {customer_service_name} successfully."
msgstr "تمت اعاده اسناد المهمة {task_id} بنجاح ل {customer_service_name}"

#: notification_manager/mixins/notifications.py:31
msgid "{tasks_count} Tasks reassigned successfully to {customer_service_name}."
msgstr " تم اعاده اسناد عدد {tasks_count} مهام بنجاح"

#: notification_manager/schema/mutations.py:25
#: notification_manager/schema/mutations.py:44
msgid "This notification not exist"
msgstr ""

#: orders/apps.py:8
msgid "orders"
msgstr "الطلبات"

#: orders/mixins/tasks.py:17 orders/tests/test_schema/test_mutations.py:329
#: orders/tests/test_schema/test_mutations.py:508
#: orders/tests/test_schema/test_mutations.py:698
#: orders/tests/test_schema/test_mutations.py:1027
#: orders/tests/test_schema/test_mutations.py:1183
#: orders/tests/test_schema/test_mutations.py:1649
#: orders/tests/test_schema/test_queries.py:417
#: orders/tests/test_schema/test_queries.py:629
msgid "task matching query doesn't found"
msgstr "المهمة المطابقة للاستعلام غير موجودة"

#: orders/mixins/tasks.py:29 orders/tests/test_schema/test_mutations.py:853
#: orders/tests/test_schema/test_mutations.py:1342
#: orders/tests/test_schema/test_mutations.py:1496
#, python-format
msgid "invalid %(task_ids)s task ids"
msgstr "معرف المهمة %(task_ids)s غير صالح"

#: orders/mixins/tasks.py:43 orders/tests/test_schema/test_mutations.py:1393
#: orders/tests/test_schema/test_mutations.py:1548
#, python-format
msgid "Tasks with ids %(ids)s, can't be reassigned"
msgstr "حاله المهمهات %(ids)s، لا يمكن تعديلها"

#: orders/mixins/tasks.py:53 orders/tests/test_schema/test_mutations.py:1112
#: orders/tests/test_schema/test_mutations.py:1225
#, python-format
msgid "Task is %(status)s, can't be deleted or rejected"
msgstr "حاله المهمه %(status)s، لا يمكن الغائها أو رفضها"

#: orders/mixins/tasks.py:67 orders/tests/test_schema/test_mutations.py:1130
#: orders/tests/test_schema/test_mutations.py:1243
msgid "this task has ongoing deal"
msgstr "هذه المهمة تحتوي على صفقة جارية"

#: orders/mixins/tasks.py:90
msgid "Submit Form Data and remove Drafts"
msgstr "تقديم بيانات النموذج وإزالة المسودات"

#: orders/mixins/tasks.py:98 orders/tests/test_schema/test_mutations.py:778
#: orders/tests/test_schema/test_mutations.py:952
#, python-format
msgid "Key '%(key)s' is missing or empty in task %(task_id)s form data"
msgstr "المفتاح '%(key)s' مفقود أو فارغ في مهمة %(task_id)s بيانات النموذج"

#: orders/models/notes.py:7
msgid "Note"
msgstr ""

#: orders/models/order.py:14
msgid "Start Date"
msgstr "تاريخ البدء"

#: orders/models/order.py:15
msgid "End Date"
msgstr "تاريخ الانتهاء"

#: orders/models/order.py:19 orders/models/task.py:56
msgid "Order"
msgstr "طلب"

#: orders/models/order.py:20
msgid "Orders"
msgstr "الطلبات"

#: orders/models/task.py:18 orders/models/task.py:38
msgid "Looking For Property"
msgstr ""

#: orders/models/task.py:19
msgid "Real State Financing"
msgstr ""

#: orders/models/task.py:23
msgid "Not Assigned"
msgstr "غير معين"

#: orders/models/task.py:24
msgid "Assigned To Customer Service"
msgstr "تم تعيينه لخدمة العملاء"

#: orders/models/task.py:25
msgid "Assigned To Negotiator"
msgstr "تم تعيينه للمفاوض"

#: orders/models/task.py:26
msgid "Assigned To Project Manager"
msgstr "تم تعيينه لمدير المشروع"

#: orders/models/task.py:32
msgid "Pending Review"
msgstr "قيد المراجعة"

#: orders/models/task.py:33
msgid "Finished"
msgstr "تم الانتهاء"

#: orders/models/task.py:34
msgid "Checking Beneficiary Data"
msgstr "فحص بيانات المستفيد"

#: orders/models/task.py:35
msgid "Review Recommendations"
msgstr "مراجعة التوصيات"

#: orders/models/task.py:36
msgid "Review Offers"
msgstr "مراجعة العروض"

#: orders/models/task.py:37
msgid "Create Deal"
msgstr "إنشاء صفقة"

#: orders/models/task.py:39
#, fuzzy
#| msgid "Create Beneficiary"
msgid "Send Property To Beneficiary"
msgstr "إنشاء مستفيد"

#: orders/models/task.py:40
msgid "Property Inspection"
msgstr ""

#: orders/models/task.py:41
#, fuzzy
#| msgid "Negotiator"
msgid "Negotiate With Owner"
msgstr "مفاوض"

#: orders/models/task.py:42
msgid "Sharing Data With Client"
msgstr ""

#: orders/models/task.py:43
#, fuzzy
#| msgid "Create Beneficiary"
msgid "Offer Sent To Beneficiary"
msgstr "إنشاء مستفيد"

#: orders/models/task.py:44
msgid "Beneficiary Accept The Offer"
msgstr ""

#: orders/models/task.py:45
msgid "Offer Received"
msgstr ""

#: orders/models/task.py:46
msgid "Offer Agreement"
msgstr ""

#: orders/models/task.py:47
msgid "Second Level Preferences"
msgstr ""

#: orders/models/task.py:48
msgid "Identify Suitable Property"
msgstr ""

#: orders/models/task.py:63 users/admin/users.py:41
msgid "Customer Service"
msgstr "خدمة العملاء"

#: orders/models/task.py:71 users/admin/users.py:45 users/models.py:13
msgid "Negotiator"
msgstr "مفاوض"

#: orders/models/task.py:89
msgid "Assigned Status"
msgstr "حالة التعيين"

#: orders/models/task.py:96
#, fuzzy
#| msgid "Metadata"
msgid "Meta Data"
msgstr "بيانات وصفية"

#: orders/models/task.py:101
msgid "Tasks"
msgstr "المهام"

#: orders/schema/mutations.py:199
#: orders/tests/test_schema/test_mutations.py:1209
msgid "Task Deleted Successfully"
msgstr "تم حذف المهمة بنجاح"

#: orders/schema/mutations.py:283
#: orders/tests/test_schema/test_mutations.py:1717
#, python-format
msgid "invalid %(task_id)s task id"
msgstr "معرف المهمة %(task_id)s غير صالح"

#: orders/tests/test_schema/test_mutations.py:155
#: orders/validators/orders.py:21
msgid "invalid customer service id"
msgstr "معرف خدمة العملاء غير صالح"

#: orders/tests/test_schema/test_mutations.py:168
#: orders/tests/test_schema/test_mutations.py:386
#: orders/tests/test_schema/test_mutations.py:566
#: orders/tests/test_schema/test_mutations.py:589
#: orders/tests/test_schema/test_mutations.py:759
#: orders/tests/test_schema/test_mutations.py:774
#: orders/tests/test_schema/test_mutations.py:931
#: orders/tests/test_schema/test_mutations.py:948
#: orders/tests/test_schema/test_mutations.py:1108
#: orders/tests/test_schema/test_mutations.py:1127
#: orders/tests/test_schema/test_mutations.py:1221
#: orders/tests/test_schema/test_mutations.py:1240
#: orders/tests/test_schema/test_mutations.py:1389
#: orders/tests/test_schema/test_mutations.py:1544
#: orders/tests/test_schema/test_mutations.py:1562
#: orders/tests/test_schema/test_queries.py:454
#: orders/tests/test_schema/test_queries.py:467
#: orders/tests/test_schema/test_queries.py:482
#: orders/tests/test_schema/test_queries.py:521
#: orders/tests/test_schema/test_queries.py:535
msgid "Bad Request"
msgstr ""

#: orders/tests/test_schema/test_mutations.py:389
#: orders/tests/test_schema/test_mutations.py:569
#: orders/tests/test_schema/test_mutations.py:762
#: orders/tests/test_schema/test_mutations.py:934 orders/validators/tasks.py:43
#: orders/validators/tasks.py:264 orders/validators/tasks.py:291
#, python-format
msgid "Task is %(status)s, can't be updated"
msgstr "حاله المهمه %(status)s، لا يمكن تحديثها"

#: orders/tests/test_schema/test_mutations.py:868
#: orders/tests/test_schema/test_mutations.py:1327
#: orders/tests/test_schema/test_mutations.py:1481 users/mixins/users.py:14
#, python-format
msgid "User with id %(user_id)s not found"
msgstr "لم يتم العثور على المستخدم %(user_id)s"

#: orders/tests/test_schema/test_mutations.py:1567
#: orders/validators/tasks.py:359
#, python-format
msgid ""
"Tasks with ids %(ids)s have no negotiator, please assign to negotiator first"
msgstr "المهمام %(ids)s ليست مسندوه الي مفاوض, الرجاء اسنادها الي مفاوض اولا"

#: orders/tests/test_schema/test_queries.py:457 orders/validators/tasks.py:379
msgid "Please Submit Form Data First"
msgstr "يرجى تقديم بيانات النموذج أولاً"

#: orders/tests/test_schema/test_queries.py:470 orders/validators/tasks.py:386
msgid "please Submit first level"
msgstr "يرجى تقديم المستوي الاول"

#: orders/tests/test_schema/test_queries.py:485 orders/validators/tasks.py:395
msgid "please Submit second level"
msgstr "يرجى تقديم المستوي الثاني"

#: orders/tests/test_schema/test_queries.py:524 orders/validators/tasks.py:402
msgid "District is Required"
msgstr "الحي مطلوب"

#: orders/tests/test_utils/test_validator.py:127
#, python-format
msgid "District %(wrong_districts_ids)s Doesnt Relate to this Zone/City "
msgstr "الحي %(wrong_districts_ids)s لا يتعلق بهذه المنطقة/المدينة"

#: orders/validators/tasks.py:106
msgid "city Layer Not Found"
msgstr "طبقة المدينة غير موجودة"

#: orders/validators/tasks.py:109
msgid "region Layer Not Found"
msgstr "طبقة المنطقة غير موجودة"

#: orders/validators/tasks.py:114
msgid "districts Layer Not Found"
msgstr "طبقة الأحياء غير موجودة"

#: orders/validators/tasks.py:123
#, python-format
msgid "Region %(region_id)s Not Found"
msgstr "المنطقة %(region_id)s غير موجودة"

#: orders/validators/tasks.py:135
#, python-format
msgid "City %(city_id)s Not Found"
msgstr "المدينة %(city_id)s غير موجودة"

#: orders/validators/tasks.py:166
#, python-format
msgid "District %(wrong_districts_ids)s Doesnt Relate to this Zone/City"
msgstr "الحي %(wrong_districts_ids)s لا يتعلق بهذه المنطقة/المدينة"

#: orders/validators/tasks.py:195
msgid "invalid input field"
msgstr "بعض الحقول مفقودة"

#: orders/validators/tasks.py:243
#, fuzzy
#| msgid "invalid Action"
msgid "invalid expected plan "
msgstr "إجراء غير صالح"

#: resource_management/admin/forms.py:34
msgid "invalid Action"
msgstr "إجراء غير صالح"

#: resource_management/admin/resource_management.py:29
msgid "choose only 1 script"
msgstr "اختر نصاً واحداً فقط"

#: resource_management/admin/resource_management.py:36
msgid "Script is Already Executed"
msgstr "تم تنفيذ النص بالفعل"

#: resource_management/apps.py:8
#: resource_management/models/resource_mangement.py:49
msgid "Resource Management"
msgstr "إدارة الموارد"

#: resource_management/models/resource_mangement.py:13
msgid "Create Beneficiary"
msgstr "إنشاء مستفيد"

#: resource_management/models/resource_mangement.py:24
msgid "Executed"
msgstr "تم التنفيذ"

#: resource_management/models/resource_mangement.py:25
msgid "Failed"
msgstr "فشل"

#: resource_management/models/resource_mangement.py:30
msgid "Action"
msgstr "إجراء"

#: resource_management/models/resource_mangement.py:44
msgid "Attachment"
msgstr "المرفق"

#: resource_management/models/resource_mangement.py:50
msgid "Resource Managements"
msgstr "إدارات الموارد"

#: templates/admin/base_site.html:5
msgid "Django site admin"
msgstr "إدارة موقع جانغو"

#: templates/admin/base_site.html:8
msgid "Django administration"
msgstr "إدارة جانغو"

#: users/admin/users.py:37
msgid "Project Manager"
msgstr "مدير المشاريع"

#: users/apps.py:7
msgid "Users"
msgstr "المستخدمون"

#: users/models.py:10
msgid "Admin"
msgstr "مسؤول"

#: users/models.py:11
msgid "Project Managers"
msgstr "مديرو المشاريع"

#: users/models.py:12
msgid "Customer Services"
msgstr "خدمات العملاء"

#: users/models.py:48
msgid "Role"
msgstr "الدور"

#: users/models.py:52
msgid "REN Role"
msgstr "صلاحية المفاوض العقاري"

# gabbro/acl/models
#: users/models.py:53
msgid "REN Roles"
msgstr "صلاحيات المفاوض العقاري"

#: users/models.py:60
msgid "ren roles"
msgstr ""

#: utils/geometry.py:74
msgid "Invalid geometry format"
msgstr ""

#: utils/geometry.py:79
msgid "Invalid geometry: Not a valid geometry object."
msgstr ""

#: utils/graphene/query.py:57
msgid "Value provided is not an integer"
msgstr "القيمة المقدمة ليست عددًا صحيحًا"

#: utils/graphene/query.py:73
#, python-format
msgid "Value must be bigger than %(min_value)s"
msgstr "يجب أن تكون القيمة أكبر من %(min_value)s"

#~ msgid ""
#~ "beneficiary %(beneficiary_name)s requested to reactivate offers task "
#~ "%(task_id)s for new SLA 48 hours"
#~ msgstr ""
#~ "المستفيد %(beneficiary_name)s طلب اعاده تفعيل عروض المهمة %(task_id)s  "
#~ "لمده 48 ساعه اخري"

#, python-format
#~ msgid "expected plan is %(task_expected_plan)s"
#~ msgstr "الخطه المتوقعة هيا %(task_expected_plan)s"

#~ msgid "there's a deal with an ongoing task that can't be overridden"
#~ msgstr "هناك صفقة بمهمة جارية لا يمكن تجاوزها"

#, python-format
#~ msgid "Offer %(favorite_offer_id)s not found"
#~ msgstr "لم يتم العثور على العرض %(favorite_offer_id)s"

#~ msgid "this offer can't be deleted, it's related to Deal"
#~ msgstr "لا يمكن حذف هذا العرض، فهو مرتبط بصفقة"

#, python-format
#~ msgid "Key '%(key)s' is missing or empty in form data"
#~ msgstr "المفتاح '%(key)s' مفقود أو فارغ في بيانات النموذج"

#~ msgid "Task is Rejected, can't add Offer"
#~ msgstr "تم رفض المهمة، لا يمكن إضافة العرض"

#~ msgid "Task is pending review, can't add Offer"
#~ msgstr "المهمة قيد المراجعة لا يمكن إضافة العرض"

#~ msgid "You Don't Have Permission to this Task"
#~ msgstr "ليس لديك إذن لهذه المهمة"

#, python-format
#~ msgid "invalid %(negotiator_id)s Negotiator id"
#~ msgstr "معرف المفاوض %(negotiator_id)s غير صالح"

#~ msgid "You don not have permission to list the orders"
#~ msgstr "ليس لديك إذن لقائمة الطلبات"

#, python-format
#~ msgid "Task %(task_id)s matching query does not exist"
#~ msgstr "المهمة %(task_id)s المطابقة للاستعلام غير موجودة"

#~ msgid "You Don't Have Permission to Check Preferences For this Task"
#~ msgstr "ليس لديك إذن لفحص التفضيلات لهذه المهمة"

#, python-format
#~ msgid "Task is %(status)s, can't be deleted"
#~ msgstr "حاله المهمه %(status)s، لا يمكن حذفها"

#, fuzzy
#~ msgid "You Don't Have Permission"
#~ msgstr "ليس لديك إذن لهذه المهمة"

#, fuzzy, python-format
#~ msgid "invalid %(user_id)s user id"
#~ msgstr "معرف المهمة %(task_id)s غير صالح"

#~ msgid "Deal is Canceled, can't be Updated"
#~ msgstr "تم إلغاء الصفقة، لا يمكن تحديثها"

#~ msgid "Deal is Finished can't be Updated"
#~ msgstr "تم إنهاء الصفقة، لا يمكن تحديثها"

#~ msgid "Deal is already Canceled"
#~ msgstr "تم إلغاء الصفقة بالفعل"

#~ msgid "task is pending review, can't be updated"
#~ msgstr "المهمة قيد المراجعة، لا يمكن تحديثها"

#~ msgid "Task is Rejected, can't be Updated"
#~ msgstr "تم رفض المهمة، لا يمكن تحديثها"

#~ msgid "Task is already Assigned to Project Manager"
#~ msgstr "تم تعيين المهمة بالفعل لمدير المشروع"

#, python-format
#~ msgid "Task %(task_id)s is Rejected, can't be Updated"
#~ msgstr "تم رفض المهمة %(task_id)s، لا يمكن تحديثها"

#, python-format
#~ msgid "Task %(task_id)s is in progress, can't be Updated"
#~ msgstr "المهمة %(task_id)s قيد التنفيذ، لا يمكن تحديثها"

#~ msgid "Task is already Rejected"
#~ msgstr "تم رفض المهمة بالفعل"

#~ msgid "Task is assigned to Project Manager, Can't be Rejected"
#~ msgstr "تم تعيين المهمة لمدير المشروع، لا يمكن رفضها"

#~ msgid "Task is Finished, Can't be Deleted"
#~ msgstr "تم إنهاء المهمة، لا يمكن حذفها"

# gabbro/acl/apps
#~ msgid "Access Control"
#~ msgstr "صلاحية التحكم"

# gabbro/acl/models
#~ msgid "Role Title"
#~ msgstr "عنوان صلاحيه التحكم"

# gabbro/acl/models
#~ msgid "Permissions"
#~ msgstr "الاذونات"

# gabbro/users/models
#~ msgid "Email"
#~ msgstr "الأيميل"
