import os  # noqa

from gabbro.settings import *  # noqa
from gabbro.settings import INSTALLED_APPS as GABBRO_INSTALLED_APPS, MIDDLEWARE

PROJECT_APPS = (
    # include gabbro for global gabbro locale messages
    "gabbro",
    # Access control list based on Django Guardian
    "gabbro.acl",
    # app to handle images and attachments
    "gabbro.uploads",
    # Common used models and utils apps
    "common",
    # Users
    "users",
    # Core Apps
    "notifications",
    "deals",
    "orders",
    "dashboard",
    "resource_management",
    "formschemas",
    "auditing",
)
INSTALLED_APPS = GABBRO_INSTALLED_APPS
INSTALLED_APPS += PROJECT_APPS

MIDDLEWARE = ["django.middleware.gzip.GZipMiddleware", *MIDDLEWARE]

INTERNAL_MODE = True if os.getenv("INTERNAL_MODE", "False") == "True" else False
NOTEBOOK_ARGUMENTS = [
    "--ip",
    "0.0.0.0",
    "--port",
    "8888",
    "--allow-root",
    "--no-browser",
]

# LOGGING
LOGGING_LOGGERS_DEFAULTS = {
    "propagate": True,
    "level": "DEBUG",
}
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "default_formatter": {
            "format": "%(asctime)s - [%(name)s] - %(message)s",
        },
    },
    "handlers": {
        "console": {
            "level": "DEBUG",
            "formatter": "default_formatter",
            "class": "logging.StreamHandler",
        },
    },
    "loggers": {
        "deals": {
            **LOGGING_LOGGERS_DEFAULTS,
            "handlers": ["console"],
        },
        "orders": {
            **LOGGING_LOGGERS_DEFAULTS,
            "handlers": ["console"],
        },
        "resource_management": {
            **LOGGING_LOGGERS_DEFAULTS,
            "handlers": ["console"],
        },
        "formschemas": {
            **LOGGING_LOGGERS_DEFAULTS,
            "handlers": ["console"],
        },
        "commands": {
            **LOGGING_LOGGERS_DEFAULTS,
            "handlers": ["console"],
        },
        "utils": {
            **LOGGING_LOGGERS_DEFAULTS,
            "handlers": ["console"],
        },
    },
}


DEAL_RESERVATION_LIMIT_DAYS = 2
OFFERS_INTERNAL_BASE_URL = os.getenv("OFFERS_INTERNAL_BASE_URL")
OFFERS_API_KEY = os.getenv("OFFERS_API_KEY")
OFFERS_RECORDS_LIMIT = os.getenv("OFFERS_RECORDS_LIMIT", 100)

OFFERS = {
    "INTERNAL_BASE_URL": OFFERS_INTERNAL_BASE_URL,
    "API_KEY": OFFERS_API_KEY,
    "RECORDS_LIMIT": OFFERS_RECORDS_LIMIT,
}
DJANGO_NOTIFICATIONS_CONFIG = {
    "SOFT_DELETE": True,
}

ASGI_APPLICATION = "app.asgi.application"
