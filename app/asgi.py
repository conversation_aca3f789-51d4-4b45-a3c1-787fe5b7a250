"""
ASGI config

It exposes the ASGI callable as a module-level variable named application.

For more information on this file, see
https://docs.djangoproject.com/en/3.1/howto/deployment/asgi/
"""

import os

from channels.routing import ProtocolTypeRouter, URLRouter
from channels.staticfiles import StaticFilesHandler
from django.core.asgi import get_asgi_application

from mcp_app.routing import websocket_urlpatterns

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "app.settings")
django_asgi_app = get_asgi_application()


application = ProtocolTypeRouter(
    {
        # Django's ASGI application to handle traditional HTTP requests
        "http": django_asgi_app,
        # WebSocket chat handler
        "websocket": URL<PERSON>outer(
            websocket_urlpatterns
        ),
    }
)

channel_routing = {"http.request": StaticFilesHandler()}
