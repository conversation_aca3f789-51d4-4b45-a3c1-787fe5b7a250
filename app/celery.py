from __future__ import absolute_import, unicode_literals

from django.conf import settings

import os

from celery import Celery

# set environment variable with Django settings location
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "app.settings")

# create new celery instance with name of the project which is 'app' in this case
app = Celery("ren_backend")

# namespace used to detect CELERY_*settings* in the django settings
# Ex: CELERY_BROKER_URL instead of BROKER_URL
namespace = "CELERY"

# configure celery app to get configuration from django settings
app.config_from_object(settings, namespace=namespace)

# allow celery to auto discover tasks in the django project
app.autodiscover_tasks()
