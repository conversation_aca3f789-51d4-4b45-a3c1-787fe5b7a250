import logging
import asyncio

from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView
import aiohttp
import json
from asgiref.sync import async_to_sync

logger = logging.getLogger(__name__)


class TestView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        return Response(dict(message="REN API GATEWAY : OK", ren="0.4"))
