import graphene

import dashboard.schema
import deals.schema
import formschemas.schema
import notification_manager.schema
import orders.schema
import resource_management.schema
import users.schema


class Query(
    orders.schema.Query,
    deals.schema.Query,
    formschemas.schema.Query,
    users.schema.Query,
    notification_manager.schema.Query,
    dashboard.schema.Query,
    resource_management.schema.Query,
    graphene.ObjectType,
):
    pass


class Mutation(
    orders.schema.Mutation,
    deals.schema.Mutation,
    notification_manager.schema.Mutation,
    graphene.ObjectType,
):
    pass


schema = graphene.Schema(
    query=Query,
    mutation=Mutation,
)
