{"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "properties": {"willingnessToContinueProgram": {"$ref": "#/definitions/willingnessToContinueProgram"}}, "definitions": {"contact": {"type": "object", "title": "التواصل", "required": ["expectedCallTime", "expectedCallDate", "callStatus"], "properties": {"callStatus": {"enum": ["تم الرد", "لم يتم الرد", "إعادة جدولة الاتصال"], "type": "string", "title": "حالة الاتصال"}, "expectedCallDate": {"type": "string", "title": "تاريخ الاتصال المتوقع", "format": "date"}, "expectedCallTime": {"type": "string", "title": "وقت الاتصال المتوقع", "format": "time"}}}, "request": {"type": "object", "title": "الطلب", "required": ["registrationDate"], "properties": {"registrationDate": {"type": "string", "title": "تاريخ التسجيل", "format": "date"}}}, "location": {"type": "object", "title": "الموقع", "required": ["region", "city"], "properties": {"city": {"enum": ["الدمام", "الخبر", "الرياض", "جدة", "مكة", "المدينة", "تبوك", "القصيم", "نجران"], "type": "string", "title": "المدينة"}, "region": {"enum": ["الشرقية", "الرياض", "مكة", "المدينة", "القصيم", "الجوف", "تبوك", "حائل", "نجران", "جازان", "عسير", "الباحة", "الحدود الشمالية"], "type": "string", "title": "المنطقة"}}}, "familyData": {"type": "object", "title": "بيانات الأسرة", "required": ["familyMembersCount"], "properties": {"familyMembersCount": {"type": "integer", "title": "<PERSON><PERSON><PERSON> افراد الاسرة"}}}, "personalData": {"type": "object", "title": "البيانات الشخصية", "required": ["name", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "properties": {"name": {"type": "string", "title": "الاسم"}, "ageHijri": {"type": "integer", "title": "الع<PERSON><PERSON> (هجري)"}, "mobileNumber": {"type": "string", "title": "رقم الجوال", "readOnly": true}}}, "financialData": {"type": "object", "title": "البيانات  الشخصية المالية", "required": ["salary", "housingAllowance", "monthlyDeduction", "remainingDurationMonths", "additionalIncome", "salaryBank", "funder", "fundingAmount", "entitlement"], "properties": {"funder": {"enum": ["البنك الأهلي السعودي", "مصرف الراجحي", "بنك الرياض", "بنك ساب", "البنك العربي الوطني", "مصرف الانماء", "البنك السعودي للاستثمار", "البنك السعودي الفرنسي", "بنك الجزيرة", "بنك البلاد", "بنك الخليج الدولي", "بنك الامارات", "بنك أبو ظبي الأول", "دار التمليك", "بداية", "أملاك", "شركة مسار النمو للتمويل", "سهل"], "type": "string", "title": "الممول"}, "salary": {"type": "number", "title": "الراتب"}, "salaryBank": {"enum": ["البنك الأهلي السعودي", "مصرف الراجحي", "بنك الرياض", "بنك ساب", "البنك العربي الوطني", "مصرف الانماء", "البنك السعودي للاستثمار", "البنك السعودي الفرنسي", "بنك الجزيرة", "بنك البلاد", "بنك الخليج الدولي", "بنك الامارات", "بنك أبو ظبي الأول"], "type": "string", "title": "بنك الراتب"}, "entitlement": {"enum": ["نعم", "لا"], "type": "string", "title": "الاستحقاق"}, "fundingAmount": {"type": "number", "title": "مب<PERSON>غ التمويل"}, "additionalIncome": {"type": "number", "title": "مداخيل اضافية"}, "housingAllowance": {"type": "number", "title": "بدل السكن"}, "monthlyDeduction": {"type": "number", "title": "الاستقطاع الشهري"}, "remainingDurationMonths": {"type": "number", "title": "المدة المتبقية (شهر)"}}}, "supportPackages": {"type": "object", "title": "باقات الدعم", "required": ["product"], "properties": {"product": {"enum": ["البناء الذاتي", "الوحدات الجاهزة", "البيع على الخارطة"], "type": "string", "title": "المنتج"}}}, "professionalData": {"type": "object", "title": "البيانات الشخصية المهنية", "required": ["job"], "properties": {"job": {"enum": ["حكومي مدني", "خاص", "عسكري", "<PERSON><PERSON><PERSON> حر", "متقا<PERSON>د", "مدني", "موظ<PERSON> بنك"], "type": "string", "title": "الوظيفة"}}}, "basicTransactionData": {"type": "object", "title": "البيانات الأساسية للمعاملة", "required": ["assignmentNumber", "customerServiceEmployeeName", "assignmentDate", "assignmentTime"], "properties": {"assignmentDate": {"type": "string", "title": "تاريخ الاسناد", "format": "date", "readOnly": true}, "assignmentTime": {"type": "string", "title": "وقت الاسناد", "format": "time", "readOnly": true}, "assignmentNumber": {"type": "string", "title": "رقم الاسناد", "readOnly": true}, "customerServiceEmployeeName": {"type": "string", "title": "اسم موظف خدمة العملاء", "readOnly": true}}}, "personalRealEstateData": {"type": "object", "title": "البيانات الشخصية العقارية", "required": ["hasLand", "hasBuildingLicense", "startedBuilding"], "properties": {"hasLand": {"type": "boolean", "title": "لديه ارض", "default": false}, "startedBuilding": {"type": "boolean", "title": "بد<PERSON> بالبناء", "default": false}, "hasBuildingLicense": {"type": "boolean", "title": "لديه رخصة بناء", "default": false}}}, "willingnessToContinueProgram": {"type": "object", "title": "استكمال البرنامج", "properties": {"willingnessToContinueProgram": {"enum": [true, false], "type": "boolean", "title": "هل يرغب المستفيد في استكمال البرنامج؟", "default": true, "enumNames": ["يرغب في استكمال البرنامج", "لا يرغب في استكمال البرنامج"]}}}}, "dependencies": {"willingnessToContinueProgram": {"oneOf": [{"properties": {"willingnessToContinueProgram": {"required": ["willingnessToContinueProgram"], "properties": {"willingnessToContinueProgram": {"const": false}}}}}, {"properties": {"contact": {"$ref": "#/definitions/contact"}, "request": {"$ref": "#/definitions/request"}, "location": {"$ref": "#/definitions/location"}, "familyData": {"$ref": "#/definitions/familyData"}, "personalData": {"$ref": "#/definitions/personalData"}, "financialData": {"$ref": "#/definitions/financialData"}, "supportPackages": {"$ref": "#/definitions/supportPackages"}, "professionalData": {"$ref": "#/definitions/professionalData"}, "basicTransactionData": {"$ref": "#/definitions/basicTransactionData"}, "personalRealEstateData": {"$ref": "#/definitions/personalRealEstateData"}, "willingnessToContinueProgram": {"required": ["willingnessToContinueProgram"], "properties": {"willingnessToContinueProgram": {"const": true}}}}}]}}}