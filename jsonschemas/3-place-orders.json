{"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "properties": {"communication": {"type": "object", "title": "التواصل", "required": ["expectedCallTime", "expectedCallDate", "callStatus"], "properties": {"callStatus": {"enum": ["تم الرد", "لم يتم الرد", "إعادة جدولة الاتصال"], "type": "string", "title": "حالة الاتصال"}, "expectedCallDate": {"type": "string", "title": "تاريخ الاتصال المتوقع", "format": "date"}, "expectedCallTime": {"type": "string", "title": "وقت الاتصال المتوقع", "format": "time"}}}, "contractSigning": {"type": "object", "title": "توقيع عقد اتفاق", "required": ["agreementFile", "agreementApproval", "signatureFile"], "properties": {"agreementFile": {"type": "string", "title": "مرفق اتفاقية عرض تفاصيل العروض العقارية", "format": "uri"}, "signatureFile": {"type": "string", "title": "ارفاق ملف التوقيع على الاتفاقية", "format": "uri"}, "agreementApproval": {"enum": ["نعم", "لا"], "type": "string", "title": "الموافقة على اتفاقية عرض تفاصيل العروض العقارية"}}}, "availabilityCheck": {"type": "object", "title": "التحقق من توفر العروض العقارية", "required": ["propertyAvailability", "modifyPreferencesLevelTwo", "modifyPreferencesLevelOne"], "properties": {"propertyAvailability": {"enum": ["متوفر", "<PERSON>ير متوفر"], "type": "string", "title": "توفر العروض العقارية"}, "modifyPreferencesLevelOne": {"enum": ["نعم", "لا"], "type": "string", "title": "هل يرغب بتعديل تفضيلات المستوى الأول"}, "modifyPreferencesLevelTwo": {"enum": ["نعم", "لا"], "type": "string", "title": "هل يرغب بتعديل تفضيلات المستوى الثاني"}}}, "locationPreferencesLevelTwo": {"type": "object", "title": "تحديد التفضيلات المكانية  المستوى الثاني", "required": ["preferredRegion", "preferredCity", "mainDivision", "preferredNeighborhood"], "properties": {"mainDivision": {"type": "string", "title": "التقسيم الرئيسي"}, "preferredCity": {"type": "string", "title": "المدينة المفضلة"}, "preferredRegion": {"type": "string", "title": "المنطقة المفضلة"}, "preferredNeighborhood": {"type": "string", "title": "الحي المفضل"}}}, "financialPreferencesLevelTwo": {"type": "object", "title": "تحديد التفضيلات المالية  المستوى الثاني", "required": ["minimumPurchaseAmount", "maximumPurchaseAmount", "preferredPurchaseAmount", "purchaseMechanism", "purchaseDesire"], "properties": {"purchaseDesire": {"type": "string", "title": "رغبة الشراء"}, "purchaseMechanism": {"type": "string", "title": "آلية الشراء"}, "maximumPurchaseAmount": {"type": "number", "title": "ال<PERSON><PERSON> الأع<PERSON>ى لمبلغ الشراء"}, "minimumPurchaseAmount": {"type": "number", "title": "الح<PERSON> الأ<PERSON>نى لمبلغ الشراء"}, "preferredPurchaseAmount": {"type": "number", "title": "مبلغ الشراء المفضل"}}}, "realEstatePreferencesLevelTwo": {"type": "object", "title": "تحديد التفضيلات العقارية المستوى الثاني", "required": ["preferredPropertyType"], "properties": {"preferredPropertyType": {"type": "string", "title": "نوع العقار المفضل"}}}}}