{"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "properties": {"propertyValuation": {"type": "object", "title": "تقييم العقار", "required": ["valuationAmount", "valuationDocument"], "properties": {"valuationAmount": {"type": "number", "title": "قيمة العقار حسب التقييم"}, "valuationDocument": {"type": "string", "title": "مرفق تقييم العقار", "format": "binary"}}}, "financierSelection": {"type": "object", "title": "اختيار الممول من قبل المستفيد", "required": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "financingAmount"], "properties": {"financingAmount": {"type": "number", "title": "مب<PERSON>غ التمويل"}, "selectedFinancier": {"enum": ["البنك الأهلي السعودي", "مصرف الراجحي", "بنك الرياض", "بنك ساب", "البنك العربي الوطني", "مصرف الانماء", "البنك السعودي للاستثمار", "البنك السعودي الفرنسي", "بنك الجزيرة", "بنك البلاد", "بنك الخليج الدولي", "بنك الامارات", "بنك أبو ظبي الأول", "دار التمليك", "بداية", "أملاك", "شركة مسار النمو للتمويل", "سهل"], "type": "string", "title": "الممول المحدد من قبل العميل"}}}, "deedReleaseCheckIssuance": {"type": "object", "title": "اصدار شيك الإفراغ", "required": ["deedReleaseCheckDetails"], "properties": {"deedReleaseCheckDetails": {"type": "string", "title": "تفاصيل شيك الإفراغ", "format": "binary"}}}, "financingCompletionProcedures": {"type": "object", "title": "إتمام إجراءات التمويل", "required": ["financingRequestReceived", "initialOfferSent", "finalOfferAgreedAndContractSigned"], "properties": {"initialOfferSent": {"enum": ["نعم", "لا"], "type": "string", "title": "هل تم ارسال العرض المبدئي"}, "financingRequestReceived": {"enum": ["نعم", "لا"], "type": "string", "title": "هل تم استلام طلب تمويل"}, "finalOfferAgreedAndContractSigned": {"enum": ["نعم", "لا"], "type": "string", "title": "هل تم الاتفاق على العرض النهائي وتوقيع العقد"}}}}}