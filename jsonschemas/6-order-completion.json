{"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "UiSchema": {"dealCompletion": {"dealCompleted": {"ui:widget": "YesNoWidget"}}, "deedReleaseCheckIssuance": {"deedReleaseCheckFile": {"ui:widget": "FileWidget"}, "deedReleaseCheckIssued": {"ui:widget": "YesNoWidget"}}}, "properties": {"closingDate": {"type": "object", "title": "تحديد موعد إتمام الصفقة", "required": ["completionDateTime"], "properties": {"completionDateTime": {"type": "string", "title": "موعد إتمام الصفقة", "format": "date-time"}}}, "dealCompletion": {"type": "object", "title": "إتمام الصفقة", "required": ["dealCompleted"], "properties": {"dealCompleted": {"enum": ["نعم", "لا"], "type": "string", "title": "هل تم إتمام الصفقة؟"}}}, "propertyTransfer": {"type": "object", "title": "افراغ العقار", "required": ["assignmentNumber", "notary<PERSON><PERSON>", "assignmentDate", "assignmentTime"], "properties": {"notaryName": {"type": "string", "title": "اسم الموثق العقاري"}, "assignmentDate": {"type": "string", "title": "تاريخ الاسناد", "format": "date", "readOnly": true}, "assignmentTime": {"type": "string", "title": "وقت الاسناد", "format": "time", "readOnly": true}, "assignmentNumber": {"type": "string", "title": "رقم الاسناد", "readOnly": true}}}, "deedReleaseCheckIssuance": {"type": "object", "title": "إصدار شيك الإفراغ", "required": ["deedReleaseCheckIssued", "deedReleaseCheckFile"], "properties": {"deedReleaseCheckFile": {"type": "string", "title": "ارفاق ملف شيك الافراغ", "format": "binary"}, "deedReleaseCheckIssued": {"enum": ["نعم", "لا"], "type": "string", "title": "هل تم اصدار شيك الإفراغ"}}}}}