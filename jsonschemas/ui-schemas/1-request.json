{"contact": {"ui:order": ["expectedCallTime", "expectedCallDate", "callStatus"], "callStatus": {"custom:gridProps": {"lg": 4}}, "expectedCallDate": {"custom:gridProps": {"lg": 4}}, "expectedCallTime": {"custom:gridProps": {"lg": 4}}}, "request": {"ui:order": ["registrationDate"]}, "location": {"city": {"custom:gridProps": {"lg": 6, "xs": 12}}, "region": {"custom:gridProps": {"lg": 6, "xs": 12}}, "ui:order": ["region", "city"]}, "ui:order": ["personalData", "personalRealEstateData", "request", "familyData", "professionalData", "location", "supportPackages", "financialData", "basicTransactionData", "contact", "willingnessToContinueProgram", "*"], "familyData": {"ui:order": ["familyMembersCount"]}, "personalData": {"name": {"custom:gridProps": {"lg": 4}}, "ageHijri": {"custom:gridProps": {"lg": 4}}, "ui:order": ["name", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobileNumber"], "mobileNumber": {"custom:gridProps": {"lg": 4}}}, "financialData": {"funder": {"custom:gridProps": {"lg": 4}}, "salary": {"custom:gridProps": {"lg": 4}}, "ui:order": ["salary", "housingAllowance", "monthlyDeduction", "remainingDurationMonths", "additionalIncome", "salaryBank", "funder", "fundingAmount", "entitlement"], "salaryBank": {"custom:gridProps": {"lg": 4}}, "entitlement": {"custom:gridProps": {"lg": 4}}, "fundingAmount": {"custom:gridProps": {"lg": 4}}, "additionalIncome": {"custom:gridProps": {"lg": 4}}, "housingAllowance": {"custom:gridProps": {"lg": 4}}, "monthlyDeduction": {"custom:gridProps": {"lg": 4}}, "remainingDurationMonths": {"custom:gridProps": {"lg": 4}}}, "supportPackages": {"ui:order": ["product"]}, "professionalData": {"ui:order": ["job"]}, "basicTransactionData": {"ui:order": ["assignmentNumber", "customerServiceEmployeeName", "assignmentDate", "assignmentTime"], "assignmentDate": {"custom:gridProps": {"lg": 4}}, "assignmentTime": {"custom:gridProps": {"lg": 4}}, "assignmentNumber": {"custom:gridProps": {"lg": 4}}, "customerServiceEmployeeName": {"custom:gridProps": {"lg": 4}}}, "personalRealEstateData": {"hasLand": {"custom:gridProps": {"lg": 4}}, "ui:order": ["hasLand", "hasBuildingLicense", "startedBuilding"], "startedBuilding": {"custom:gridProps": {"lg": 4}}, "hasBuildingLicense": {"custom:gridProps": {"lg": 4}}}, "willingnessToContinueProgram": {"willingnessToContinueProgram": {"ui:widget": "YesNoWidget"}}}