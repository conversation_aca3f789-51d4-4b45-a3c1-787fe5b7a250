{"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "properties": {"negotiationAgreement": {"type": "object", "title": "توقيع اتفاقية تفاوض", "required": ["negotiationAgreementFile", "agreementApproval", "signatureFile"], "properties": {"signatureFile": {"type": "string", "title": "ارفاق ملف التوقيع على الاتفاقية", "format": "binary"}, "agreementApproval": {"enum": ["نعم", "لا"], "type": "string", "title": "الموافقة على اتفاقية التفاوض"}, "negotiationAgreementFile": {"type": "string", "title": "مرفق اتفاقية التفاوض", "format": "binary"}}}, "propertyValueProposal": {"type": "object", "title": "اقتراح قيمة التفاوض", "required": ["averagePropertyPriceInNeighborhood", "averagePropertyPricePerMeter", "averagePropertyArea", "lastDealValueSimilarThisroperty"], "properties": {"averagePropertyArea": {"type": "number", "title": "متوسط مساحة العقار", "description": "قابل للتعديل"}, "averagePropertyPricePerMeter": {"type": "number", "title": "متوسط سعر متر العقار", "description": "قابل للتعديل"}, "lastDealValueSimilarProperty": {"type": "number", "title": "قيمة آخر صفقة تمت في الحي لعقار مماثل", "description": "قابل للتعديل"}, "averagePropertyPriceInNeighborhood": {"type": "number", "title": "متوسط سعر العقار في الحي", "description": "قابل للتعديل"}}}, "commitmentContractSigning": {"type": "object", "title": "توقيع عقد التزام بالشراء", "required": ["commitmentContractFile", "commitmentContractApproval", "commitmentSignatureFile"], "properties": {"commitmentContractFile": {"type": "string", "title": "عقد التزام بالشراء", "format": "binary"}, "commitmentSignatureFile": {"type": "string", "title": "ارفاق ملف التوقيع على الاتفاقية", "format": "binary"}, "commitmentContractApproval": {"enum": ["نعم", "لا"], "type": "string", "title": "الموافقة على عقد الالتزام بالشراء"}}}, "negotiationRequestSubmission": {"type": "object", "title": "ارسال طلب تفاوض", "required": ["assignmentNumber", "assignmentDate", "assignmentTime", "definedNegotiationValue", "negotiationStatus"], "properties": {"assignmentDate": {"type": "string", "title": "تاريخ الاسناد", "format": "date", "readOnly": true}, "assignmentTime": {"type": "string", "title": "وقت الاسناد", "format": "time", "readOnly": true}, "assignmentNumber": {"type": "string", "title": "رقم الاسناد", "readOnly": true}, "negotiationStatus": {"enum": ["تم قبول التفاوض", "تم رفض التفاوض", "تم ارسال طلب تفاوض جديد"], "type": "string", "title": "حالة التفاوض"}, "definedNegotiationValue": {"type": "number", "title": "قيمة التفاوض المحددة", "description": "قابل للتعديل"}}}, "negotiationValueDetermination": {"type": "object", "title": "تحديد قيمة التفاوض مع المستفيد", "required": ["proposedNegotiationValue"], "properties": {"proposedNegotiationValue": {"type": "number", "title": "قيمة التفاوض المقترحة"}}}}}